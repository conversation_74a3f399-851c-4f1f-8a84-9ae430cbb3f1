<?php

namespace App\Services;

use Arr;
use Exception;

class GenPdfResumeService
{
    /**
     * @param $data
     *
     * @return string
     * @throws Exception
     */
    public function genPdf($data): string
    {
        $response = \Http::post(config('services.genpdf.api_url'), $this->parseData($data));
        if ($response->clientError()) {
            throw new Exception('Errors Convert Preview CV');
        }

        return $response->body();
    }

    public function parseData($data): array
    {
        $resume = [
            'name_resume' => Arr::get($data, 'fullname'),
            'lang' => 'en',
            'template_name' => 'template1',
            'experience_type' => 0,
            'meta' => [
                'name' => Arr::get($data, 'fullname'),
                'lang' => 'en',
                'theme' => 'template1',
                'color' => null,
            ],
            'summary' => [
                [
                    'summary' => Arr::get($data, 'summary'),
                ],
            ],
            'personal' => [
                [
                    'name' => Arr::get($data, 'fullname'),
                    'email' => Arr::get($data, 'email'),
                    'phone' => Arr::get($data, 'phone'),
                    'gender' => Arr::get($data, 'gender'),
                    'images' => Arr::get($data, 'avatar_url', ''),
                    'address' => Arr::get($data, 'address'),
                    'profession' => Arr::get($data, 'position'),
                    'dateofbirth' => Arr::get($data, 'birthday'),
                    'extra_skills' => null,
                    'provinces_id' => Arr::get($data, 'province_code', ''),
                    'province_name' => Arr::get($data, 'province_name', ''),
                ],
            ],
            'interests' => [
                [
                    'interest' => Arr::get($data, 'interests'),
                ],
            ],
            'activities' => Arr::get($data, 'activities'),
            'educations' => Arr::get($data, 'educations'),
            'experiences' => Arr::get($data, 'experiences'),
            'skill_groups' => [
                [
                    'skills' => $this->mapSkills('technical', Arr::get($data, 'skills.technical_skills', [])),
                    'group_name' => 'Technical Skill',
                ],
                [
                    'skills' =>  $this->mapSkills('soft', Arr::get($data, 'skills.soft_skills')),
                    'group_name' => 'Soft skills',
                ],
            ],
            'languages' => Arr::get($data, 'languages', []),
            'additionals' => Arr::get($data, 'additionals', []),
            'projects' => Arr::get($data, 'projects', []),
            'certificates' => Arr::get($data, 'certificates', []),
            'references' => Arr::get($data, 'references'),
            'sectionsOrder' => [],
            'sections' => [
                [
                    'key' => 'personal',
                    'status' => 'active',
                    'order' => 1,
                ],
                [
                    'key' => 'summary',
                    'status' => 'active',
                    'order' => 2,
                ],
                [
                    'key' => 'experiences',
                    'status' => 'active',
                    'order' => 3,
                ],
                [
                    'key' => 'skill_groups',
                    'status' => 'active',
                    'order' => 4,
                ],
                [
                    'key' => 'educations',
                    'status' => 'active',
                    'order' => 5,
                ],
                [
                    'key' => 'languages',
                    'status' => !empty(Arr::get($data, 'languages')) ? 'active' : 'inactive',
                    'order' => 6,
                ],
                [
                    'key' => 'projects',
                    'status' => !empty(Arr::get($data, 'projects')) ? 'active' : 'inactive',
                    'order' => 7,
                ],
                [
                    'key' => 'interests',
                    'status' => !empty(Arr::get($data, 'interests')) ? 'active' : 'inactive',
                    'order' => 8,
                ],
                [
                    'key' => 'references',
                    'status' => !empty(Arr::get($data, 'references')) ? 'active' : 'inactive',
                    'order' => 9,
                ],
                [
                    'key' => 'activities',
                    'status' => !empty(Arr::get($data, 'activities')) ? 'active' : 'inactive',
                    'order' => 10,
                ],
                [
                    'key' => 'certificates',
                    'status' => !empty(Arr::get($data, 'certificates')) ? 'active' : 'inactive',
                    'order' => 11,
                ],
                [
                    'key' => 'additionals',
                    'status' => !empty(Arr::get($data, 'additionals')) ? 'active' : 'inactive',
                    'order' => 12,
                ],
            ],
        ];

        return ['resume' => $resume];
    }

    public function mapSkills($type, $skills): array
    {
        if (empty($skills)) {
            return [];
        }
        $skillArr = [];

        switch ($type) {
            case 'technical':
                foreach ($skills as $skill) {
                    $skillArr[] = [
                        'skill_id' => Arr::get($skill, 'skill_id'),
                        'skill' => Arr::get($skill, 'skill_name'),
                    ];
                }
                break;
            case 'soft':
                foreach ($skills as $skill) {
                    $skillArr[] = [
                        'skill_id' => $skill,
                        'skill' => $skill,
                    ];
                }
                break;
        }

        return $skillArr;
    }
}
