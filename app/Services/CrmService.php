<?php

namespace App\Services;

use App\Http\Resources\Crm\CrmInvoiceResouce;
use Exception;
use Illuminate\Support\Facades\Http;

class CrmService
{
    /**
     * Make request to CRM system via api
     * @param string $uri uri for crm api
     * @param array $params request params based on method
     * @param string $method http method for request, default is GET
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function request($uri, $params = [], $method = 'GET')
    {
        $http = Http::baseUrl(config('services.crm.base_url'))
            ->accept('application/json')
            ->timeout(config('services.crm.timeout'))
            ->withHeaders([
                'api-auth-code' => config('services.crm.auth_code')
            ])
            ->when(config('app.env') !== 'production', fn($http) => $http->withoutVerifying());

        $apiUri = '/api/v3/' . ltrim($uri, "/");

        if ('GET' === $method) {
            return $http->get($apiUri, $params)->json();
        }

        return $http->post($apiUri, $params)->json();
    }

    public function getInvoiceByCompany($companyId, $ranges = [], $page = 1, $size = 5, $orders = [])
    {
        try {
            $params = array_filter([
                'page' => $page,
                'size' => $size,
                'orders' => $orders,
                'from_date' => $ranges[0] ?? null,
                'to_date' => $ranges[0] ?? null,
            ]);
            $response = $this->request('/invoices/get_invoices/' . $companyId, $params);
            return CrmInvoiceResouce::make($response);
        } catch (Exception $ex) {
            app('sentry')->captureException($ex);
            return CrmInvoiceResouce::make([]);
        }
    }

    /**
     * Check check available packages every time employer create a new job request
     *
     * @param mixed $companyId
     * @return bool
     */
    public function hasAvailablePackages($companyId)
    {
        try {
            $response = $this->request('/invoices/check_has_available_packages/' . $companyId);
            return $response['data'] && $response['data'] === true;
        } catch (Exception $ex) {
            app('sentry')->captureException($ex);
            return false;
        }
    }

    /**
     * Check check available packages every time employer create a new job request
     *
     * @param mixed $companyId
     * @return bool
     */
    public function getAvailablePackages($companyId, $package_id = null)
    {
        try {
            $response = $this->request('/invoices/get_available_packages/' . $companyId, [
                'package_id' => $package_id
            ]);
            return $response['data'] ?? [];
        } catch (Exception $ex) {
            app('sentry')->captureException($ex);
            return [];
        }
    }
}
