<?php

namespace App\Services;

use App\Enums\JobLevelEnum;
use App\Helpers\CrmApi;
use App\Models\Job;
use App\Models\JobNote;
use App\Models\Taxonomy;
use App\Models\Term;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\QueryException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;

class JobService
{
    public function update(Job $job, $data)
    {
        $isPaidJob = Arr::get($data, 'level') == JobLevelEnum::PAID->value;

        // Take values from the package id fields
        if ($isPaidJob) {
            [$packageId, $invoiceId] = Arr::get($data, 'package_id');
        }

        $job->update(
            collect($data)
            ->only('title', 'content', 'emails_cc', 'education_certificate', 'company_tagline', 'level')
            ->merge([
                'uuid' => Str::uuid(),
                'creator_id' => Auth::id(),
                'refreshed_at' => now(),
                'crm_invoice_id' => $invoiceId ?? null,
            ])
            ->all()
        );

        // New job information
        $job->information()->updateOrCreate(
            [],
            collect($data)->only('salary', 'benefits')->all()
        );

        // New job note
        $job->note()->updateOrCreate(
            [],
            collect($data, 'recruiment_process')
                ->merge(['employer_notes' => Arr::get($data, 'note')])
                ->all()
        );

        // Addresses
        $job->addresses()->sync(
            Arr::get($data, 'addresses_id')
        );

        // Job's categories
        $job->job_categories()->sync(
            array_merge(
                is_array(Arr::get($data, 'category_id')) ? Arr::get($data, 'category_id') : [Arr::get($data, 'category_id')],
                Arr::get($data, 'job_category_id', [])
            )
        );

        // You know :))
        $job->taxonomies()->detach();

        $job->taxonomies()->attach(
            collect($data)
                ->only([
                    'experiences_ids',
                    'skills_ids',
                    'job_levels',
                    'job_types',
                    'contract_type',
                    'education_major',
                    'job_template_color',
                    'job_banner',
                    'job_template',
                    'education',
                    'education_degree',
                    'education_major',
                ])
                ->flatten()
                ->unique()
                ->filter()
                ->all()
        );

        // Save taxonomies with custom properties
        // Handle items that can have multiple entries with same ID (like "Other" items)
        collect($data)
            ->only('responsibilities', 'requirements', 'recruiment_process', 'benefits')
            ->each(function ($items) use ($job) {
                // Group items by ID to handle duplicates properly
                $groupedItems = collect($items)->groupBy('id');

                foreach ($groupedItems as $taxonomyId => $itemsWithSameId) {
                    // If there's only one item with this ID, use normal attach
                    if ($itemsWithSameId->count() === 1) {
                        try {
                            $job->taxonomies()->attach(
                                $taxonomyId,
                                [
                                    'custom_properties' => $itemsWithSameId->first(),
                                ]
                            );
                        } catch (QueryException $e) {
                            if ($e->getCode() != 23000) {
                                throw $e;
                            }
                        }
                    } else {
                        // For multiple items with same ID, create a new term and taxonomy for each item
                        // Get the original taxonomy to determine its type
                        $originalTaxonomy = Taxonomy::with('term')->find($taxonomyId);

                        if ($originalTaxonomy) {
                            collect($itemsWithSameId)->each(function ($item) use ($job, $originalTaxonomy) {
                                try {
                                    $name = ($item['name'] ?? $originalTaxonomy->term->name);
                                    $newTerm = Term::create([
                                        'name' => $name,
                                        'description' => $item['description'] ?? '',
                                        'slug' => Str::slug($name),
                                    ]);
                                    $newTerm->translates()->create([
                                        'name' => $name,
                                        'locale' => app()->getLocale(),
                                    ]);

                                    // Create a new taxonomy that references the new term
                                    $newTaxonomy = Taxonomy::create([
                                        'term_id' => $newTerm->id,
                                        'taxonomy' => $originalTaxonomy->taxonomy,
                                        'parent' => $originalTaxonomy->parent,
                                    ]);

                                    // Attach the job to the new taxonomy
                                    $job->taxonomies()->attach(
                                        $newTaxonomy->id,
                                        [
                                            'custom_properties' => $item,
                                        ]
                                    );
                                } catch (QueryException $e) {
                                    if ($e->getCode() != 23000) {
                                        throw $e;
                                    }
                                }
                            });
                        } else {
                            // Fallback to original behavior if taxonomy not found
                            collect($itemsWithSameId)->each(function ($item) use ($job, $taxonomyId) {
                                try {
                                    $job->taxonomies()->attach(
                                        $taxonomyId,
                                        [
                                            'custom_properties' => $item,
                                        ]
                                    );
                                } catch (QueryException $e) {
                                    if ($e->getCode() != 23000) {
                                        throw $e;
                                    }
                                }
                            });
                        }
                    }
                }
            });

        // Add package to the job with expire and package type
        if ($isPaidJob) {
            $job->taxonomies()->attach(
                $packageId,
                [
                    'expires_at' => Carbon::now()->addDays(
                        in_array($packageId, [10336, 10337]) ? 15 : 30 // hardcode package 15 days
                    )->endOfDay(),
                ]
            );
        } else {
            $job->taxonomies()->attach(
                3904, // TODO: Gói free, remove hard code
                [
                    'expires_at' => Carbon::now()->addDays(14)->endOfDay(),
                ]
            );
        }

        // Company logo
        if ($tempLogoUrl = Arr::get($data, 'company_logo')) {
            $this->updateJobCompanyLogo($job, $tempLogoUrl);
        }

        return $job;
    }

    public function create($data)
    {
        $job = Auth::user()
            ->company
            ->jobs()
            ->create([
                'title' => Arr::get($data, 'title'),
                'status' => Job::STATUS_DRAFT,
                'uuid' => Str::uuid(),
            ]);

        $this->update($job, $data);

        $job->refresh();

        return $job;
    }

    /**
     * @param  Job  $job
     * @param  string  $imageUrl
     *
     * @return void
     *
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateJobCompanyLogo(Job $job, string $imageUrl): void
    {
        if (blank($imageUrl)) {
            return;
        }

        if (empty($job->company_logo)) {
            $this->updateCompanyLogo(
                $job,
                parse_url($imageUrl, PHP_URL_PATH)
            );
        }
    }

    /**
     * @param  Job  $job
     * @param $imagePath
     *
     * @return void
     *
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyLogo(Job $job, $imagePath): void
    {
        $filename = null;
        $extension = pathinfo($imagePath, PATHINFO_EXTENSION);
        if (is_null(null)) {
            $filename = 'TopDev-' . Str::random() . '-' . time() . '.' . $extension;
        }
        $job
            ->addMediaFromDisk(
                'uploads/' . basename($imagePath),
                'tmp'
            )
            ->preservingOriginal()
            ->usingFileName($filename)
            ->toMediaCollection('company_logo');
    }

    /**
     * @param  Job  $job
     * @param  User  $author
     *
     * @return void
     */
    public function createJobAuditLog(Job $job, User $author): void
    {
        /**
         * Audit Log
         * Value before create.
         */
        $oldValues = [];

        /*
         * Audit Log
         * Get new value after create and save Audit Log
         */
        $job->refresh();
        $newValued = $job->getAuditJobResource();
        $job->audits()->create([
            'user_type' => $author->getMorphClass(),
            'user_id' => $author->id,
            'event' => 'created',
            'old_values' => $oldValues,
            'new_values' => $newValued,
            'url' => request()->url(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * @param  array  $data
     *
     * @return array
     */
    public function updateTaxonomyRequestData(array $data): array
    {
        //        $data['requirements'] = collect($data['requirements'])->pluck('id')->all();
        $data['recruitment_processes'] = $data['recruiment_process'];
        //        $data['responsibilities'] = collect($data['responsibilities'])->pluck('id')->all();
        $data['education'] = $data['education_degree'];
        //        $data['benefits'] = collect($data['benefits'])->pluck('id')->all();

        return $data;
    }

    /**
     * @param  Job  $job
     * @param $field
     *
     * @return Job
     */
    public function updateTaxonomyByField(Job $job, $field): Job
    {
        collect($field)->each(function ($item) use ($job) {
            $job->taxonomies()->detach($item['id']);
            $job->taxonomies()->attach($item['id'], [
                'custom_properties' => json_encode($item),
            ]);
        });

        return $job;
    }

    /**
     * @param  Job  $job
     * @param  User  $author
     *
     * @return Job
     */
    public function createRequestDesignTicket(Job $job, User $author): Job
    {
        $response = CrmApi::createCrmTicket([
            'subject' => "Job #{$job->id}: Yêu cầu thiết kế JD mới",
            'service' => 3, // value: "Đăng tin mới"
            'type_id' => 1, // ticket

            'userid' => $author->company_id,
            'department' => 1,
            'job_id' => $job->id,
            'message' => $author->display_name
                . ' - Company: ' . $author->company->display_name
                . ' -  yêu cầu thiết kế JD mới cho JD #' . $job->id,
        ]);

        $job->update([
            'crm_request_design_id' => $response['data']['id'],
        ]);

        return $job;
    }
}
