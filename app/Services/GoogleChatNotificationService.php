<?php

namespace App\Services;

use App\Notifications\EmployerBadActionNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class GoogleChatNotificationService
{
    /**
     * @param  string  $badActionType
     *
     * @return JsonResponse
     */
    public static function employerBadActionNotification(string $badActionType): JsonResponse
    {
        $email = Auth::user()->email;
        $company = Auth::user()->company;

        try {
            Notification::route('googleChat', config('google-chat.spaces.bad_action_employer'))
                ->notify(new EmployerBadActionNotification(
                    $email,
                    $company->display_name,
                    $badActionType
                ));
        } catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ]);
        }

        return response()->json([
            'data' => null,
        ]);
    }
}
