<?php

namespace App\Notifications;

use App\Channels\TopDevMailChannel;
use App\Helpers\Helper;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class NotifyGiftSearchPackageWithCustomer extends Notification
{
    use Queueable;

    public const UTM_DEFAULT = [
        'utm_source' => 'expire-reminder',
        'utm_medium' => 'email',
        'utm_campaign' => 'automation'
    ];

    protected array $data;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param Company $notifiable
     * @return array
     */
    public function via(Company $notifiable)
    {
        if ($notifiable->isInactive()) {
            Log::info("[NotifyGiftSearchPackageWithCustomer] Company {$notifiable->display_name} is inactive, no send mail");
            return [];
        }
        return [TopDevMailChannel::class];
    }

    public function toSendMailSystem(Company $notifiable): array
    {
        $email = $this->toEmail($notifiable);
        return [
            'mail_type' => 'autoflow',
            'template' => 'mail-gift-search-package-with-customer',
            'display_name' => $notifiable->display_name,
            'subject' => $this->subjectEmail($notifiable),
            'email' => $email,
            'cc_mail' => Helper::toCcEmail($notifiable, $email),
            'package' => [
                'nameCompany' => $notifiable->display_name,
                'namePackage' => $this->data['namePackage'],
                'from' => Carbon::parse($this->data['from'])->format('d/m/Y'),
                'to' => Carbon::parse($this->data['to'])->format('d/m/Y'),
                'totalCredit' => $this->data['totalCredit']
            ],
            'param' => [
                '{name}' => $notifiable->display_name,
                '{email}' => $notifiable->email,
                '{utm}' => '?' . http_build_query(self::UTM_DEFAULT),
            ]
        ];
    }
    private function subjectEmail(Company $notifiable): string
    {
        return "[TopDev] Thông Báo Kích Hoạt Gói Search CV Của Quý Khách Hàng {$notifiable->display_name}";
    }

    private function toEmail(Company $notifiable)
    {
        return $notifiable
            ->employers()
            ->where('approved_at','<>', null)
            ->first()
            ->email;
    }

}
