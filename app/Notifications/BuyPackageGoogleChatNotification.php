<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\GoogleChat\Card;
use NotificationChannels\GoogleChat\GoogleChatChannel;
use NotificationChannels\GoogleChat\GoogleChatMessage;
use NotificationChannels\GoogleChat\Section;
use NotificationChannels\GoogleChat\Widgets\TextParagraph;

class BuyPackageGoogleChatNotification extends Notification
{
    use Queueable;

    private string $email;

    private string $company;

    private int $available;

    private int $companyId;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($companyId, $company, $email, $available)
    {
        $this->companyId = $companyId;
        $this->company = $company;
        $this->email = $email;
        $this->available = $available;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable): array
    {
        return [
            GoogleChatChannel::class,
        ];
    }

    /**
     * @param  BuyPackageGoogleChatNotification  $notifiable
     *
     * @return GoogleChatMessage
     */
    public function toGoogleChat(self $notifiable): GoogleChatMessage
    {
        return GoogleChatMessage::create()
            ->card(
                Card::create()
                    ->section(
                        Section::create(
                            TextParagraph::create()
                                ->bold('Support for job posting package purchase')
                                ->break()
                                ->break()
                                ->bold('Company: ')
                                ->text($notifiable->company)
                                ->break()
                                ->bold('ID AMS: ')
                                ->text($notifiable->companyId)
                                ->break()
                                ->bold('Email: ')
                                ->text($notifiable->email)
                                ->break()
                                ->bold('Available Job Postings: ')
                                ->text($notifiable->available)
                        )
                    )
            );
    }
}
