<?php

namespace App\Notifications;

use App\Models\Company;
use App\Models\SearchCandidate;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\GoogleChat\GoogleChatChannel;
use NotificationChannels\GoogleChat\GoogleChatMessage;

class RequestRefundNotification extends Notification
{
    use Queueable;

    private Company $company;

    private User $employer;

    private SearchCandidate $resume;

    private string $credit;

    private string $reason;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Company $company, User $employer, SearchCandidate $resume, $credit, $reason)
    {
        $this->company = $company;
        $this->employer = $employer;
        $this->resume = $resume;
        $this->credit = $credit;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     *
     * @return array
     */
    public function via($notifiable): array
    {
        return [
            GoogleChatChannel::class,
        ];
    }

    /**
     * @param  RequestRefundNotification  $notifiable
     *
     * @return GoogleChatMessage
     */
    public function toGoogleChat(self $notifiable): GoogleChatMessage
    {
        $id = (string) $this->company->id;

        return GoogleChatMessage::create()
            ->bold('Request Refund')
            ->line('')
            ->line('ID AMS: ')
            ->link(config('app.ams_url') . "/admin/search-cv/company-credits/{$id}/credit-history", $id)
            ->line('Company: ' . $this->company->display_name)
            ->line('Employer: ' . $this->employer->display_name)
            ->line('Resume\'s Name: ' . $this->resume->display_name)
            ->line('Resume\'s Credit: ' . $this->credit)
            ->line('Reason: ' . $this->reason)
            ->to('sales_team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @return array
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
