<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\GoogleChat\Card;
use NotificationChannels\GoogleChat\GoogleChatChannel;
use NotificationChannels\GoogleChat\GoogleChatMessage;
use NotificationChannels\GoogleChat\Section;
use NotificationChannels\GoogleChat\Widgets\TextParagraph;

class GoogleChatNotification extends Notification
{
    use Queueable;

    private string $email;

    private string $company;

    private string $available;

    private string $credit;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($email, $company, $available, $credit)
    {
        $this->email = $email;
        $this->company = $company;
        $this->available = $available;
        $this->credit = $credit;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable): array
    {
        return [
            GoogleChatChannel::class,
        ];
    }

    /**
     * @param  GoogleChatNotification  $notifiable
     *
     * @return GoogleChatMessage
     */
    public function toGoogleChat($notifiable): GoogleChatMessage
    {
        return GoogleChatMessage::create()
            ->card(
                Card::create()
                    ->section(
                        Section::create(
                            TextParagraph::create()
                                ->bold('The Credits top up Process')
                                ->break()
                                ->break()
                                ->bold('Email: ')
                                ->text($this->email)
                                ->break()
                                ->bold('Company: ')
                                ->text($this->company)
                                ->break()
                                ->bold('Available Credit: ')
                                ->text($this->available)
                                ->break()
                                ->bold('Resume\'s Credit: ')
                                ->text($this->credit)
                        )
                    )
            );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
