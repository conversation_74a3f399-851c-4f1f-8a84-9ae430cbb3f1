<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\GoogleChat\Card;
use NotificationChannels\GoogleChat\GoogleChatChannel;
use NotificationChannels\GoogleChat\GoogleChatMessage;
use NotificationChannels\GoogleChat\Section;
use NotificationChannels\GoogleChat\Widgets\TextParagraph;

class EmployerBadActionNotification extends Notification
{
    use Queueable;

    private string $email;

    private string $company;

    private string $badActionType;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($email, $company, $badActionType)
    {
        $this->email = $email;
        $this->company = $company;
        $this->badActionType = $badActionType;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     *
     * @return array
     */
    public function via($notifiable): array
    {
        return [
            GoogleChatChannel::class,
        ];
    }

    /**
     * @param  self  $notifiable
     *
     * @return GoogleChatMessage
     */
    public function toGoogleChat(self $notifiable): GoogleChatMessage
    {
        $maxAllowUnlock = config('candidate.max_allow_unlock');
        $maxAllowClick = config('candidate.max_allow_click_per_minute');

        $title = 'Unlock candidate exceeds the limit of ' . $maxAllowUnlock . ' CV per month.';
        if ($this->badActionType === 'click_candidate') {
            $title = 'Click exceeds the limit of ' . $maxAllowClick . ' CV per minute.';
        }

        return GoogleChatMessage::create()
            ->card(
                Card::create()
                    ->section(
                        Section::create(
                            TextParagraph::create()
                                ->bold('Employer Bad Action.')
                                ->break()
                                ->bold($title)
                                ->break()
                                ->break()
                                ->bold('Employer Email: ')
                                ->text($notifiable->email)
                                ->break()
                                ->bold('Company: ')
                                ->text($notifiable->company)
                        )
                    )
            );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     *
     * @return array
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
