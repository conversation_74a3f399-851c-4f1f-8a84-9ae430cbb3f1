<?php

namespace App\Exports;

use App\Http\Controllers\Api\Candidate\GetCandidateAction;
use App\Models\Candidate;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CandidateExport implements FromQuery, WithMapping, WithHeadings
{
    use Exportable;

    /**
     * @var array
     */
    private array $search = [];

    public function __construct(array $search)
    {
        $this->search = $search;
    }

    public function query()
    {
        /** @var Company $company */
        $company = request()->user()->company;

        $candidatesQuery = $company->candidates()
            ->withStatusReadyAndUnqualifiedAndRecall()
            ->orderByDesc('created_at')
            ->with([
                'resume',
                'job',
                'resume.addresses',
                'resume.addresses.district',
                'resume.addresses.province',
                'resume.taxonomies.term',
                'resume.meta',
            ]);

        // Use function from the search action to have the same result
        $candidatesQuery = GetCandidateAction::filterCandidateByRequest(new Request($this->search), $candidatesQuery);

        return $candidatesQuery;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Candidate Fullname',
            'Candidate Email',
            'Candidate Phone number',
            'Candidate Location',
            'Candidate Skills',
            'Candidate YOE',
            'Job title',
            'Applied date',
            'Candidate has cover letter',
            'View CV',
            'Note',
            'Status',
        ];
    }

    /**
     * @param  Candidate  $row
     * @return array
     */
    public function map($row): array
    {
        if (!$row->resume->exists) {
            return $this->getRowWithDeleteAccount($row);
        }

        if ($row->is_remove_cv) {
            return $this->getRowWithRemoveCv($row);
        }

        if (!is_null($row->recalled_at) || $row->status == Candidate::CANDIDATE_RECALL) {
            return $this->getRowWithRecall($row);
        }

        return [
            $row->id,
            $row->resume->getName(),
            $row->resume->email,
            $row->resume->phone,
            $row->resume->addresses
                ->map(function (\App\Models\Address $address) {
                    return join(', ', array_filter([$address->district->name ?? null, $address->province->name ?? null]));
                })
                ->implode('; '),
            $row->resume->taxonomies->where('taxonomy', 'skills')->pluck('term.name')->join(', '),
            $row->getYoeFromMeta(),
            $row->job->title . ' (#' . $row->job->id . ')',
            optional($row->sent_employer_at ?? $row->created_at)->format('H:i:s d/m/Y'),
            $row->cover_letter ? 'Yes' : 'No',
            url('/candidates/' . $row->id . '#nav-employer'),
            $row->employer_note,
            $row->avg_skill_match >= 1 ? 'Best match' : 'Other',
        ];
    }

    /**
     * @param  Candidate  $row
     *
     * @return array
     */
    public function getRowWithDeleteAccount(Candidate $row): array
    {
        return [
            '',
            'Người dùng bị vô hiệu hóa',
            '',
            '',
            '',
            '',
            '',
            $row->job->title . ' (#' . $row->job->id . ')',
            $row->created_at->format('H:i:s d/m/Y'),
            $row->cover_letter ? 'Yes' : 'No',
            '',
            $row->employer_note,
            'Recall',
        ];
    }

    /**
     * @param  Candidate  $row
     *
     * @return array
     */
    public function getRowWithRemoveCv(Candidate $row): array
    {
        return [
            '',
            $row->resume->getName(),
            '',
            '',
            '',
            '',
            '',
            $row->job->title . ' (#' . $row->job->id . ')',
            $row->created_at->format('H:i:s d/m/Y'),
            $row->cover_letter ? 'Yes' : 'No',
            '',
            $row->employer_note,
            'Recall',
        ];
    }

    /**
     * @param  Candidate  $row
     *
     * @return array
     */
    public function getRowWithRecall(Candidate $row): array
    {
        return [
            $row->id,
            $row->resume->getName(),
            $row->resume->email,
            $row->resume->phone,
            $row->resume->addresses
                ->map(function ($address) {
                    return join(', ', array_filter([$address->district->name ?? null, $address->province->name ?? null]));
                })
                ->implode('; '),
            $row->resume->taxonomies->where('taxonomy', 'skills')->pluck('term.name')->join(', '),
            $row->getYoeFromMeta(),
            $row->job->title . ' (#' . $row->job->id . ')',
            $row->created_at->format('H:i:s d/m/Y'),
            $row->cover_letter ? 'Yes' : 'No',
            url('/candidates/' . $row->id . '#nav-employer'),
            $row->employer_note,
            'Recall',
        ];
    }
}
