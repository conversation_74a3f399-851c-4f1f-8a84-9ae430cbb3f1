<?php

namespace App\Exports;

use App\Http\Controllers\Api\SearchCandidate\GetSearchCandidateAction;
use App\Models\SearchCandidate;
use App\Models\Taxonomy;
use Auth;
use Carbon\Carbon;
use Elastic\Elasticsearch\Client;
use Exception;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Search;
use ONGR\ElasticsearchDSL\Sort\FieldSort;

class SearchCandidateExport implements FromQuery, WithMapping, WithHeadings
{
    use Exportable;

    /**
     * @var array
     */
    private $search = [];

    public function __construct(array $search)
    {
        $this->search = $search;
    }

    public function query()
    {
        $query = SearchCandidate::query()
            ->with([
                'unlockedByCurrentCompany'
            ]);

        // Filter only unlocked candidate logic here
        /* @phpstan-ignore-next-line */
        $unlockedUserIds = Auth::user()->company->unlockedSearchCandidates->pluck('id');

        $query->whereIn('id', $unlockedUserIds->all());

        $this->exportSearchCandidateFromFilter($query);

        return $query;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Full name',
            'Job position',
            'Year of experience',
            'Location', // Province name
            'Email',
            'Phone',
            'Skills',
            'Latest experience',
            'Latest education',
            'Unlocked time' // format: Y-m-d
        ];
    }

    /**
     * @param SearchCandidate $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->display_name,
            $row->position,
            $row->years_of_exp,
            $row->province_name ?? ($row->province ? $row->province->name : ''),
            $row->email,
            $row->phone,
            $this->getTechnicalSkills($row->skills),
            $this->getLatestExperience($row->experiences),
            $this->getLatestEducations($row->educations),
            $this->getUnlockedTime($row)
        ];
    }

    public function exportSearchCandidateFromFilter($query)
    {
        $filters = $this->search;
        $keyword = $filters['keyword'] ?? [];

        // filter keyword
        if (!blank($keyword)) {
            $listKeyword = collect($keyword)
                ->filter()
                ->map(fn ($keyword) => trim($keyword));

            $listKeyword->each(function($kw) use ($query) {
                $query->where(function($q) use ($kw) {
                    $this->getKeywordQuery($q, $kw);
                });
            });
        }

        // Filter saved candidates only
        if (is_bool($filters['showWishList']) && $filters['showWishList'] == true) {
            /* @phpstan-ignore-next-line */
            $savedUserIds = Auth::user()->company->savedSearchCandidates->pluck('id');

            $query->whereIn('id', $savedUserIds->all());
        }

        // Filter by skills
        if (!empty($filters['skill'])) {
            $skills = collect($filters['skill'])
                ->unique()
                ->filter();

            $query->where(function ($q) use ($skills) {
                $skills->each((function($skillId) use ($q) {
                    $q->whereJsonContains('skills->technical_skills', ['skill_id' => (int) $skillId]);
                }));
            });
        }

        // Filter by working experiences
        if (!empty($filters['experience'])) {
            $yearsOfExp = Arr::get(Taxonomy::EXPERIENCE_TAXONOMIES, $filters['experience']);
            if ($filters['experience'] == 1651) {
                $query->where('years_of_exp', '>', $yearsOfExp);
            } else {
                $query->where('years_of_exp', '=', $yearsOfExp);
            }
        }

        // Filter by location
        if (!empty($filters['location'])) {
            $query->where('province_code', $filters['location']);
        }

        // Filter by language
        if (!empty($filters['candidate_language'])) {
            $query->whereJsonContains('languages', ['language' => $filters['candidate_language']]);
        }

        // Filter by last updated time
        $dateRange = !blank($filters['timeRange']) ? $filters['timeRange'] : [];
        if (!empty($dateRange['start']) && !empty($dateRange['end'])) {
            $query->whereBetween('updated_at', [
                Carbon::createFromFormat('Y-m-d', $dateRange['start'])->startOfDay()->toDateTimeString(),
                Carbon::createFromFormat('Y-m-d', $dateRange['end'])->endOfDay()->toDateTimeString()
            ]);
        }
    }

    private function getKeywordQuery($query, $keyword)
    {
        $searchKeyword = SearchCandidate::search('*', function (Client $client, Search $body) use ($keyword) {
            // Base query
            $body->setTrackTotalHits(true);
            $getSearchCandidateAction = new GetSearchCandidateAction();

            // Handle logic to search
            if (!blank($keyword)) {
                $listKeyword = collect($keyword)
                    ->filter()
                    ->map(fn ($keyword) => trim($keyword));

                // Features for developers only
                if (Auth::user()->email === '<EMAIL>') { /** @phpstan-ignore-line */
                    $index = $listKeyword->search(function ($keyword) {
                        return preg_match('/^id:/i', $keyword);
                    });

                    if (is_numeric($index)) {
                        // Extract and process the IDs
                        $ids = collect(explode(",", explode(":", $listKeyword[$index])[1]))
                            ->map(fn ($id) => trim($id))
                            ->filter();

                        // Remove the keyword from the list
                        $listKeyword->forget($index);

                        // Add post filter if IDs exist
                        if ($ids->isNotEmpty()) {
                            $body->addPostFilter(
                                new TermsQuery('id', $ids->all())
                            );
                        }
                    }
                }

                $listKeyword->each(function ($keyword) use ($body, $getSearchCandidateAction) {
                    $body->addQuery($getSearchCandidateAction->getBaseSearchQuery($keyword));
                });
            } else {
                // Sort by field updated_at if keyword search is not available
                $body->addSort(
                    new FieldSort(
                        'updated_at',
                        FieldSort::DESC
                    )
                );
            }

            return $client->search(['index' => 'search_candidates', 'body' => $body->toArray()])->asArray();
        });

        $searchCandidateIds = array_map(function ($item) {
            return $item['id'];
        }, $searchKeyword->get()->toArray());

        $query->whereIn('id', $searchCandidateIds);
    }

    /**
     * get unlocked time
     *
     * @param SearchCandidate $searchCandidate
     * @return string
     */
    private function getUnlockedTime($searchCandidate)
    {
        if (!($unlockedLog = $searchCandidate->companyUnlockedLog)) return '';

        return $unlockedLog->created_at->format('Y-m-d');
    }

    /**
     * get technical skills
     *
     * @param array $skills
     * @return string
     */
    private function getTechnicalSkills($skills)
    {
        if ($technicalSkills = Arr::get($skills, 'technical_skills')) {
            $skillNames = array_map(function ($item) {
                return $item['skill_name'];
            }, $technicalSkills);

            return implode(', ', $skillNames);
        }

        return '';
    }

    /**
     * get latest education value
     *
     * @param array $educations
     * @return string
     */
    private function getLatestEducations($educations)
    {
        if (empty($educations)) return '';
        $educations = $this->formatDateStringForArray($educations);

        $max = max(array_map(function ($item) {
            return Arr::get($item, 'from');
        }, $educations)); // find latest from

        $latestEdu = array_values(array_filter($educations, function ($item) use ($max) {
            return Arr::get($item, 'from') == $max;
        })); // search latest from in array

        if (!$latestEdu = $latestEdu[array_key_first($latestEdu)]) return '';

        return Arr::get($latestEdu, 'school_name') . "\n" .
            implode(' - ', [Arr::get($latestEdu, 'from'), Arr::get($latestEdu, 'to')]);
    }

    /**
     * format date string for array
     *
     * @param array $array
     * @return array
     */
    private function formatDateStringForArray($array)
    {
        return array_map(function ($exp) {
            try {
                $exp['to'] = $this->convertDate($exp['to']);
                $exp['from'] = $this->convertDate($exp['from']);
            } catch (Exception $exception) {
            }

            return $exp;
        }, $array); // format date value and convert to array
    }

    /**
     * get latest experience value
     *
     * @param array $experiences
     * @return string
     */
    private function getLatestExperience($experiences)
    {
        if (empty($experiences)) return '';
        $experiences = $this->formatDateStringForArray($experiences);

        $max = max(array_map(function ($item) {
            return Arr::get($item, 'from');
        }, $experiences)); // find latest from

        $latestExp = array_values(array_filter($experiences, function ($item) use ($max) {
            return Arr::get($item, 'from') == $max;
        })); // search latest from in array

        if (!$latestExp = $latestExp[array_key_first($latestExp)]) return '';

        return implode(' - ', [Arr::get($latestExp, 'position'), Arr::get($latestExp, 'company')]) . "\n" .
            implode(' - ', [Arr::get($latestExp, 'from'), Arr::get($latestExp, 'to')]);
    }

    /**
     * return date format Y-m
     *
     * @param string $dateString
     * @return string
     */
    private function convertDate($dateString)
    {
        try {
            return Carbon::parse($dateString)->format('Y-m');
        } catch (Exception $exception) {
        }

        return $dateString;
    }
}
