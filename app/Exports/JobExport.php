<?php

namespace App\Exports;

use App\Models\Job;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class JobExport implements FromQuery, WithMapping, WithHeadings
{
    use Exportable;

    /**
     * @var array
     */
    private $search = '';

    public function __construct(array $search)
    {
        $this->search = $search;
    }

    public function query()
    {
        $jobsQuery = Job::query()
            ->orderByDesc('created_at')
            ->whereOwnedId(
                request()->user()->company_id
            )
            ->with([
                'addresses',
                'addresses.district',
                'information',
                'packages',
                'creator',
                'count',
                'taxonomies',
                'taxonomies.term',
                'announcements',
                'note',
            ]);

        $this->exportJobFromFilter($jobsQuery);

        return $jobsQuery;
    }

    public function headings(): array
    {
        return [
            'Job ID',
            'Status',
            'Job Title',
            'Location',
            'Mức lương của job',
            'Mức lương estimation',
            'Year of experience',
            'Level Job',
            'Type',
            'Skills',
            'Created at',
            'Created by',
            'Published date',
            'Expires at',
            'View(s)',
            'Applications',
            'Recruitment process',
            'Job benefits',
            'Email for applications',
            'Note for TopDev',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->getStatusText(),
            $row->title,
            $row->addresses->pluck('full_address')->implode('/ '),
            Arr::get($row->salary, 'value'),
            Arr::get($row->salary, 'estimate'),
            $row->taxonomies->where('taxonomy', 'experiences')->pluck('term.name')->implode(', '),
            $row->taxonomies->where('taxonomy', 'job_levels')->pluck('term.name')->implode(', '),
            $row->taxonomies->where('taxonomy', 'job_types')->pluck('term.name')->implode(', '),
            $row->taxonomies->where('taxonomy', 'skills')->pluck('term.name')->implode(', '),
            $row->created_at,
            $row->creator->email,
            $row->published_at,
            !empty($row->expires_at) ? $row->expires_at : '',
            $row->count->num_viewers,
            $row->qualifiedCandidates->count(),
            !empty($row->note->recruiment_process ?? null) ? 'Yes' : 'No',
            !empty($row->benefits) ? 'Yes' : 'No',
            trim(implode(', ', $row->emails_cc), ' ,'),
            $row->note->employer_notes ?? '',
        ];
    }

    /**
     * Handle function exportJobFromFilter.
     *
     * @param {location_id,job_id,skills_id,status,created_by,query}
     * @return job query
     */
    public function exportJobFromFilter($jobsQuery)
    {
        // Export keywords
        $jobsQuery->when($this->search['query'], function ($query) {
            $query->where(
                function ($query) {
                    $query->where('title', 'LIKE', '%' . $this->search['query'] . '%')
                        ->orWhere('id', 'LIKE', '%' . $this->search['query'] . '%');
                }
            );
        });

        // Export location id
        $jobsQuery->when($this->search['location_id'], function ($query) {
            $query->whereHas(
                'addresses.district',
                function ($query) {
                    $query->where('code', $this->search['location_id']);
                }
            );
        });

        // Export job id
        $jobsQuery->when($this->search['job_id'], function ($query) {
            $query->where('id', $this->search['job_id']);
        });

        // Filter by skills_id
        $jobsQuery->when($this->search['skills_id'], function ($query) {
            $query->whereHas('taxonomies', function ($query) {
                $query->whereIn('term_taxonomy.id', $this->search['skills_id']);
            });
        });

        // Filter by status
        $jobsQuery->when($this->search['status'], function ($query) {
            $query->where('status', $this->search['status']);
        });

        // Filter by created by
        $jobsQuery->when($this->search['created_by'], function ($query) {
            if ($this->search['created_by'] != config('topdev.creator.default.id')) {
                $query->where('creator_id', $this->search['created_by']);
            } else {
                $query->doesntHave('creator');
            }
        });

        return $jobsQuery;
    }
}
