<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ActivityLog
{
    protected $excepts = [
        'api.form-values.job-title',
        'api.form-values.employer-name',
        'search-candidates.preview-cv',
    ];

    /**
     * Enforce json.
     *
     * @param Request  $request
     * @param Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        return $next($request);
    }

    /**
     * @param  Request  $request
     * @param  mixed  $response
     *
     * @throws \JsonException
     */
    public function terminate(Request $request, $response): void
    {
        if (!$request->isJson()) {
            return;
        }

        if (in_array($request->route()->getName(), $this->excepts, true)) {
            return;
        }

        $activity = [
            'user_id' => auth()->id(),
            'request' => [
                'uri' => $request->fullUrl(),
                'input' => $request->except([
                    'old_password',
                    'new_password',
                ]),
            ],
            'response' => [
                'status' => $response->getStatusCode(),
                'content' => $response->getContent(),
            ],
        ];

        Log::channel(config('logging.activity_log_default'))
            ->info(json_encode($activity, JSON_THROW_ON_ERROR));
    }
}
