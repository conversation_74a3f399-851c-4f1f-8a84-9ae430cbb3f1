<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON><PERSON>;
use Illuminate\Http\Request;

class ForceLogoutUser
{
    /**
     * Handle an incoming request.
     *
     * @param Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!$request->hasCookie('forced_logout') && $request->user() && $request->user()->email == '<EMAIL>') {
            auth()->logout();
            Cookie::queue('forced_logout', 'true');

            return redirect()->to('/');
        }

        return $next($request);
    }
}
