<?php

namespace App\Http\Resources\Audit;

use Illuminate\Http\Resources\Json\JsonResource;

class AuditCompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            /*
            |--------------------------------------------------------------------------
            | Info basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->id,

            /*
            |--------------------------------------------------------------------------
            | Address
            |--------------------------------------------------------------------------
            */
            'addresses' => $this->full_addresses,

            /*
            |--------------------------------------------------------------------------
            | Content
            |--------------------------------------------------------------------------
            */
            'email' => $this->email,
            'phone' => $this->phone,
            'website' => $this->website,
            'display_name' => $this->display_name,
            'tagline' => $this->tagline,
            'description' => $this->description,
            'description_str' => $this->description_str,
            'slug' => $this->slug,
            'recruitment_process' => $this->recruitment_process ?? [],
            'status' => $this->status,
            'status_display' => $this->status_display,
            'benefits' => $this->benefits ?? [],
            'logo' => optional($this->image_logo)->getUrl(),

            /*
            |--------------------------------------------------------------------------
            | Datetime
            |--------------------------------------------------------------------------
            */
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
