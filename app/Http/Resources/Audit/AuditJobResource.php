<?php

namespace App\Http\Resources\Audit;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

class AuditJobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            /*
            |--------------------------------------------------------------------------
            | Info basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->id,
            'title' => $this->title,

            /*
            |--------------------------------------------------------------------------
            | Content
            |--------------------------------------------------------------------------
            */
            'content' => $this->content,
            'benefits' => $this->benefits ?? [],
            'requirements' => $this->requirements,
            'responsibilities' => $this->responsibilities,
            'recruiment_process' => $this->recruiment_process ?? [],

            /*
            |--------------------------------------------------------------------------
            | Company
            |--------------------------------------------------------------------------
            */
            'company' => $this->company->display_name,

            /*
            |--------------------------------------------------------------------------
            | Skill
            |--------------------------------------------------------------------------
            */
            'extra_skills_str' => $this->taxonomies->where('taxonomy', 'extra_skills')->pluck('term.name')->implode(', '),
            'skills_str' => $this->taxonomies->where('taxonomy', 'skills')->pluck('term.name')->implode(', '),
            'experiences_str' => $this->taxonomies->where('taxonomy', 'experiences')->pluck('term.name')->implode(', '),
            'job_types_str' => $this->taxonomies->where('taxonomy', 'job_types')->pluck('term.name')->implode(', '),
            'job_levels_str' => $this->taxonomies->where('taxonomy', 'job_levels')->pluck('term.name')->implode(', '),

            /*
            |--------------------------------------------------------------------------
            | Address
            |--------------------------------------------------------------------------
            */
            'addresses' => $this->full_addresses,

            /*
            |--------------------------------------------------------------------------
            | Status
            |--------------------------------------------------------------------------
            */
            'status_display' => $this->getStatusText(),
            'salary' => Arr::get($this->salary, 'value'),

            /*
            |--------------------------------------------------------------------------
            | Datetime
            |--------------------------------------------------------------------------
            */
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'expires_at' => empty($this->expires_at) ? null : $this->expires_at->format('Y-m-d H:i:s'),
            'published_at' => empty($this->published_at) ? null : $this->published_at->format('Y-m-d H:i:s'),
            'crm_invoice_id' => $this->crm_invoice_id,
        ];
    }
}
