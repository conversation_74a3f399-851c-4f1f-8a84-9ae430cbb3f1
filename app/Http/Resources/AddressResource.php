<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'ward_id' => $this->ward_id,
            'province_id' => $this->province_id,
            'district_id' => $this->district_id,
            'full_address' => $this->full_address,
            'street' => $this->street,
            'order' => $this->order,
        ];
    }
}
