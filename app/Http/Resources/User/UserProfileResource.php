<?php

namespace App\Http\Resources\User;

use App\Helpers\FeatureFlag;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\User
 */
class UserProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->display_name,
            'email' => $this->email,
            'roles' => [$this->type],
            'company_id' => $this->company_id,
            'approved' => now()->greaterThanOrEqualTo($this->approved_at),
            'full_name' => $this->getName(),
            'position' => $this->position,
            'phone' => $this->phone,
            'username' => $this->username,
            'is_denied' => $this->getIsDenied(),
            'is_unlocked' => $this->company->companySearchPackage->count() > 0,
            'available_credit' => $this->company->available_credit,
        ];
    }

    private function getIsDenied()
    {
        return now()->lessThan($this->approved_at) || blank($this->company);
    }
}
