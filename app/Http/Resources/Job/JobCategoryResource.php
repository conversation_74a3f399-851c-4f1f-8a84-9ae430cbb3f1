<?php

namespace App\Http\Resources\Job;

use App\Http\Resources\AddressResource;
use App\Models\Job;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Job
 */
class JobCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array|Arrayable|\JsonSerializable
     */
    public function toArray(Request $request): array|\JsonSerializable|Arrayable
    {
        return [
            'categories' => $this['categories']->map(fn($category) => $this->mapCategory($category)),
            'roles' => $this['roles']->map(fn($category) => $this->mapCategory($category))->groupBy('parent_id'),
        ];
    }

    private function mapCategory($category)
    {
        return [
            'id' => $category->id,
            'parent_id' => $category->parent_id,
            'name' => $category->name,
            'type' => $category->type,
            'sort_order' => $category->sort_order,
        ];
    }
}
