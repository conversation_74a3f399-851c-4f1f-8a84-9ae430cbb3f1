<?php

namespace App\Http\Resources\Job;

use App\Http\Resources\AddressResource;
use App\Models\Job;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Job
 */
class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array|Arrayable|\JsonSerializable
     */
    public function toArray(Request $request): array|\JsonSerializable|Arrayable
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'level' => $this->level,
            'content' => $this->content,
            'requirements' => $this->requirements,
            'requirements_id' => $this->requirements_id,
            'responsibilities' => $this->responsibilities,
            'responsibilities_id' => $this->responsibilities_id,
            'experiences_ids' => $this->experiences_ids,
            'experiences' => $this->experiences,
            'contract_type' => $this->contract_types,
            'contract_type_id' => $this->contract_type_ids,
            'salary' => $this->information->raw_salary,
            'recruiment_process' => $this->recruiment_processes,
            'recruiment_process_id' => $this->recruiment_processes_id,
            'skills_ids' => $this->skills_ids,
            'skills' => $this->skills,
            'job_levels' => $this->job_levels,
            'levels' => $this->levels,
            'job_types' => $this->job_types,
            'types' => $this->types,
            'role' => $this->role,
            'role_id' => $this->role_id,
            'addresses_id' => $this->addresses_id,
            'addresses' => AddressResource::collection($this->addresses),
            'benefits' => $this->benefits,
            'benefits_id' => $this->benefits_id,
            'emails_cc' => $this->emails_cc,
            'note' => $this->note->employer_notes,
            'status' => $this->getStatusText(),
            'education_certificate' => $this->education_certificate,
            'education_degree' => $this->education_degree,
            'education_degree_id' => $this->education_degree_id,
            'education_major' => $this->education_major,
            'education_major_id' => $this->education_major_id,
            'benefits_description' => $this->benefits_description,
            'tx_recruiment_process' => $this->taxonomy_recruitment_processes,
            'tx_benefits' => $this->taxonomy_benefits,
            'job_banner' => $this->job_banner,
            'job_banner_id' => $this->job_banner_id,
            'job_template' => $this->job_template,
            'job_template_id' => $this->job_template_id,
            'job_template_color' => $this->job_template_color,
            'job_template_color_id' => $this->job_template_color_id,
            'company_tagline' => $this->company_tagline,
            'is_48h' => $this->is_48h,
            'created_at' => $this->created_at->format('H:i:s d-m-Y'),
            'created_by' => $this->creator->email,
            'package_id' => $this->package_id,
            'status_id' => $this->status,
            'category_id' => $this->job_category_id,
            'job_category_id' => $this->job_category_role_ids,
        ];
    }
}
