<?php

namespace App\Http\Resources\Job;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

/**
 * @property mixed $id
 * @property mixed $title
 * @property null|Carbon $published_at
 */
class JobTitleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $publishedAt = $this->published_at ? $this->published_at->format('d-m-Y') : '';
        return [
            'value' => $this->id,
            'title' => $this->title,
            'label' => $request->need_published_at ? $this->title . $this->getIdText() . $publishedAt : $this->title . $this->getIdText(),
            'published_at' => $publishedAt,
        ];
    }

    protected function getIdText(): string
    {
        if (!$this->id) {
            return '';
        }

        return '(#' . $this->id . ')';
    }
}
