<?php

namespace App\Http\Resources\Job;

use App\Models\Job;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\DB;

class JobCollection extends ResourceCollection
{
    public $collects = JobListResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection,
            'facets' => [
                'status' => $this->getStatusFacets($request),
            ],
        ];
    }

    protected function getStatusFacets($request)
    {
        return $request
            ->user()
            ->company
            ->jobs()
            ->select(DB::raw('count(*) as total, status as status_code'))
            ->groupBy('status')
            ->get()
            ->map(function ($status) {
                $status['status_code'] = Job::getStatusDescription()[$status['status_code']];

                return $status;
            });
    }
}
