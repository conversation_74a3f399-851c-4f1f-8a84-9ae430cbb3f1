<?php

namespace App\Http\Resources\Job;

use Illuminate\Http\Resources\Json\JsonResource;

class JobListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->title,
            'location' => $this->getLocation(),
            'salary' => $this->information->raw_salary,
            'package' => $this->packages->pluck('name')->implode(', '),
            'published_date' => optional($this->published_at)->format('d-m-Y'),
            'expires_in' => max(now()->startOfDay()->diffInDays($this->expires_at, false), 0),
            'expires_at' => optional($this->expires_at)->format('d-m-Y'),
            'created_by' => optional($this->creator)->email,
            'status' => $this->getStatusText(),
            'application_count' => $this->qualifiedCandidates->count(),
            'views_count' => $this->count->num_viewers,
            'skills' => $this->taxonomies->where('taxonomy', 'skills')->pluck('term.name'),
            'detail_url' => $this->detail_url,
            'is_48h' => $this->is_48h,
            'best_match_count' => $this->candidate_best_match_count,
        ];
    }

    protected function getLocation()
    {
        return $this->addresses->map(function ($address) {
            return trim($address->district->name_with_type . ', ' . $address->province->name, " ,\t\n\r\0\x0B");
        })
            ->implode('; ');
    }

    public function with($request)
    {
        return [
            'meta' => [
                'key' => 'value',
            ],
        ];
    }
}
