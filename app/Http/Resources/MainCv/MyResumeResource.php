<?php

namespace App\Http\Resources\MainCv;

use App\Models\MyResume;
use Illuminate\Support\Arr;
use Str;

/**
 * @mixin MyResume
 */
class MyResumeResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Check search candidate unlocked by current company
        $isUnlocked = $this->user->searchCandidate->is_unlocked;
        $experiences = $this->mapExperiences(Arr::get($this->content, 'experiences'));

        return [
            'id' => $this->id,
            'fullname' => $isUnlocked
                ? Arr::get($this->content, 'personal.0.name')
                : $this->maskName(Arr::get($this->content, 'personal.0.name')),
            'email' => $isUnlocked
                ? Arr::get($this->content, 'personal.0.email')
                : Str::mask(Arr::get($this->content, 'personal.0.email'), '*', 0),
            'phone' => $isUnlocked
                ? Arr::get($this->content, 'personal.0.phone')
                : Str::mask(Arr::get($this->content, 'personal.0.phone'), '*', 0),
            'address' => $isUnlocked
                ? Arr::get($this->content, 'personal.0.address')
                : Str::mask(Arr::get($this->content, 'personal.0.address'), '*', 0),
            'province_code' => Arr::get($this->content, 'personal.0.provinces_id'),
            'province_name' => Arr::get($this->content, 'personal.0.province_name'),
            'avatar_url' => Arr::get($this->content, 'personal.0.images'),

            'years_of_exp' => $this->getYearsOfExp($experiences),
            'educations' => Arr::get($this->content, 'educations', []),
            'position' => Arr::get($this->content, 'personal.0.profession'),

            'experiences' => $experiences,
            'linkedin_link' => Arr::get($this->content, 'personal.0.linkedin'),
            'github_link' => Arr::get($this->content, 'personal.0.github'),
            'summary' => Arr::get($this->content, 'summary.0.summary'),
            'skills' => array_filter([
                'technical_skills' => array_filter($this->mapTechnicalSkills(Arr::get($this->content, 'skill_groups'))),
                'soft_skills' => array_filter(explode(',', Arr::get($this->content, 'personal.0.extra_skills', ''))),
            ]),

            'projects' => array_filter(array_map(fn ($project) => [
                'project' => Arr::get($project, 'project'),
                'project_time' => Arr::get($project, 'project_time'),
                'description' => Arr::get($project, 'description'),
            ], Arr::get($this->content, 'projects', []))),

            'languages' => Arr::get($this->content, 'languages', []),
            'interests' => array_map(fn ($interest) => Arr::get($interest, 'interest'), Arr::get($this->content, 'interests', [])),
            'references' => Arr::get($this->content, 'references', []),
            'activities' => array_filter(array_map(fn ($activity) => [
                'activity' => Arr::get($activity, 'activity'),
                'is_working_here' => empty(Arr::get($activity, 'date_to')),
                'date_from' => $this->parseDate(Arr::get($activity, 'date_from')),
                'date_to' => $this->parseDate(Arr::get($activity, 'date_to')),
                'achievement' => Arr::get($activity, 'achievement'),
            ], Arr::get($this->content, 'activities', []))),

            'certificates' => Arr::get($this->content, 'certificates', []),
            'additionals' => Arr::get($this->content, 'additionals', []),
        ];
    }
}
