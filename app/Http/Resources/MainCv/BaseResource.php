<?php

namespace App\Http\Resources\MainCv;

use App\Models\Term;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

abstract class BaseResource extends JsonResource
{
    protected array $keyCheckGiveMonthFromAndMonthTo = ['present', 'now'];

    public static function getYearsOfExp($experiences, $default = 0)
    {
        if (empty($experiences)) {
            return $default;
        }

        $from = array_filter(Arr::pluck($experiences, 'year_from'));
        $to = array_filter(Arr::pluck($experiences, 'year_to'));
        $isWorkingHere = count(array_filter($experiences, fn ($experience) => $experience['is_working_here'])) > 0;

        // Does not have from/to take from exp
        if (empty($from) && (empty($to) && !$isWorkingHere)) {
            return $default;
        }

        // Get min/max value from the list
        $minFrom = empty($from) ? date('Y-m-d') : min($from);
        $maxTo = (empty($to) || $isWorkingHere) ? date('Y-m-d') : max($to);

        $minFrom = Carbon::parse($minFrom);
        $maxTo = Carbon::parse($maxTo);
        // Get the different day between from and to then divide by 365 to get years
        $diffInDays = $maxTo->diffInDays($minFrom) / 365;

        // Round up or down based on fraction of the number: >0.5 => 1, else 0
        return intval(floor($diffInDays) + (($diffInDays - floor($diffInDays)) >= 0.5 ? 1 : 0));
    }

    public function mapTechnicalSkills($skill_groups): array
    {
        if (empty($skill_groups)) {
            return [];
        }
        $skills = [];
        foreach ($skill_groups as $group) {
            $skills = array_merge($skills, array_map(fn ($skill) => ['skill_id' => Arr::get($skill, 'skill_id'), 'skill_name' => Arr::get($skill, 'skill')], Arr::get($group, 'skills', [])));
        }

        return $skills;
    }

    public function getValidSkills($skillNames, $taxonomy = null): array
    {
        if (empty($skillNames)) {
            return [];
        }
        $skillNames = array_map(fn ($name) => mb_strtolower(trim($name), 'UTF-8'), $skillNames);
        $placeholders = substr(str_repeat(', ?', count($skillNames)), 2);

        return Term::selectTerm($taxonomy)
            ->whereRaw('LOWER(`name`) IN (' . $placeholders . ')', $skillNames)
            ->get()
            ->map(fn ($skill) => ['skill_id' => $skill->ttid, 'skill_name' => $skill->name]) /* @phpstan-ignore-line I don't want to fix this */
            ->toArray();
    }

    public function parseDate($dateStr = null, $format = 'Y-m'): ?string
    {
        if (!$dateStr) {
            return null;
        }
        try {
            $dateObj = Carbon::parse($dateStr);
            if (!$dateObj->isValid()) {
                return null;
            }

            return $dateObj->format($format);
        } catch (\Exception $ex) {
            return null;
        }
    }

    public function maskName($name): string
    {
        return Str::of($name)
            ->explode(' ')
            ->filter()
            ->map(fn ($value) => (Str::substr(trim($value), 0, 1)))
            ->implode('. ') . '.';
    }

    public function mapExperiences($experiences): array
    {
        if (empty($experiences)) {
            return [];
        }
        $profileExp = [];

        foreach ($experiences as $experience) {
            $skills = !empty(Arr::get($experience, 'technical'))
                ? $this->getValidSkills(explode(',', Arr::get($experience, 'technical')), 'skills') : [];
            $profileExp[] = [
                'company' => Arr::get($experience, 'company'),
                'position' => Arr::get($experience, 'position'),
                'experience' => Arr::get($experience, 'experience'),
                'year_from' => $this->parseDate(Arr::get($experience, 'year_from')),
                'year_to' => $this->parseDate(Arr::get($experience, 'year_to')),
                'is_working_here' => empty(Arr::get($experience, 'year_to')),
                'projects' => array_map(fn ($project) => [
                    'project' => Arr::get($project, 'project'),
                    'project_time' => Arr::get($project, 'project_time'),
                    'description' => Arr::get($project, 'description'),
                ], Arr::get($experience, 'projects', [])),
                'skills' => array_filter($skills),
            ];
        }

        return $profileExp;
    }
}
