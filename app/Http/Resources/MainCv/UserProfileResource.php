<?php

namespace App\Http\Resources\MainCv;

use App\Models\UserProfile;
use Illuminate\Support\Arr;
use Str;

/**
 * @mixin UserProfile
 */
class UserProfileResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $isUnlocked = $this->user->searchCandidate->is_unlocked;

        return [
            'id' => $this->id,
            'fullname' => $isUnlocked ? $this->user->display_name : $this->maskName($this->user->display_name),
            'email' => $isUnlocked ? $this->user->email : Str::mask($this->user->email, '*', 0),
            'phone' => $isUnlocked ? $this->user->phone : Str::mask($this->user->phone, '*', 0),
            'avatar_url' => $this->avatar_url,
            'years_of_exp' => $this->years_of_exp,
            'status' => $this->status,
            'address' => $isUnlocked ? $this->address : Str::mask($this->address, '*', 0),
            'educations' => array_filter(array_map(fn ($education) => [
                'schoolname' => Arr::get($education, 'school_name'),
                'degree' => Arr::get($education, 'degree'),
                'year_from' => $this->parseDate(Arr::get($education, 'from')),
                'year_to' => $this->parseDate(Arr::get($education, 'to')),
                'is_studying_here' => empty(Arr::get($education, 'to')),
                'description' => Arr::get($education, 'description'),
            ], $this->educations)),

            'position' => $this->user->position,
            'experiences' => $this->mapExperiences($this->experiences),
            'province_code' => $this->province_code,
            'linkedin_link' => $this->linkedin_link,
            'github_link' => $this->github_link,
            'summary' => $this->summary,
            'skills' => $this->skills,
            'projects' => array_filter(array_map(fn ($project) => [
                'project' => Arr::get($project, 'project_name'),
                'project_time' => Arr::get($project, 'project_time'),
                'description' => Arr::get($project, 'description'),
            ], $this->projects)),

            'languages' => $this->languages,
            'interests' => $this->interests,
            'references' => $this->references,
            'activities' => $this->activities,
            'certificates' => $this->certificates,
            'additionals' => $this->additionals,
            'completed_sections' => $this->completed_sections,
        ];
    }

    public function mapExperiences($experiences): array
    {
        if (empty($experiences)) {
            return [];
        }
        $profileExp = [];

        foreach ($experiences as $experience) {
            $skills = !empty(Arr::get($experience, 'technical'))
                ? $this->getValidSkills(explode(',', Arr::get($experience, 'technical')), 'skills') : [];
            $profileExp[] = [
                'company' => Arr::get($experience, 'company'),
                'position' => Arr::get($experience, 'position'),
                'experience' => Arr::get($experience, 'description'),
                'year_from' => $this->parseDate(Arr::get($experience, 'from')),
                'year_to' => $this->parseDate(Arr::get($experience, 'to')),
                'is_working_here' => empty(Arr::get($experience, 'to')),
                'projects' => array_map(fn ($project) => [
                    'project' => Arr::get($project, 'project'),
                    'project_time' => Arr::get($project, 'project_time'),
                    'description' => Arr::get($project, 'description'),
                ], Arr::get($experience, 'projects', [])),
                'skills' => array_filter($skills),
            ];
        }

        return $profileExp;
    }
}
