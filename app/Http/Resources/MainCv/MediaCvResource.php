<?php

namespace App\Http\Resources\MainCv;

use App\Models\Media;
use App\Models\SearchCandidate;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * @mixin Media
 */
class MediaCvResource extends BaseResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $user_id = $this->mainCv ? $this->mainCv->user_id : $this->user->id;

        $searchCandidate = SearchCandidate::where('user_id', $user_id)->first();
        $isUnlocked = $searchCandidate->is_unlocked && !$searchCandidate->unlockedByCurrentCompany()->where('expired_at', '<', now())->exists();

        //  Email, tên, sđt, position, YOE, province, skill
        $fullName = $searchCandidate->display_name;
        $email = $searchCandidate->email;
        $phone = $searchCandidate->phone;
        $address = $searchCandidate->province_name ?? ($searchCandidate->province ? $searchCandidate->province->name : '');

        return [
            'fullname' => $isUnlocked ? $fullName : $this->maskName($fullName),
            'email' => $isUnlocked ? $email : Str::mask($email, '*', 0),
            'phone' => $isUnlocked ? $phone : Str::mask($phone, '*', 0),
            'address' => $isUnlocked ? $address : Str::mask($address, '*', 0),
            'years_of_exp' => $searchCandidate->years_of_exp ?? 0,
            'educations' => $this->mapEducations($searchCandidate->educations),
            'position' => $searchCandidate->position,
            'experiences' => $this->mapExperiences($searchCandidate->experiences),
            'linkedin_link' => $searchCandidate->linkedin,
            'github_link' => $searchCandidate->github,
            'summary' =>  $searchCandidate->summary,
            'skills' => $searchCandidate->skills,
            'projects' => $searchCandidate->projects,
            'languages' => $searchCandidate->languages,
            'interests' => $searchCandidate->interests,
            'references' => $searchCandidate->references,
            'activities' => $this->mapActivities($searchCandidate->activities),
            'certificates' => $searchCandidate->certificates,
            'additionals' => $searchCandidate->additionals,
        ];
    }

    public function mapActivities($activities): array
    {
        return collect($activities)->map(function ($activity) {
            return [
                'activity' => Arr::get($activity, 'activity'),
                'is_working_here' => Arr::get($activity, 'is_working_here'),
                'date_from' => $this->parseDate(Arr::get($activity, 'from')),
                'date_to' => $this->parseDate(Arr::get($activity, 'to')),
                'achievement' => Arr::get($activity, 'achievement'),
            ];
        })->toArray();
    }

    public function mapEducations($educations): array
    {
        return collect($educations)->map(function ($education) {
            return [
                'schoolname' => Arr::get($education, 'school_name'),
                'degree' => Arr::get($education, 'degree'),
                'year_from' => $this->parseDate(Arr::get($education, 'from')),
                'year_to' => $this->parseDate(Arr::get($education, 'to')),
                'is_studying_here' => Arr::get($education, 'is_studying_here'),
                'description' => Arr::get($education, 'description'),
            ];
        })->toArray();
    }

    public function mapExperiences($experiences): array
    {
        return collect($experiences)->map(function ($experience) {
            return [
                'company' => Arr::get($experience, 'company'),
                'position' => Arr::get($experience, 'position'),
                'experience' => Arr::get($experience, 'description'),
                'year_from' => $this->parseDate(Arr::get($experience, 'from')),
                'year_to' => $this->parseDate(Arr::get($experience, 'to')),
                'is_working_here' => Arr::get($experience, 'is_working_here'),
                'projects' => array_map(fn ($project) => [
                    'project' => Arr::get($project, 'project'),
                    'project_time' => Arr::get($project, 'project_time'),
                    'description' => Arr::get($project, 'description'),
                ], Arr::get($experience, 'projects', [])),
                'skills' => Arr::get($experience, 'skills'),
            ];
        })->toArray();
    }
}
