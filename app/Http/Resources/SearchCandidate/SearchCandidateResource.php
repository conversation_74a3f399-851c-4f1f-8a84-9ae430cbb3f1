<?php

namespace App\Http\Resources\SearchCandidate;

use App\Models\CompanyUnlockSearchCandidate;
use App\Models\SearchCandidate;
use App\Models\UserProfile;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Str;

/**
 * @mixin SearchCandidate
 */
class SearchCandidateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array
     */
    public function toArray($request): array
    {
        /** @var CompanyUnlockSearchCandidate $companyUnlockSearchCandidate */
        $companyUnlockSearchCandidate = $this->companyUnlockSearchCandidates()->latest()->first();

        return [
            'id' => $this->id,
            'fullname' => $this->is_unlocked
                ? $this->display_name
                : $this->maskName($this->display_name),
            'email' => $this->is_unlocked
                ? $this->email
                : Str::mask($this->email, '*', 0),
            'phone' => $this->is_unlocked
                ? $this->phone
                : Str::mask($this->phone, '*', 0),
            'summary'   => $this->summary,
            'current_job' => $this->position,
            'credit' => $this->credit,
            'skills' => Arr::get($this->skills, 'technical_skills'),
            'address' => $this->address,
            'province' => $this->province_name ?? ($this->province ? $this->province->name : ''),
            'educations' => collect($this->educations)->map(function ($education) {
                try {
                    $education['to'] = $this->convertDate($education['to']);
                    $education['from'] = $this->convertDate($education['from']);
                } catch (Exception $exception) {
                }

                return $education;
            }),
            'experiences' => collect($this->experiences)->map(function ($experience) {
                try {
                    $experience['to'] = $this->convertDate($experience['to']);
                    $experience['from'] = $this->convertDate($experience['from']);
                } catch (Exception $exception) {
                }

                return $experience;
            }),
            'willing_to_work' => $this->user
                ? $this->user->taxonomies()->where('taxonomy', 'status_works')->willingToWork()->exists()
                : '',
            'years_of_exp' => $this->years_of_exp,

            'viewed_count' => $this->viewed_count,
            'unlocked_count' => $this->unlocked_count,
            'last_updated_at' => $this->updated_at->diffForHumans(),
            'is_saved' => $this->is_saved,
            'is_unlocked' => $this->is_unlocked,
            'is_expired' => $companyUnlockSearchCandidate && $companyUnlockSearchCandidate->expired_at < now(),
            'is_viewed' => $this->is_viewed,
            'type' => $this->type,
            'avatar_url' => $this->userProfile instanceof UserProfile ? $this->userProfile->avatar_url : null,
            'force_show_willing_to_work_status_tip' => Auth::user()->isTalentSuccessUser(),
        ];
    }

    private function maskName($name): String
    {
        return Str::of($name)
            ->explode(' ')
            ->filter()
            ->map(fn ($value) => (Str::substr(trim($value), 0, 1)))
            ->implode('. ') . '.';
    }

    private function convertDate($dateString): string
    {
        try {
            return Carbon::parse($dateString)->format('Y-m');
        } catch (Exception $exception) {
        }

        return $dateString;
    }
}
