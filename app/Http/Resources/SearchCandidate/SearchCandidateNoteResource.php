<?php

namespace App\Http\Resources\SearchCandidate;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\SearchCandidateNote
 */
class SearchCandidateNoteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'content' => $this->content,
            'user_name' => $this->createdBy->username,
            'user_id' => $this->created_by,
            'created_at' => $this->created_at->format('F j Y \a\t h:i a'),
            'updated_at' => $this->updated_at->format('F j Y \a\t h:i a'),
        ];
    }
}
