<?php

namespace App\Http\Resources\SearchCandidate;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;

/**
 * @mixin \App\Models\SearchCandidate
 */
class ShowSearchCandidateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'email' => $this->user->email,
            'phone' => $this->user->phone,
            'fullname' => $this->user->display_name,
            'current_job' => $this->user->position,
            'credit' => $this->credit,
            'skills' => Arr::get($this->userProfile->skills, 'technical_skills'),
            'address' => $this->userProfile->address,
            'educations' => $this->userProfile->educations,
            'experiences' => $this->userProfile->experiences,
            'willing_to_work' => $this->user->taxonomies()->where('taxonomy', 'status_works')->first(),
            'years_of_exp' => $this->userProfile->years_of_exp,

            'viewed_count' => $this->viewed_count,
            'unlocked_count' => $this->unlocked_count,
            'last_updated_at' => $this->updated_at->diffForHumans(),
        ];
    }
}
