<?php

namespace App\Http\Resources\MyProduct;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Company
 */
class JobPostingHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array_merge(
            $this->invoices->toArray(),
            [
                'usages' => [
                    'data' => array_map(fn($item) => [
                        'id' => $item['id'],
                        'company_id' => $item['company_id'],
                        'job_id' => $item['job_id'],
                        'job_title' => $item['job_title'],
                        'package_name' => $item['package_name'],
                        'created_at' => $item['created_at'],
                        'job_detail' => $item['job_detail'],
                    ], $this->jobPostingLogs->items()),
                    'current_page' => $this->jobPostingLogs->currentPage(),
                    'last_page' => $this->jobPostingLogs->lastPage(),
                ]
            ],
        );
    }
}
