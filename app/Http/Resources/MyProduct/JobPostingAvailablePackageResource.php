<?php

namespace App\Http\Resources\MyProduct;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Company
 */
class JobPostingAvailablePackageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $resource = $this->resource;
        return [
            'value' => $resource['ams_package_id'] . ':' . $resource['invoice_id'] . ':' . ($resource['free_package'] ? 1 : 0),
            'is_free_package' => $resource['free_package'],

            'po_number' => $resource['po_number'],
            'label' => $resource['description'],
            'expired_at' => $resource['expired_at'],
        ];
    }
}
