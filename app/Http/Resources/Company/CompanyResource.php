<?php

namespace App\Http\Resources\Company;

use App\Helpers\FeatureFlag;
use App\Http\Resources\AddressResource;
use App\Http\Resources\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int $id
 * @property string $display_name
 * @property array $faqs
 * @property array $industries_ids
 * @property array $skills_ids
 * @property array $benefits
 * @property array $image_galleries
 * @property string $image_cover
 * @property string $num_employees
 * @property array $addresses
 * @property array $social_network
 * @property array $products
 * @property array $nationalities
 * @property string $tagline
 * @property string $website
 * @property string $description
 * @property string $detail_url
 * @property string $slug
 * @property string $image_logo
 * @property string $erc_file
 * @property int $status
 */
class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $company = [
            'id' => $this->id,
            'display_name' => $this->display_name,
            'image_logo' => MediaResource::make($this->image_logo),
            'slug' => $this->slug,
            'detail_url' => $this->detail_url,
            'description' => $this->description,
            'website' => $this->website,
            'tagline' => $this->tagline,
            'nationalities' => $this->nationalities,
            'products' => CompanyProductResource::collection($this->products),
            'social_network' => $this->social_network,
            'addresses' => AddressResource::collection($this->addresses),
            'num_employees' => $this->num_employees,
            'image_cover' => MediaResource::make($this->image_cover),
            'image_galleries' => MediaResource::collection($this->image_galleries),
            'benefits' => collect($this->benefits)->pluck('value')->implode('<br>') ?? "",
            'skills_ids' => $this->skills_ids,
            'industries_ids' => $this->industries_ids,
            'faqs' => $this->faqs,
            'erc_file' => MediaResource::make($this->erc_file),
            'status' => $this->status,
        ];

        return $company;
    }
}
