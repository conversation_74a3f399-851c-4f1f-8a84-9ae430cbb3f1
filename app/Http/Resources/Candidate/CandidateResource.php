<?php

namespace App\Http\Resources\Candidate;

use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin Candidate
 */
class CandidateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray($request): array
    {
        /**
         * @var Candidate $this
         */

        $meta = $this->resume->meta;

        $yearOfExperience = null;
        if ($meta) {
            $yearOfExperience = $meta->where('key', 'years_of_exp')->first();
        }
        $yearOfExperience = str_replace(',', '.', $yearOfExperience ? $yearOfExperience->value : 0);

        return [
            'id' => $this->id,
            'full_name' => $this->resume->display_name,
            'phone' => $this->getResumePhone(),
            'email' => $this->getResumeEmail(),
            'location' => $this->getResumeLocation(),
            'job_title' => $this->job->title,
            'skills' => $this->resume->taxonomies->where('taxonomy', 'skills')->pluck('term.name'),
            'applied_at' => optional($this->sent_employer_at ?? $this->created_at)->format('H:i:s d-m-Y'),
            'applied_status' => $this->getStatusText(),
            'has_cover' => !empty($this->cover_letter),
            'employer_note' => $this->employer_note,
            'job_detail_url' => $this->job->detail_url,
            'download_cv_url' => $this->download_cv_url,
            'read_status' => !empty($this->employer_read_at),
            'cover_letter' => $this->cover_letter,
            'recalled_at' => $this->recalled_at,
            'is_remove_cv' => $this->is_remove_cv,
            'is_resume_exists' => $this->resume->exists,
            'view_cv_url' => optional($this->cvMedia)->getUrl(),
            'procedure_status' => $this->procedure_status, /* @phpstan-ignore-line ignore for now, when doing procedure feature, please check */
            'cv_profile_experiences' => $this->cv_profile_experiences,
            'cv_profile_educations' => $this->cv_profile_educations,
            'avg_skill_match' => $this->avg_skill_match,
            'matching_status' => $this->avg_skill_match >= 1 ? 1 : 0,
            'yoe' => (float) $yearOfExperience,
        ];
    }

    /**
     * @return string|null
     */
    public function getResumePhone(): ?string
    {
        if ($this->is_remove_cv) {
            return Str::mask($this->resume->phone, '*', 2);
        }

        return $this->resume->phone;
    }

    /**
     * @return string|null
     */
    public function getResumeLocation(): ?string
    {
        if ($this->is_remove_cv) {
            return Str::mask(substr($this->getResumeProvinceName(), 0, 10), '*', 0);
        }

        return $this->getResumeProvinceName();
    }

    /**
     * @return string|null
     */
    public function getResumeEmail(): ?string
    {
        if ($this->is_remove_cv) {
            return Str::maskEmail($this->resume->email);
        }

        return $this->resume->email;
    }

    /**
     * @return string|null
     */
    public function getResumeProvinceName(): ?string
    {
        return $this->resume->addresses->pluck('province.name')->implode(', ');
    }
}
