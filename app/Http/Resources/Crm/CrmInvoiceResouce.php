<?php

namespace App\Http\Resources\Crm;

use App\Models\Taxonomy;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Company
 */
class CrmInvoiceResouce extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request = null)
    {
        $resource = $this->resource['data'] ?? [];
        $invoices = $resource['invoices'] ?? [];
        $packages = $resource['summary']['packages'] ?? [];
        return [
            'invoices' => [
                'data' => collect($invoices['data'] ?? [])->map(fn ($invoice) => [
                    'invoice' => $invoice['invoice_number'] ?? '',
                    'total' => $invoice['total_packages'] ?? 0,
                    'paid_at' => $invoice['purchase_date'] ?? '',
                    'search_packages' => collect($invoice['packages'] ?? [])->map(fn ($package) => [
                        'id' => $package['id'] ?? '',
                        'name' => $package['package_name'] ?? '',
                        'qty' => $package['qty'] ?? 0,
                        'used' => $package['used'] ?? 0,
                        'remain' => $package['remain'] ?? 0,
                        'free_package' => $package['free_package'] ?? false,
                        'expired_at' => $package['use_expired_at'] ?? '',
                    ]),
                ]),
                'current_page' => $invoices['current_page'] ?? 1,
                'last_page' => $invoices['last_page'] ?? 1,
            ],
            'summary' => [
                'total' => $resource['summary']['total'] ?? 0,
                'top_job' => $packages[Taxonomy::JOB_TOP_JOB_PACKAGE] ?? 0,
                'distinction' => $packages[Taxonomy::JOB_DISTINCTION_JOB_PACKAGE] ?? 0,
                'basic_plus' => $packages[Taxonomy::JOB_BASIC_PLUS_JOB_PACKAGE] ?? 0,
                'basic' => $packages[Taxonomy::JOB_BASIC_JOB_PACKAGE] ?? 0,
            ]
        ];
    }
}
