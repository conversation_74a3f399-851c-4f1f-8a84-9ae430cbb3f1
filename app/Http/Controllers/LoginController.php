<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Laravel\Socialite\Facades\Socialite;
use Log;
use Session;

class LoginController extends Controller
{
    /**
     * Redirect the user to the Passport server's authentication page.
     */
    public function redirectToProvider()
    {
        return Socialite::driver('laravelpassport')->redirect();
    }

    /**
     * Obtain the user information from the Passport server.
     */
    public function handleProviderCallback(): \Illuminate\Http\RedirectResponse
    {
        try {
            $user = Socialite::driver('laravelpassport')->user();
            Auth::guard('web')->loginUsingId($user->id, true);

            // Set Token cookie for Frontend
            Cookie::queue(cookie(config('topdev.cookie.token.name'), $user->token)->withHttpOnly(false));
        } catch (Exception $exception) {
            Auth::logout();
            Session::regenerate();

            Log::error('Exception occurred: ' . $exception->getMessage());
        }

        return redirect()->to('/');
    }

    public function logout(): \Illuminate\Http\RedirectResponse
    {
        Auth::guard('web')->logout();
        Session::regenerate();

        return redirect()->to(config('oauth2-sso.oauthconf.urlLogout'));
    }

    public function getToken(Request $request)
    {
        return encrypt($request->cookie(config('session.cookie')));
    }
}
