<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class HealthController extends Controller
{

    public function liveness()
    {
        return [
            'name' => config('app.name'),
            'env' => config('app.env'),
        ];
    }

    public function livenessDatabase()
    {
        $database = [];
        foreach ([config('database.default')] as $connection) {
            try {
                $database[$connection] = DB::connection($connection)->selectOne('select version()');
            } catch (\Exception $e) {
                $database[$connection] = $e->getMessage();
            }
        }
        return [
            'database' => $database,
            // 'connections' => config('database.connections'),
        ];
    }

    public function livenessRedis()
    {
        try {
            /** @var \Illuminate\Redis\Connections\PredisClusterConnection $connection */
            $nodes = [];
            $connection = Redis::connection();
            if ($connection instanceof \Illuminate\Redis\Connections\PredisClusterConnection) {
                /** @var \Predis\Client $node */
                foreach ($connection->client() as $node) {
                    $nodes[] = $node->ping('Keyspace');
                    $nodes[] = $node->info('Keyspace');
                }
                return $nodes;
            }

            $connection->set('ping', random_int(0, 100));
            return [
                'ping' => $connection->get('ping'),
            ];

        } catch (\Exception $e) {
            return response([$e->getMessage()], 500);
        }

    }

    public function livenessElasticsearch()
    {
        /** @var \Elastic\Elasticsearch\Client $client */
        $client = app(\Elastic\Elasticsearch\Client::class);
        $response = $client->info();
        return response($response->asArray(), $response->getStatusCode());
    }

    public function readiness(Request $request)
    {
        return [
            'database.default' => DB::connection()->selectOne('select version()'),
            'client_ips' => $request->getClientIps()
        ];
    }

    public function phpinfo(Request $request)
    {
        abort_unless(in_array($request->getClientIp(), config('topdev.trust.ips')), 404);

        if ($request->get('hostname') == $_SERVER['HOSTNAME']) {
            phpinfo($request->get('flags', 32));
        } else {
            return [
                array_filter($_SERVER, function ($k) {
                    return str_starts_with($k, 'HTTP_')
                        || $k === 'REMOTE_ADDR'
                        || $k === 'REQUEST_SCHEME'
                        ;
                }, ARRAY_FILTER_USE_KEY),
            ];
        }

    }


}
