<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Candidate\UpdateCandidateRequest;
use App\Http\Resources\Candidate\CandidateResource;
use App\Jobs\EmployerDash\AMSProcessCandidateForEmployerDash;
use App\Models\Candidate;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Gate;

class UpdateCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UpdateCandidateRequest  $request
     * @param $id
     * @return CandidateResource
     *
     * @throws AuthorizationException
     */
    public function __invoke(UpdateCandidateRequest $request, $id): CandidateResource
    {
        $candidate = Candidate::find($id);
        Gate::authorize('update', $candidate);

        $updateKeyMapping = [
            // model key <=> request key OR
            // model/request key if have the same key
            'employer_note' => 'note',
            'procedure_status',
        ];
        $updateData = [];

        // Loop all mapping fields to preapre update data
        foreach ($updateKeyMapping as $key => $requestKey) {
            // Request key and value have the same key
            $modelKey = is_numeric($key) ? $requestKey : $key;
            if ($request->has($requestKey)) {
                $updateData[$modelKey] = $request->get($requestKey);
            }
        }

        // Make sure at least 1 record to update
        if (count($updateData)) {
            $candidate->update($updateData);
            AMSProcessCandidateForEmployerDash::dispatch($candidate->id);
        }

        return CandidateResource::make($candidate);
    }
}
