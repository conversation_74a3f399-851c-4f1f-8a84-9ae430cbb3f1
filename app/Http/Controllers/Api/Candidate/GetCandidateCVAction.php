<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Candidate\GetCandidateCVRequest;
use App\Models\Candidate;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class GetCandidateCVAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  GetCandidateCVRequest  $request
     * @param $candidateId
     * @return JsonResponse
     *
     * @throws AuthorizationException
     */
    public function __invoke(GetCandidateCVRequest $request, $candidateId): JsonResponse
    {
        $candidate = Candidate::find($candidateId);
        Gate::authorize('download-cv', $candidate);

        $randomKey = md5(Str::random());
        Cache::put('download-cv-candidate-' . $candidateId, $randomKey, 120); // 120 sec

        return response()->json([
            'data' => [
                'candidate_id' => $candidateId,
                'download_url' => route('candidates.download-cv', ['id' => $candidateId, 'key' => $randomKey]),
            ],
        ]);
    }
}
