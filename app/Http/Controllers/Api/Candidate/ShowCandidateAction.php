<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Resources\Candidate\CandidateResource;
use App\Jobs\EmployerDash\AMSProcessCandidateForEmployerDash;
use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

class ShowCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @param $id
     *
     * @return CandidateResource
     */
    public function __invoke(Request $request, $id)
    {
        $candidate = Candidate::with('job')->find($id);
        Gate::authorize('view', $candidate);

        if (empty($candidate->employer_read_at)) {
            $candidate->update(['employer_read_at' => now()]);

            AMSProcessCandidateForEmployerDash::dispatch($candidate->id);
        }

        return CandidateResource::make($candidate);
    }
}
