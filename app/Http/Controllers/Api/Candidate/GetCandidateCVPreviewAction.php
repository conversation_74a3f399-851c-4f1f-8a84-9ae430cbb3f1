<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class GetCandidateCVPreviewAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @param $candidateId
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request, $candidateId): JsonResponse
    {
        /** @var Candidate $candidate */
        $candidate = Candidate::find($candidateId);
        Gate::authorize('preview-cv', $candidate);

        $randomKey = md5(Str::random());
        Cache::put('preview-cv-candidate-' . $candidateId, $randomKey);

        return response()->json([
            'data' => [
                'candidate_id' => $candidateId,
                'preview_url' => !is_null($candidate->cvMedia) ? route('candidates.preview-cv', ['id' => $candidateId, 'key' => $randomKey]) : null,
                'type' => !is_null($candidate->cvMedia) ? (Str::endsWith($candidate->cvMedia->file_name, '.pdf') ? 'pdf' : 'docx') : null,
            ],
        ]);
    }
}
