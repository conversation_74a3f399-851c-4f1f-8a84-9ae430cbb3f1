<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Candidate\GetCandidateRequest;
use App\Http\Resources\Candidate\CandidateResource;
use DB;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class GetCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param GetCandidateRequest $request
     *
     * @return AnonymousResourceCollection
     *
     * @throws AuthorizationException
     */
    public function __invoke(GetCandidateRequest $request): AnonymousResourceCollection
    {
        $candidatesQuery = $request->user()->company->candidates()
            ->withStatusReadyAndUnqualifiedAndRecall()
            ->with([
                'job',
                'resume.taxonomies',
                'resume.taxonomies.term',
                'resume.addresses.province',
                'cvMedia',
                'cvProfile',
            ])
            ->orderSentEmployerOrCreated();

        $candidatesQuery = static::filterCandidateByRequest($request, $candidatesQuery);

        return CandidateResource::collection(
            $candidatesQuery->paginate($request->get('page_size', 10))->onEachSide(1)
        );
    }

    /**
     * Build search params based on request on the client. IT'S SHARED with export function
     * Double check if change in here.
     *
     * @param Request $request
     * @param $candidatesQuery
     *
     * @return mixed
     */
    public static function filterCandidateByRequest(Request $request, $candidatesQuery)
    {
        // Filter by job id
        $request->whenFilled('job_id', fn($jobId) => $candidatesQuery->where('job_id', $jobId));

        // Filter by location
        $request->whenFilled(
            'location_id',
            fn($provinceId) => $candidatesQuery->whereHas(
                'resume.addresses.province',
                fn($query) => $query->where('code', $provinceId)
            )
        );

        // Filter applied date from
        $request->whenFilled(
            'applied_date_from',
            fn($dateFrom) => $candidatesQuery
                ->whereRaw(
                    'IFNULL(candidates.sent_employer_at, candidates.created_at) >= ?',
                    Carbon::createFromFormat('Y-m-d', $dateFrom)->startOfDay()->toDateTimeString()
                )
        );

        // Filter applied date to
        $request->whenFilled(
            'applied_date_to',
            fn($dateTo) => $candidatesQuery
                ->whereRaw(
                    'IFNULL(candidates.sent_employer_at, candidates.created_at) <= ?',
                    Carbon::createFromFormat('Y-m-d', $dateTo)->endOfDay()->toDateTimeString()
                )
        );

        // Filter by procedure status
        $request->whenFilled(
            'procedure_status',
            fn($procedureStatus) => $candidatesQuery->hasProcedureStatus($procedureStatus)
        );

        // Filter by skills id
        $request->whenFilled(
            'skills_id',
            fn($skills_id) => $candidatesQuery->whereHas(
                'resume.taxonomies',
                fn($query) => $query->where('taxonomy_id', explode(',', $skills_id))
            )
        );

        // Filter by query
        $request->whenFilled(
            'query',
            fn($query) => $candidatesQuery->where(
                function ($builder) use ($query) {
                    $builder->whereHas('resume', function ($builder) use ($query) {
                        $builder->where('email', 'LIKE', '%' . $query . '%')
                            ->orWhere('phone', 'LIKE', '%' . $query . '%')
                            ->orWhere('display_name', 'LIKE', '%' . $query . '%');
                    });

                    $builder->orWhereHas('job', function ($builder) use ($query) {
                        $builder->where('title', 'LIKE', '%' . $query . '%');
                    });
                }
            )
        );

        // Filter by matching status
        $request->whenFilled(
            'matching_status',
            fn($matchingStatus) => $candidatesQuery->where(
                function ($builder) use ($matchingStatus) {
                    if ($matchingStatus == 1) {
                        $builder->where('avg_skill_match', '>=', 1);
                    } else {
                        $builder->where('avg_skill_match', '<', 1);
                    }
                }
            )
        );
        // Filter by YOE
        $request->whenFilled(
            'experience_ids',
            fn($yoe) => $candidatesQuery->where(
                function ($builder) use ($yoe) {
                    $experienceIds = Str::of($yoe)
                        ->explode(',')
                        ->map(fn($item) => trim($item))
                        ->map(fn($item) => $item === '' ? null : (int)$item)
                        ->unique();
                    // Handle years_of_exp key in meta
                    $builder->whereHas('resume.meta', function ($builder) use ($experienceIds) {
                        $builder->where('key', 'years_of_exp')
                            ->where(function ($query) use ($experienceIds) {
                                // Case for -1: value is null or less than 1
                                if ($experienceIds->contains(-1)) {
                                    $query->whereNull('value')
                                        ->orWhere(DB::raw('CAST(value AS DECIMAL(10, 2))'), '<', 1);
                                }

                                // Case for 11: value is greater than 10
                                if ($experienceIds->contains(11)) {
                                    $query->orWhere(DB::raw('CAST(value AS DECIMAL(10, 2))'), '>', 10);
                                }

                                // Normal case: whereIn, exclude -1 and 11
                                $normalExperienceIds = $experienceIds->filter(fn($value) => !is_null($value) && $value !== -1 && $value !== 11);
                                if ($normalExperienceIds->isNotEmpty()) {
                                    $query->orWhereIn(DB::raw('CAST(value AS DECIMAL(10, 2))'), $normalExperienceIds->toArray());
                                }
                            });
                    });
                    // Case for -1: No record in meta
                    if ($experienceIds->contains(-1)) {
                        $builder->orWhereDoesntHave('resume.meta', function ($query) {
                            $query->where('key', 'years_of_exp');
                        });
                    }
                }
            )
        );
        return $candidatesQuery;
    }
}
