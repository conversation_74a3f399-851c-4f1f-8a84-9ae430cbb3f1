<?php

namespace App\Http\Controllers\Api\Candidate;

use App\Exports\CandidateExport;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ExportCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $randomKey = md5(Str::random());
        Cache::put('export-list-candidates-for-employer-' . $randomKey, $randomKey, 300); // 5 minutes

        (new CandidateExport($request->filled('search') ? $request->search : ''))->store('candidates-' . $randomKey . '.xlsx', 'tmp');

        return response()->json([
            'data' => [
                'download_url' => route('candidates.download', ['key' => $randomKey]),
            ],
        ]);
    }
}
