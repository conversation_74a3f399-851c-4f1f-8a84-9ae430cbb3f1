<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\GetCreditHistoryRequest;
use App\Http\Resources\Company\CreditHistoryResource;

class GetCreditHistoryAction extends Controller
{
    /**
     * Handle the get company profile information request.
     *
     * @param  GetCreditHistoryRequest  $request
     *
     * @return CreditHistoryResource
     */
    public function __invoke(GetCreditHistoryRequest $request)
    {
        $company = $request->user()->company;

        $perPagePackage = $request->input('per_page_package', 5);
        $pagePackage = $request->input('page_package', 1);

        $perPageUsage = $request->input('per_page_usage', 10);
        $pageUsage = $request->input('page_usage', 1);

        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $packageOrderBy = $request->input('package_order_by', 'ASC');
        $packageOrderByField = $request->input('package_order_by_field', 'id');

        $company->setRelations([
            'companySearchPackage' => $company->companySearchPackage()
                ->with(['searchPackage:id,name', 'searchPackage.translations'])
                ->select([
                    'id',
                    'created_at',
                    'search_package_id',
                    'valid_at',
                    'expired_at',
                    'remain_credit',
                ])
                ->when($fromDate && $toDate, fn ($query) => $query->validBetween($fromDate, $toDate))
                ->orderBy($packageOrderByField, $packageOrderBy)
                ->orderByDesc('id')
                ->paginate($perPagePackage, ['*'], 'page', $pagePackage),
            'creditLogs' => $company->creditLogs()
                ->select([
                    'id',
                    'type',
                    'credit',
                    'candidate_name',
                    'created_by_name',
                    'created_at',
                ])
                ->when($fromDate && $toDate, fn ($query) => $query->createdBetween($fromDate, $toDate))
                ->orderByDesc('id')
                ->paginate($perPageUsage, ['*'], 'page', $pageUsage),
        ]);

        return CreditHistoryResource::make($company);
    }
}
