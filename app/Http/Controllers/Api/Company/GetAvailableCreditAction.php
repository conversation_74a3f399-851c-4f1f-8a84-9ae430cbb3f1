<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class GetAvailableCreditAction extends Controller
{
    /**
     * @return JsonResponse
     */
    public function __invoke(): JsonResponse
    {
        $company = Auth::user()->company;

        return response()->json([
            'data' => [
                'available' => (int) $company->available_credit,
            ],
        ]);
    }
}
