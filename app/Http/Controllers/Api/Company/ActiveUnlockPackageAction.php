<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\ActiveUnlockPackageRequest;
use App\Models\Company;
use App\Models\CompanySearchPackage;
use App\Notifications\NotifyGiftSearchPackageWithCustomer;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;

class ActiveUnlockPackageAction extends Controller
{
    public function __invoke(ActiveUnlockPackageRequest $request)
    {
        $row = CompanySearchPackage::find($request->company_search_package_id);
        $expiresIn = $row->searchPackage->expires_in;

        $row->update([
            'valid_at' => Carbon::now()->format('Y-m-d 00:00:00'),
            'expired_at' => Carbon::now()->addDays($expiresIn)->format('Y-m-d 00:00:00')
        ]);

        // send mail notification
        $company = Company::find($row->company_id);
        $namePackage = $row->searchPackage->translate('en')->name ?? null;
        $data = [
            'namePackage' => $namePackage,
            'from' => Carbon::now(),
            'to' =>  Carbon::now()->addDays($expiresIn),
            'totalCredit' => $row->searchPackage->credit
        ];
        Notification::send($company, new NotifyGiftSearchPackageWithCustomer($data));
        // Cleanup related items when perform activated package
        $row->cleanRelatedItems();
        return response()->json([
            'data' => $row->fresh()
        ]);
    }
}
