<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\UpdateContactInformationRequest;
use App\Http\Resources\Company\CompanyContactResource;
use App\Jobs\EmployerDash\AMSProcessCompanyForEmployerDash;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Gate;

class UpdateContactInformationAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UpdateContactInformationRequest  $request
     * @return CompanyContactResource
     *
     * @throws AuthorizationException
     */
    public function __invoke(UpdateContactInformationRequest $request): CompanyContactResource
    {
        $company = $request->user()->company;
        Gate::authorize('update', $company);

        $company->update($request->only('email', 'phone'));

        AMSProcessCompanyForEmployerDash::dispatch($company->id);

        return CompanyContactResource::make($company);
    }
}
