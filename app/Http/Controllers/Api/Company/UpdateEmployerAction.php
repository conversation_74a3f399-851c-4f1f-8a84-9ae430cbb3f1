<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\UpdateEmployerRequest;
use App\Http\Resources\Employer\EmployerResource;
use App\Jobs\EmployerDash\AMSProcessUserForEmployerDash;
use App\Models\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Gate;

class UpdateEmployerAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UpdateEmployerRequest  $request
     * @param $id
     * @return EmployerResource
     *
     * @throws AuthorizationException
     */
    public function __invoke(UpdateEmployerRequest $request, $id): EmployerResource
    {
        $employer = User::find($id);
        Gate::authorize('update-employer', [
            $request->user()->company,
            $employer,
        ]);

        $employer->update($request->only('full_name', 'position', 'email', 'phone'));

        AMSProcessUserForEmployerDash::dispatch($employer->id);

        return EmployerResource::make($employer);
    }
}
