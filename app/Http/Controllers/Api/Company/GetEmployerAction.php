<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\GetEmployerRequest;
use App\Http\Resources\Employer\EmployerResource;
use App\Models\Company;
use Illuminate\Support\Facades\Gate;

class GetEmployerAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  GetEmployerRequest  $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     *
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function __invoke(GetEmployerRequest $request)
    {
        Gate::authorize('viewAny', Company::class);

        return EmployerResource::collection(
            $request->user()->company
                ->employers()
                ->whereNotNull('approved_at')
                ->paginate($request->page_size ?? 10)
        );
    }
}
