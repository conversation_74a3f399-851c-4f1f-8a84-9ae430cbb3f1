<?php

namespace App\Http\Controllers\Api\Company;

use App\Facades\Unleash;
use App\Helpers\FeatureFlag;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\UpdateCompanyInformationRequest;
use App\Http\Resources\Company\CompanyResource;
use App\Jobs\EmployerDash\AMSProcessCompanyForEmployerDash;
use App\Models\Company;
use App\Models\CompanyProduct;
use App\Models\Taxonomy;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Exceptions\DiskDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;
use Throwable;

class UpdateCompanyInformationAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UpdateCompanyInformationRequest  $request
     * @return CompanyResource
     *
     * @throws Throwable
     */
    public function __invoke(UpdateCompanyInformationRequest $request): CompanyResource
    {
        try {
            $author = $request->user();
            $company = $this->updateCompany($author->company, $request->validated(), $author);

            AMSProcessCompanyForEmployerDash::dispatch($company->id);
        } catch (Throwable $exception) {
            /* Do something with this error */
            Log::error($exception->getCode() . ': ' . $exception->getMessage());

            throw $exception;
        }

        $company->refresh();

        return CompanyResource::make($company);
    }

    /**
     * Update the company.
     *
     * @param  Company  $company
     * @param $validated
     * @param  User  $author
     *
     * @return Company
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     * @throws Throwable
     */
    private function updateCompany(Company $company, $validated, User $author): Company
    {
        try {
            DB::beginTransaction();

            /**
             * Audit Log
             * Get Old value before update.
             */
            $oldValues = $company->getAuditCompanyResource();

            /*
             * Update company basic information
             */
            $this->updateCompanyBasicInformation(
                $company,
                Arr::only($validated, [
                    'display_name',
                    'description',
                    'website',
                    'tagline',
                    'social_network',
                    'benefits',
                    'faqs',
                ])
            );

            /*
             * Update addresses
             */
            $this->updateCompanyAddresses(
                $company,
                Arr::get($validated, 'addresses', [])
            );

            /*
             * Update taxonomies of this company
             */
            $this->updateCompanyTaxonomies(
                $company,
                Arr::only($validated, [
                    'industries_ids',
                    'skills_ids',
                    'nationalities',
                    'num_employees',
                ])
            );

            /*
             * Update media
             */
            $this->updateCompanyImageLogo(
                $company,
                Arr::get($validated, 'image_logo')
            );

            $this->updateCompanyImageCover(
                $company,
                Arr::get($validated, 'image_cover')
            );

            /*
             * Delete old galleries
             */
            $this->updateCompanyImageGalleries(
                $company,
                Arr::get($validated, 'image_galleries')
            );

            /*
             * Update products
             */
            $this->updateCompanyProducts(
                $company,
                collect(Arr::get($validated, 'products'))
            );

            /**
             * Update ERC file
             */
            $this->updateCompanyErcFile(
                $company,
                Arr::get($validated, 'erc_file')
            );

            $company->status = Company::STATUS_REVIEW;
            $company->save();

            /*
             * Audit Log
             * Get new value after update and save Audit Log
             */
            $company->refresh();
            $newValued = $company->getAuditCompanyResource();
            $company->audits()->create([
                'user_type' => $author->getMorphClass(),
                'user_id' => $author->id,
                'event' => 'updated',
                'old_values' => $oldValues,
                'new_values' => $newValued,
                'url' => request()->url(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();

            throw $exception;
        }

        return $company;
    }

    /**
     * @param  Company  $company
     * @param $imagePath
     * @param $collectionName
     * @return void
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyImage(Company $company, $imagePath, $collectionName, $filename = null): void
    {
        $extension = pathinfo($imagePath, PATHINFO_EXTENSION);
        if (is_null($filename)) {
            $filename = 'TopDev-' . Str::random() . '-' . time() . '.' . $extension;
        }
        $company
            ->addMediaFromDisk(
                'uploads/' . basename($imagePath),
                'tmp'
            )
            ->preservingOriginal()
            ->usingFileName($filename)
            ->toMediaCollection($collectionName);
    }

    /**
     * @param  Company  $company
     * @param $data
     * @return void
     */
    private function updateCompanyBasicInformation(Company $company, $data): void
    {
        $company->update(
            $data
        );
    }

    /**
     * @param  Company  $company
     * @param $taxonomies
     * @return void
     */
    private function updateCompanyTaxonomies(Company $company, $taxonomies): void
    {
        $taxonomiesNeedToKeep = $company
            ->taxonomies
            ->whereNotIn('taxonomy', Taxonomy::COMPANY_TAXONOMY_CATEGORIES);

        $company->taxonomies()->sync(
            array_merge(
                $taxonomiesNeedToKeep->pluck('id')->all(),
                Arr::flatten($taxonomies)
            )
        );
    }

    /**
     * @param  Company  $company
     * @param $imageLogoUrl
     * @return void
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyImageLogo(Company $company, $imageLogoUrl): void
    {
        if (blank($company->image_logo) || $company->image_logo->getUrl() != $imageLogoUrl) {
            $this->updateCompanyImage(
                $company,
                parse_url($imageLogoUrl, PHP_URL_PATH),
                'image_logo'
            );
        }
    }

    /**
     * @param  Company  $company
     * @param $imageCoverUrl
     * @return void
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyImageCover(Company $company, $imageCoverUrl): void
    {
        /*
         * Note: Current system do not have function delete cover image
         * So we can skip if cover url is blank
         */
        if (blank($imageCoverUrl)) {
            return;
        }

        if (blank($company->image_cover) || $company->image_cover->getUrl() != $imageCoverUrl) {
            $this->updateCompanyImage(
                $company,
                parse_url($imageCoverUrl, PHP_URL_PATH),
                'image_cover'
            );
        }
    }

    /**
     * @param  Company  $company
     * @param $imageGalleries
     * @return void
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyImageGalleries(Company $company, $imageGalleries): void
    {
        $company->clearMediaCollectionExcept(
            'image_galleries',
            $company
                ->image_galleries
                ->whereIn(
                    'id',
                    collect($imageGalleries)->pluck('id')
                )
        );

        /*
         * Create new galleries media
         */
        collect($imageGalleries)
            ->whereNull('id')
            ->each(function ($image) use ($company) {
                $this->updateCompanyImage(
                    $company,
                    parse_url(
                        Arr::get($image, 'url'),
                        PHP_URL_PATH
                    ),
                    'image_galleries'
                );
            });
    }

    /**
     * @param  Company  $company
     * @param  Collection  $products
     * @return void
     *
     * @throws DiskDoesNotExist
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    private function updateCompanyProducts(Company $company, Collection $products): void
    {
        /*
         * Delete old products
         */
        $company
            ->products()
            ->whereNotIn(
                'id',
                $products->pluck('id')->filter()
            )
            ->delete();

        /*
         * Create or update every products left
         * There are 2 step in this block
         *  1 - Make sure product persistence to DB
         *  2 - Update image of the product if exist
         */
        $products->each(function ($productData) use ($company) {
            /** @var CompanyProduct $product */
            $product = value(function () use ($company, $productData) {
                $productId = Arr::get($productData, 'id');
                if (blank($productId)) {
                    return $company->products()->create($productData);
                }

                $product = $company->products->find($productId);
                if (blank($product)) {
                    $company->products()->create(
                        Arr::except($productData, ['id'])
                    );

                    return $product;
                }

                $product->update(
                    Arr::except($productData, ['id'])
                );

                return $product;
            });

            $image = Arr::get($productData, 'image.url');
            if ($image && (blank($product->image) || $product->image->getUrl() != $image)) {
                $imagePath = parse_url($image, PHP_URL_PATH);

                $extension = pathinfo($imagePath, PATHINFO_EXTENSION);
                $product
                    ->addMediaFromDisk(
                        'uploads/' . basename($imagePath),
                        'tmp'
                    )
                    ->preservingOriginal()
                    ->usingFileName('TopDev-' . Str::random() . '-' . time() . '.' . $extension)
                    ->toMediaCollection('images');
            }
        });
    }

    private function updateCompanyAddresses(Company $company, array $addresses)
    {
        // Remove old addresses
        $company
             ->addresses()
             ->whereNotIn(
                 'id',
                 collect($addresses)
                     ->pluck('id')
                     ->filter()
             )
            ->delete();

        // Update or Create new address
        foreach ($addresses as $index => $addressData) {
            $addressId = Arr::get($addressData, 'id');
            $companyAddress = $company->addresses->find($addressId);

            // I want to save order base on index in array
            $addressData['order'] = $index;
            $addressData = Arr::only($addressData, ['ward_id', 'province_id', 'district_id', 'street', 'order']);

            if ($companyAddress) {
                $companyAddress->update($addressData);
            } else {
                $company->addresses()->create($addressData);
            }
        }
    }

    private function updateCompanyErcFile(Company $company, $ercFileUrl)
    {
        if (blank($ercFileUrl) || ($company->erc_file && $company->erc_file->getUrl() == $ercFileUrl)) {
            return;
        }

        $this->updateCompanyImage(
            $company,
            parse_url($ercFileUrl, PHP_URL_PATH),
            'erc',
            'TopDev-erc-' . Str::slug($company->display_name) . '-' . time() . '.pdf'
        );
    }
}
