<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\GetCompanyInformationRequest;
use App\Http\Resources\Company\CompanyResource;

class GetCompanyInformationAction extends Controller
{
    /**
     * Handle the get company profile information request.
     *
     * @param  GetCompanyInformationRequest  $request
     * @return CompanyResource
     */
    public function __invoke(GetCompanyInformationRequest $request): CompanyResource
    {
        return CompanyResource::make($request->user()->company);
    }
}
