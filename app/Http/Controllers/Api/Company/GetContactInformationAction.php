<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Company\GetContactInformationRequest;
use App\Http\Resources\Company\CompanyContactResource;
use App\Models\Company;
use Illuminate\Support\Facades\Gate;

class GetContactInformationAction extends Controller
{
    /**
     * Handle the incoming request.
     * @param  GetContactInformationRequest  $request
     *
     * @return CompanyContactResource
     */
    public function __invoke(GetContactInformationRequest $request)
    {
        Gate::authorize('viewAny', Company::class);

        return CompanyContactResource::make($request->user()->company);
    }
}
