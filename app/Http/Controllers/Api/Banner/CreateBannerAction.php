<?php

namespace App\Http\Controllers\Api\Banner;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Banner\CreateBannerActionRequest;
use App\Http\Resources\Banner\BannerResource;
use App\Models\BannerAction;
use App\Models\User;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Throwable;

class CreateBannerAction extends Controller
{
    /**
     * Handle the store add banner action request.
     *
     * @param  CreateBannerActionRequest  $request
     *
     * @return BannerResource
     * @throws Exception
     */
    public function __invoke(CreateBannerActionRequest $request): BannerResource
    {
        try {
            $employer = User::whereId($request->user()->id)->select('username')->first();
            BannerAction::create(array_merge(
                Arr::only($request->validated(), ['banner_id', 'action']),
                [
                    'username' => $employer->username ?? null,
                ]
            ));
        } catch (Throwable $exception) {
            Log::error($exception->getMessage());
            throw new Exception(
                'There is something wrong'
            );
        }

        return BannerResource::make($request);
    }
}
