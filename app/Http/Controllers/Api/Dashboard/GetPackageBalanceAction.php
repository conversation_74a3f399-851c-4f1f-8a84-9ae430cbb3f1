<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Dashboard\GetPackageBalanceRequest;
use App\Http\Resources\Api\Dashboard\PackageBalanceResource;
use App\Services\CrmService;

class GetPackageBalanceAction extends Controller
{

    public function __construct(
        protected CrmService $crmService
    )
    {}

    /**
     * Handle the incoming request.
     */
    public function __invoke(GetPackageBalanceRequest $request)
    {
        // Prepare some data
        $next7Day = now()->endOfDay()->addDays(7);
        $availablePackages = collect($this->crmService->getAvailablePackages($request->user()->company_id));
        $company = $request->user()->company; /** @var \App\Models\Company $company */

        return PackageBalanceResource::make([
            'premium_jobs_remain' => $availablePackages->sum('available_packages'),
            'premium_jobs_expire_next_7_days' => $availablePackages->where('use_expired_at', '<=', $next7Day->valueOf())->sum('available_packages'),
            'search_cv_credits_remain' => $company->available_credit,
            'search_cv_credits_expire_next_7_days' => $company->valid_available_credit->where('expired_at', '<=', $next7Day)->sum('remain_credit'),
        ]);
    }
}
