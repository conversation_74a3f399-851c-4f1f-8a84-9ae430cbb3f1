<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Dashboard\GetApplicationScoreCardsRequest;
use App\Http\Resources\Api\Dashboard\GetApplicationScoreCardsResource;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class GetApplicationScoreCardsAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GetApplicationScoreCardsRequest $request)
    {
        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        /** @var \App\Models\Company $company */
        $company = $request->user()->company;

        $baseQuery = $company
            ->candidates()
            ->when(count($request->job_id) > 0, function ($query) use ($request) {
                return $query->whereIn('candidates.job_id', $request->job_id);
            });

        $dateRangeQuery = $baseQuery
            ->clone()
            ->whereBetween('candidates.sent_employer_at', [$fromDate, $toDate]);

        $viewJobQuery = $company
            ->jobs()
            ->withCount([
                'views' => fn ($query) => $query->whereBetween('viewed_at', [$fromDate, $toDate])
            ])
            ->when(count($request->job_id) > 0, function ($query) use ($request) {
                return $query->whereIn('jobs.id', $request->job_id);
            });

        // Make response data
        $applicationScoreCards = [
            'total_application' => $dateRangeQuery->clone()->count(),
            'view_job' => $viewJobQuery->get()->sum('views_count'),
            'applications_today' => $baseQuery->whereBetween('candidates.sent_employer_at', [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()])->count(),
            'viewed_application' => $dateRangeQuery->clone()->whereNotNull('employer_read_at')->count(),
            'not_viewed_application' => $dateRangeQuery->clone()->whereNull('employer_read_at')->count()
        ];

        return GetApplicationScoreCardsResource::make($applicationScoreCards);
    }
}
