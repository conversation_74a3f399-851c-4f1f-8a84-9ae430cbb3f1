<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Dashboard\GetApplicationByStatusRequest;
use App\Http\Resources\Api\Dashboard\ApplicationByStatusResource;
use App\States\Candidate\ProcedureStatus\Failed;
use App\States\Candidate\ProcedureStatus\Hired;
use App\States\Candidate\ProcedureStatus\InterviewAppointment;
use App\States\Candidate\ProcedureStatus\Interviewed;
use App\States\Candidate\ProcedureStatus\NotMatching;
use App\States\Candidate\ProcedureStatus\Matched;
use App\States\Candidate\ProcedureStatus\Offer;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class GetApplicationByStatusAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GetApplicationByStatusRequest $request)
    {
        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        /** @var \App\Models\Company $company */
        $company = $request->user()->company;

        $query = $company
            ->candidates()
            ->groupBy('procedure_status')
            ->whereBetween('candidates.sent_employer_at', [$fromDate, $toDate])
            ->when(count($request->job_id) > 0, function ($query) use ($request) {
                $query->whereIn('job_id', $request->job_id);
            })
            ->select([
                'procedure_status',
                DB::raw('COUNT(*) as total'),
            ])
            ->get()
            ->toArray();

        $procedureStatus = collect($query);

        // Create resonse data
        $applicationByStatus = [];

        // Tiếp nhận
        $total = $procedureStatus->sum('total');
        $applicationByStatus[] = [
            'status' => 'all',
            'total' => $total,
        ];

        // Not match
        $applicationByStatus[] = [
            'status' => NotMatching::$name,
            'total' => $procedureStatus->where('procedure_status', NotMatching::$name)->sum('total'),
        ];

        // Matched
        $applicationByStatus[] = [
            'status' => Matched::$name,
            'total' => $procedureStatus->whereIn(
                    'procedure_status',
                    [Matched::$name, InterviewAppointment::$name, Offer::$name, Hired::$name, Failed::$name]
                )
                ->sum('total'),
        ];

        // Đã phỏng vấn
        $applicationByStatus[] = [
            'status' => Interviewed::$name,
            'total' => $procedureStatus->whereIn(
                    'procedure_status',
                    [InterviewAppointment::$name, Offer::$name, Hired::$name, Failed::$name]
                )
                ->sum('total'),
        ];

        return ApplicationByStatusResource::make($applicationByStatus);
    }
}
