<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Dashboard\GetApplicationTimeSeriesRequest;
use App\Http\Resources\Api\Dashboard\ApplicationTimeSeriesResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class GetApplicationTimeSeriesAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GetApplicationTimeSeriesRequest $request)
    {
        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        // Check if the date range is more than 30 days
        // If it is, use month as date format
        $dateFormat = '%Y-%m-%d';
        if ($fromDate->diffInDays($toDate) > 30) {
            $dateFormat = '%Y-%m';
        }

        /** @var App\Models\Company $company */
        $company = $request->user()->company;

        $query = $company
            ->candidates()
            ->groupByRaw('DATE_FORMAT(candidates.sent_employer_at, "' . $dateFormat . '")')
            ->whereBetween('candidates.sent_employer_at', [$fromDate, $toDate])
            ->when(count($request->job_id) > 0, function ($query) use ($request) {
                return $query->whereIn('candidates.job_id', $request->job_id);
            })
            ->select([
                DB::raw('DATE_FORMAT(candidates.sent_employer_at, "' . $dateFormat . '") as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(case when candidates.employer_read_at is not null then 1 else 0 end) as viewed')
            ]);

        return ApplicationTimeSeriesResource::collection($query->get());
    }
}
