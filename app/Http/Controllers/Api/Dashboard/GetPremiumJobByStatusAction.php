<?php

namespace App\Http\Controllers\Api\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Dashboard\GetPremiumJobByStatusRequest;
use App\Http\Resources\Api\Dashboard\PremiumJobByStatusResource;
use Illuminate\Support\Facades\DB;

class GetPremiumJobByStatusAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(GetPremiumJobByStatusRequest $request)
    {
        /** @var \App\Models\Company $company */
        $company = $request->user()->company;

        $jobStatuses = $company
            ->jobs()
            ->select([
                DB::raw(
                    'case when status = 3 then "open" when status = 1 then "closed" when status = 2 then "review" when status = 0 then "draft" end as text_status'
                ),
                DB::raw('count(*) as total'),
            ])
            ->groupByRaw('text_status')
            ->pluck('total', 'text_status')
            ->toArray();

        return PremiumJobByStatusResource::make([
            [
                'status' => 'open',
                'total' => $jobStatuses['open'] ?? 0,
            ],
            [
                'status' => 'closed',
                'total' => $jobStatuses['closed'] ?? 0,
            ],
            [
                'status' => 'review',
                'total' => $jobStatuses['review'] ?? 0,
            ],
            [
                'status' => 'draft',
                'total' => $jobStatuses['draft'] ?? 0,
            ],
        ]);
    }
}
