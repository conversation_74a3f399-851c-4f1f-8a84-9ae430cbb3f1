<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Services\CrmService;
use Illuminate\Http\Request;

class CheckJobPostingAvailableAction extends Controller
{
    /**
     * Handle the get company available package request.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        $company = $request->user()->company;

        return response()->json([
            'available' => $company && app(CrmService::class)->hasAvailablePackages($company->id)
        ]);
    }
}
