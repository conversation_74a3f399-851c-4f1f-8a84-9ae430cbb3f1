<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;

class CheckFreeJobPostingAvailableAction extends Controller
{
    /**
     * Handle the get company available package request.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {
        $company = auth()->user()->company;

        return response()->json([
            'hasFreeJobPostingOpened' => $company->hasFreeJobPostingOpened(),
            'hasFreeJobPostingQuota' => $company->hasFreeJobPostingQuota(),
            'freeITJobPostingOpening' => (int) $company->hasFreeITJobPostingOpening(),
            'freeJobOpeningCount' => $company->free_job_opening_count,
            'freeNonITJobPostingOpening' => max(0, Company::LIMITED_FREE_QUOTA - (int) $company->hasFreeNonITJobPostingOpening()),
        ]);
    }
}
