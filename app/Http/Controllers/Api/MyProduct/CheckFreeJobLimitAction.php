<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Job;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CheckFreeJobLimitAction extends Controller
{
    public function __invoke(): JsonResponse
    {
        $user = Auth::user();
        $company = $user->company;

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found'
            ], 404);
        }

        // Count non-IT open jobs
        $nonItJobCount = $company->jobs()
            ->where('status', Job::STATUS_OPEN)
            ->count();

        $maxJobs = Company::LIMITED_FREE_QUOTA;
        $canPost = $nonItJobCount <= $maxJobs;
        $remaining = $maxJobs - $nonItJobCount;

        return response()->json([
            'success' => true,
            'can_post' => $canPost,
            'current_count' => $nonItJobCount,
            'max_limit' => $maxJobs,
            'remaining' => $remaining
        ]);
    }
}
