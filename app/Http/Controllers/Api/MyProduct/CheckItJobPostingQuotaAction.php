<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\JobCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CheckItJobPostingQuotaAction extends Controller
{
    /**
     * Check if the company has reached the maximum number of free IT job postings
     *
     * @return JsonResponse
     */
    public function __invoke(): JsonResponse
    {
        $user = Auth::user();
        $company = $user->company;

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'Company not found',
            ], 404);
        }

        // Count the number of IT jobs posted by this company
        $itJobCount = $company->jobs()
            ->where('status', Job::STATUS_OPEN)
            ->whereHas('job_categories', function ($query) {
                $query->where('job_category_id', JobCategory::IT_CATEGORY_ID);
            })
            ->count();

        return response()->json([
            'success' => true,
            'data' => [
                'it_job_count' => $itJobCount,
            ],
        ]);
    }
}
