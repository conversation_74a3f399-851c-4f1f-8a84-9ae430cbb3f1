<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GetFreeJobQuotaAction extends Controller
{
    /**
     * Get the free job quota for the authenticated company.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return JsonResponse
     */
    public function __invoke(Request $request)
    {
        $company = $request->user()->company;

        return response()->json([
            'total_quota' => (int) $company->free_quota_used + (int) $company->remaining_free_quota,
            'used_quota' => $company->free_quota_used ?? 0,
            'remaining_quota' => (int) $company->remaining_free_quota,
        ]);
    }
}
