<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Helpers\CrmApi;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MyProduct\RequestUpgradeToPremiumRequest;
use App\Jobs\SystemTopDevMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class RequestUpgradeToPremiumAction extends Controller
{
    /**
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke(RequestUpgradeToPremiumRequest $request): JsonResponse
    {
        $email = Auth::user()->email;
        $company = Auth::user()->company;
        $packageAvailable = 0;
        $fullName = $request->get('fullname');
        $companyName = $request->get('company_name');
        $phoneNumber = $request->get('phone_number');

        try {
            $response = CrmApi::createCrmTicket([
                'subject' => "Công ty mua gói",
                'service' => 3, // value: "Đăng tin mới"
                'type_id' => 1, // ticket

                'ams_company_id' => $company->id,
                'department' => 1,
                'message' => implode(" ", [
                    $fullName,
                    $phoneNumber,
                    $companyName,
                ])
            ]);

            $company->update([
                'company_request_buy_package_ticket_id' => $response['data']['id']
            ]);

            $this->sendEmailToSale($fullName, $phoneNumber, $companyName, $response['data']['id']);
        } catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ]);
        }

        return response()->json([
            'data' => null,
        ]);
    }

    public function sendEmailToSale($fullname, $phone, $companyName, $ticketId)
    {
        $toEmails = config('constant.SALE_UPGRADE_PREMIUM_MAILS');

        if (empty($toEmails)) {
            return;
        }

        $toEmail = array_shift($toEmails);

        $paramMail = [
            'email' => $toEmail,
            'subject' => 'Yêu cầu liên hệ khách hàng có nhu cầu mua gói',
            'delay' => 0,

            'template' => 'request-buy-package',
            'mail_type' => 'mail_to_sale_customer_request_buy_package',
            'cc_mail' => $toEmails,
            'ticket_url' => rtrim(config('services.crm.base_url'), '.') . '/admin/tickets/ticket/' . $ticketId,
            'ticket_id' => $ticketId,
            'name' => $fullname,
            'phone' => $phone,
            'company_name' => $companyName,
            'param' => [
                '{name}' => $fullname,
                '{phone}' => $phone,
                '{companyName}' => $companyName,
                '{utm}' => '?utm_source=recruiter&utm_medium=email&utm_campaign=automation'
            ]
        ];

        dispatch(new SystemTopDevMail($paramMail));
    }
}
