<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Http\Resources\MyProduct\JobPostingAvailablePackageResource;
use App\Services\CrmService;
use Illuminate\Http\Request;

class GetJobPostingAvailableAction extends Controller
{
    /**
     * Handle the get company available package request.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function __invoke(Request $request)
    {
        $company = $request->user()->company;
        $availablePackages = $company ? app(CrmService::class)->getAvailablePackages($company->id, $request->get('package_id')) : [];

        // Check and update quota if not available packages
        if (empty($availablePackages) && $company->remaining_free_quota > 0) {
            $company->update(['free_quota' => 0]);
        }

        return JobPostingAvailablePackageResource::collection($availablePackages);
    }
}
