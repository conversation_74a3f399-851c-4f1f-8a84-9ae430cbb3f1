<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Helpers\CrmApi;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MyProduct\RequestFreeJobQuotaRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class RequestFreeJobQuotaAction extends Controller
{
    /**
     * Handle the free job quota request.
     *
     * @param  \App\Http\Requests\Api\MyProduct\RequestFreeJobQuotaRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(RequestFreeJobQuotaRequest $request): JsonResponse
    {
        $user = $request->user();
        $company = $user->company;

        try {
            // Create a ticket in CRM
            $response = CrmApi::createCrmTicket([
                'subject' => 'Yêu cầu thêm gói free',
                'service' => 10, // Sale
                'type_id' => 1, // ticket

                //'userid' => $user->company_id,
                'department' => 8, // HCM Sales Team 1
                'ams_company_id' => $company->id,

                'message' => sprintf(
                    "Họ tên: %s\nSố điện thoại: %s\nCông ty: %s\nLý do yêu cầu: %s",
                    $request->input('name'),
                    $user->phone,
                    $request->input('company_name'),
                    $request->input('reason')
                ),
            ]);

            if (empty($response['data']['id'])) {
                Log::error('Failed to create CRM ticket for free job quota request', [
                    'user_id' => $user->id,
                    'company_id' => $company->id,
                    'response' => $response
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại sau.'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu của bạn đã được gửi thành công. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error requesting free job quota: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'company_id' => $company->id,
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại sau.'
            ], 500);
        }
    }
}
