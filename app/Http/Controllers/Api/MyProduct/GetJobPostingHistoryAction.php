<?php

namespace App\Http\Controllers\Api\MyProduct;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\MyProduct\GetJobPostingHistoryRequest;
use App\Http\Resources\MyProduct\JobPostingHistoryResource;
use App\Services\CrmService;

class GetJobPostingHistoryAction extends Controller
{
    /**
     * Handle the get company profile information request.
     *
     * @param  GetJobPostingHistoryRequest  $request
     *
     * @return JobPostingHistoryResource
     */
    public function __invoke(GetJobPostingHistoryRequest $request)
    {
        $company = $request->user()->company;

        $perPageInvoice = $request->input('per_page_invoice', 5);
        $pageInvoice = $request->input('page_invoice', 1);

        $perPageUsage = $request->input('per_page_usage', 10);
        $pageUsage = $request->input('page_usage', 1);

        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $invoiceOrderBy = $request->input('invoice_order_by', 'ASC');
        $invoiceOrderByField = $request->input('invoice_order_by_field', 'id');

        $company->setRelations([
            'jobPostingLogs' => $company->jobPostingLogs()
                ->select('id', 'company_id', 'job_id', 'job_title', 'package_name', 'created_at')
                ->with('job:id,slug')
                ->when($fromDate && $toDate, fn ($query) => $query->createdBetween($fromDate, $toDate))
                ->orderByDesc('id')
                ->paginate($perPageUsage, ['*'], 'page', $pageUsage)
                ->through(function ($item) {
                    $item->job_detail = $item->job->detail_url;
                    return $item;
                }),
        ]);

        $company->invoices = app(CrmService::class)->getInvoiceByCompany(
            $company->id,
            [
                $fromDate,
                $toDate
            ],
            $pageInvoice,
            $perPageInvoice,
            [
                [
                    'key' => $invoiceOrderByField,
                    'direction' => $invoiceOrderBy,
                ]
            ]
        );

        return JobPostingHistoryResource::make($company);
    }
}
