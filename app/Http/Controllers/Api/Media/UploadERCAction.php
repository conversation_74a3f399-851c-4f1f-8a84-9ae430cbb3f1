<?php

namespace App\Http\Controllers\Api\Media;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Media\UploadERCRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class UploadERCAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UploadERCRequest  $request
     * @return JsonResponse
     */
    public function __invoke(UploadERCRequest $request): JsonResponse
    {
        $path = $request->file('file')->store('uploads', 'tmp');

        return response()->json([
            'url' => Storage::disk('tmp')->url($path),
            'path' => $path,
        ]);
    }
}
