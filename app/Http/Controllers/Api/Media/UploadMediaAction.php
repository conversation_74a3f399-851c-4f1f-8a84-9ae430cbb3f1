<?php

namespace App\Http\Controllers\Api\Media;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Media\UploadMediaRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UploadMediaAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  UploadMediaRequest  $request
     * @return JsonResponse
     */
    public function __invoke(UploadMediaRequest $request): JsonResponse
    {
        $path = $request->file('file')->store('uploads', 'tmp');

        return response()->json([
            'url' => Storage::disk('tmp')->url($path),
            'path' => $path,
        ]);
    }
}
