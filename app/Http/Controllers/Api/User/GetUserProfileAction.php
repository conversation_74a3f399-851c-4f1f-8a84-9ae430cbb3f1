<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\User\UserProfileResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GetUserProfileAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @return UserProfileResource
     */
    public function __invoke(Request $request): UserProfileResource
    {
        $user = User::findOrFail(Auth::id());

        return UserProfileResource::make($user);
    }
}
