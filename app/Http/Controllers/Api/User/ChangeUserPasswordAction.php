<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\ChangeUserPasswordRequest;
use App\Http\Resources\Employer\EmployerResource;

class ChangeUserPasswordAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  ChangeUserPasswordRequest  $request
     * @return EmployerResource
     */
    public function __invoke(ChangeUserPasswordRequest $request): EmployerResource
    {
        $user = $request->user();
        $user->update(['password' => bcrypt($request->new_password)]);

        return EmployerResource::make($user);
    }
}
