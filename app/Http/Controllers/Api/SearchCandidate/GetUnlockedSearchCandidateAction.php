<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Resources\SearchCandidate\SearchCandidateResource;
use DB;
use Illuminate\Http\Request;

class GetUnlockedSearchCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request  $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function __invoke(Request $request)
    {
        return SearchCandidateResource::collection(
            $request->user()
                ->company
                ->unlockedSearchCandidates()
                ->paginate()
        );
    }
}
