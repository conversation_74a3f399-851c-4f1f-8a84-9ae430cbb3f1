<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Models\CompanyUnlockSearchCandidate;
use App\Services\GoogleChatNotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CheckMaxAllowBeforeUnlockCandidateAction extends Controller
{
    /**
     * @return JsonResponse
     */
    public function __invoke(): JsonResponse
    {
        $company = Auth::user()->company;
        $maxAllowUnlock = config('candidate.max_allow_unlock');

        // get count of unlocked search candidates in this month
        $unlockedSearchCandidateCount = CompanyUnlockSearchCandidate::query()
            ->where('company_id', $company->id)
            ->whereMonth('created_at', (string) Carbon::now()->month)
            ->count();

        $isAllow = true;
        if ($unlockedSearchCandidateCount > $maxAllowUnlock) {
            $isAllow = false;
            GoogleChatNotificationService::employerBadActionNotification('unlock_candidate');
        }

        return response()->json([
            'data' => [
                'is_allow' => $isAllow,
            ],
        ]);
    }
}
