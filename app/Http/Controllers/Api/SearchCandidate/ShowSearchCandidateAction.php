<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\ShowSearchCandidateRequest;
use App\Http\Resources\SearchCandidate\SearchCandidateResource;
use App\Models\SearchCandidate;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Log;
use Throwable;

class ShowSearchCandidateAction extends Controller
{
    /**
     * @param ShowSearchCandidateRequest $request
     * @param $searchCandidateId
     * @return JsonResponse|SearchCandidateResource
     * @throws Throwable
     */
    public function __invoke(ShowSearchCandidateRequest $request, $searchCandidateId)
    {
        $searchCandidate = SearchCandidate::find($searchCandidateId);

        if (!is_null($searchCandidate->disabled_at)) {
            return response()
                ->json([
                    'errors' => [],
                    'message' => 'The candidate not found.',
                ], 422);
        }

        try {
            DB::beginTransaction();
            $searchCandidate->viewLogsByCurrentUser()->attach(Auth::id(), [
                'credit' => $searchCandidate->credit,
                'company_id' => Auth::user()->company_id,
                'created_at' => now(),
            ]);

            $searchCandidate->loadCount('viewLogs');

            SearchCandidate::withoutTimestamps(fn () => $searchCandidate->update([
                'viewed_count' => $searchCandidate->view_logs_count,
                'unique_viewed_count' => $searchCandidate->viewLogsUniqueByCompany()->count(),
            ]));
            // Reset timestamps state after updating
            $searchCandidate->timestamps = true;

            DB::commit();
        } catch (Throwable $throwable) {
            DB::rollBack();

            Log::warning('[ShowSearchCandidateAction] Failed to update search candidate view logs: ' . $throwable->getMessage());
        }

        return SearchCandidateResource::make($searchCandidate);
    }
}
