<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Services\GoogleChatNotificationService;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class CheckMaxAllowClickCandidateAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     *
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $key = 'clicks:' . sha1(Auth::id());
        $maxAttempts = config('candidate.max_allow_click_per_minute');

        // Check if the action is allowed and increment attempts.
        if (!RateLimiter::attempt(
            $key,
            $maxAttempts,
            function () {}
        )) {
            GoogleChatNotificationService::employerBadActionNotification('click_candidate');

            return response()->json(
                [
                'data' => [
                    'is_allow' => false,
                ],
            ],
                /*Response::HTTP_TOO_MANY_REQUESTS*/
            );
        }

        // If the action is allowed, proceed with your business logic
        // ...

        return response()->json([
            'data' => [
                'is_allow' => true,
            ],
        ]);
    }
}
