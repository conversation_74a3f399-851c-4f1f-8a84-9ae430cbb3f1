<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\UnlockSearchCandidateRequest;
use App\Jobs\SendEmailEmployerViewUserProfile;
use App\Models\Company;
use App\Models\CompanyCreditLog;
use App\Models\CompanySearchPackage;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class UnlockSearchCandidateAction extends Controller
{
    /**
     * @param UnlockSearchCandidateRequest $request
     * @param $searchCandidateId
     * @return JsonResponse
     * @throws \Throwable
     */
    public function __invoke(UnlockSearchCandidateRequest $request, $searchCandidateId): JsonResponse
    {
        $company = Auth::user()->company;
        $searchCandidate = SearchCandidate::find($searchCandidateId);

        DB::beginTransaction();
        try {
            $credit = Cache::get($request->key);
            $this->unlockSearchCandidates($company, $searchCandidate, $credit);

            $availablePackage = $company->available_package;
            $this->createCreditLogs($company, $searchCandidate, $availablePackage, $credit);
            $this->updateCompanyCredit($availablePackage, $credit);

            // dispatch send inform email to resumse
            $this->sendInformEmailToCandidate($company, $searchCandidate);

            Cache::forget($request->key);
            DB::commit();

            return response()->json(['data' => null], 201);
        } catch (\Exception $exception) {
            DB::rollBack();

            throw $exception;
        }
    }

    private function sendInformEmailToCandidate(Company $company, SearchCandidate $searchCandidate)
    {
        $data = [
            'name' => $searchCandidate->user->display_name,
            'email' => $searchCandidate->user->email,
            'company_name' => $company->display_name,
            'company_logo' => optional($company->image_logo)->getUrl(),
            'company_detail_link' => $company->detail_url,
            'company_address' => $company->addresses->pluck('full_address')->first(),
        ];

        dispatch(new SendEmailEmployerViewUserProfile($data));
    }

    private function unlockSearchCandidates(Company $company, SearchCandidate $searchCandidate, $credit)
    {
        $availablePackage = $company->available_package;

        $company->unlockedSearchCandidates()->attach($searchCandidate->id, [
            'user_id' => Auth::id(),
            'credit' => $credit,
            'profile_cv_id' => $searchCandidate->userProfile->id,
            'main_cv_id' => $searchCandidate->userMainCV->redacted_cv_id ?? 0,
            'expired_at' => $availablePackage->expired_at->addDays(30) ?? now()->addYear(),
            'refunded_at' => null,
            'created_at' => now(),
        ]);

        SearchCandidate::withoutTimestamps(fn () => $searchCandidate->increment('unlocked_count'));
    }

    private function createCreditLogs(Company $company, SearchCandidate $searchCandidate, CompanySearchPackage $availablePackage, $credit)
    {
        $creditLog = CompanyCreditLog::where('company_search_package_id', $availablePackage->id)->typeIn()->first();

        $company->creditLogs()->create([
            'search_package_id' => $availablePackage->search_package_id,
            'type' => CompanyCreditLog::TYPE_OUT, // expected: type OUT
            'credit' => $credit,
            'candidate_id' => $searchCandidate->id,
            'candidate_name' => $searchCandidate->user->display_name,
            'candidate_email' => $searchCandidate->user->email ?? null,
            'created_by' => Auth::id(),
            'created_by_name' => Auth::user()->display_name,
            'created_by_email' => Auth::user()->email,
            'company_search_package_id' => $creditLog->id ?? 0,
        ]);
    }

    private function updateCompanyCredit(CompanySearchPackage $availablePackage, $credit)
    {
        $availablePackage->update([
            'used_credit' => $availablePackage->used_credit + $credit,
            'remain_credit' => $availablePackage->remain_credit - $credit,
            'updated_by' => Auth::id(),
            'updated_by_email' => Auth::user()->email,
        ]);
    }
}
