<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\StoreCompanySaveSearchCandidateRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class StoreCompanySaveSearchCandidateAction extends Controller
{
    /**
     * @param StoreCompanySaveSearchCandidateRequest $request
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke(StoreCompanySaveSearchCandidateRequest $request, $searchCandidateId): JsonResponse
    {
        $company = Auth::user()->company;

        if (!empty($company->searchCandidates()->find($searchCandidateId))) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'search_candidate_id' => 'Already exists.',
                ],
            ]);
        }

        $company->searchCandidates()->attach($searchCandidateId);

        return response()->json(['data' => null], 201);
    }
}
