<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CheckCreditBeforeUnlockCandidateAction extends Controller
{
    /**
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke($searchCandidateId): JsonResponse
    {
        $company = Auth::user()->company;
        $searchCandidate = SearchCandidate::query()->find($searchCandidateId);

        return response()->json([
            'data' => [
                'inactive_packages' => (int) $company->companySearchPackage()->isInvalid()->count(),
                'available' => (int) $company->available_credit,
                'credit' => (int) $searchCandidate->credit,
                'is_unlocked' => $searchCandidate->is_unlocked,
            ],
        ]);
    }
}
