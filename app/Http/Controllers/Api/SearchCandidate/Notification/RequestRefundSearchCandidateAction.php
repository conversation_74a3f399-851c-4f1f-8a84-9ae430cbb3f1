<?php

namespace App\Http\Controllers\Api\SearchCandidate\Notification;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\RequestRefundSearchCandidateRequest;
use App\Models\CompanyUnlockSearchCandidate;
use App\Models\SearchCandidate;
use App\Notifications\RequestRefundNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Throwable;

class RequestRefundSearchCandidateAction extends Controller
{
    /**
     * @param  RequestRefundSearchCandidateRequest  $request
     * @param $searchCandidateId
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function __invoke(RequestRefundSearchCandidateRequest $request, $searchCandidateId) : JsonResponse
    {
        $reason = $request->reason;
        $company = Auth::user()->company;
        $searchCandidate = SearchCandidate::query()->find($searchCandidateId);

        $credit = CompanyUnlockSearchCandidate::query()->where('company_id', $company->id)
            ->where('search_candidate_id', $searchCandidateId)
            ->orderBy('id', 'desc')
            ->first()
            ->value('credit');

        DB::beginTransaction();
        try {
            Notification::send($company, new RequestRefundNotification(
                $company,
                Auth::user(),
                $searchCandidate,
                $credit ?? 0,
                $reason
            ));

            $creditLog = $company->creditLogs()
                ->where('candidate_id', $searchCandidateId)
                ->typeOut()->orderBy('id', 'desc')->first();
            $creditLog->update([
                'refund_requested_at' => now(),
                'refund_requested_by' => Auth::user()->id,
            ]);

            DB::commit();
        } catch (\Exception $exception) {

            DB::rollback();

            return response()->json([
                'error' => $exception->getMessage(),
            ]);
        }

        return response()->json([
            'data' => null,
        ]);
    }
}
