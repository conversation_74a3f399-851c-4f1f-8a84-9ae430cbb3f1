<?php

namespace App\Http\Controllers\Api\SearchCandidate\Notification;

use App\Http\Controllers\Controller;
use App\Models\SearchCandidate;
use App\Notifications\GoogleChatNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class SendNotificationToGoogleChatAction extends Controller
{
    /**
     * @param Request $request
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke(Request $request, $searchCandidateId): JsonResponse
    {
        $email = Auth::user()->email;
        $company = Auth::user()->company;
        $resumeCredit = 0;
        if ($searchCandidateId != 0) {
            $searchCandidate = SearchCandidate::query()->find($searchCandidateId);
            $resumeCredit = $searchCandidate->credit;
        }

        try {
            Notification::route('googleChat', config('google-chat.space'))
                ->notify(new GoogleChatNotification(
                    $email,
                    $company->display_name,
                    $company->available_credit,
                    $resumeCredit
                ));
        } catch (\Exception $exception) {
            return response()->json([
                'error' => $exception->getMessage(),
            ]);
        }

        return response()->json([
            'data' => null,
        ]);
    }
}
