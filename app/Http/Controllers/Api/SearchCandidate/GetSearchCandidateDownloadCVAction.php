<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\GetSearchCandidateDownloadCVRequest;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class GetSearchCandidateDownloadCVAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  GetSearchCandidateDownloadCVRequest  $request
     * @param  int  $searchCandidateId
     *
     * @return JsonResponse
     */
    public function __invoke(GetSearchCandidateDownloadCVRequest $request, int $searchCandidateId): JsonResponse
    {
        $searchCandidate = SearchCandidate::find($searchCandidateId);
        Gate::authorize('download-cv', $searchCandidate);

        if (!$searchCandidate instanceof SearchCandidate) {
            return response()->json([
                'errors' => [],
                'message' => 'The candidate not found.',
            ], 422);
        }

        $randomKey = md5(Str::random());
        Cache::put('download-cv-search-candidate-' . $searchCandidateId, $randomKey, 120); // 120 sec

        return response()->json([
            'data' => [
                'search_candidate_id' => $searchCandidateId,
                'download_url' => route('search-candidates.download-cv', ['id' => $searchCandidateId, 'key' => $randomKey]),
            ],
        ]);
    }
}
