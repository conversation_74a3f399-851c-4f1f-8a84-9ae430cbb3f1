<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\UnlockSearchCandidateDraftRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Str;

class UnlockSearchCandidateDraftAction extends Controller
{
    //If code = 1 -> success, code = 2 -> failed, code =3 -> resume unlocked before
    const CODE_SUCCESS = 1;

    const CODE_FAILED = 2;

    const CODE_RESUME_ALREADY_UNLOCKED = 3;

    const CODE_NOT_ENOUGHT_CREDIT = 4;

    /**
     * @param UnlockSearchCandidateDraftRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function __invoke(UnlockSearchCandidateDraftRequest $request): JsonResponse
    {
        try {
            $key = 'unlock-resume-' . Str::uuid()->toString();

            Cache::put($key, $request->credit, (60 * 60)); // 1 hour

            return response()->json(['data' => ['key' => $key]], 201);
        } catch (\Exception $exception) {
            throw $exception;
        }
    }
}
