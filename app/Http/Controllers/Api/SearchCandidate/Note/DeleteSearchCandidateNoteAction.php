<?php

namespace App\Http\Controllers\Api\SearchCandidate\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\Note\DeleteSearchCandidateNoteRequest;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DeleteSearchCandidateNoteAction extends Controller
{
    /**
     * @param DeleteSearchCandidateNoteRequest $request
     * @param $searchCandidateId
     * @param $nodeId
     * @return JsonResponse
     */
    public function __invoke(DeleteSearchCandidateNoteRequest $request, $searchCandidateId, $nodeId): JsonResponse
    {
        $searchCandidate = SearchCandidate::find($searchCandidateId);
        $note = $searchCandidate->notes()
            ->where('id', $nodeId)
            ->where('company_id', Auth::user()->company_id)
            ->first();

        if (!empty($note) && $note->delete()) {
            return response()->json(['data' => null], 204);
        }

        return response()->json([
            'message' => 'The given data was invalid.',
            'errors' => 'Not found.',
        ]);
    }
}
