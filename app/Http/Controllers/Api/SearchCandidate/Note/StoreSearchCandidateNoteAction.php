<?php

namespace App\Http\Controllers\Api\SearchCandidate\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\Note\StoreSearchCandidateNoteRequest;
use App\Http\Resources\SearchCandidate\SearchCandidateNoteResource;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class StoreSearchCandidateNoteAction extends Controller
{
    /**
     * @param StoreSearchCandidateNoteRequest $request
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke(StoreSearchCandidateNoteRequest $request, $searchCandidateId): JsonResponse
    {
        $validatedData = $request->validated();
        $searchCandidate = SearchCandidate::find($searchCandidateId);
        $note = $searchCandidate->notes()->create([
            'content' => $validatedData['content'],
            'created_by' => Auth::user()->id,
            'company_id' => Auth::user()->company_id,
        ]);

        return SearchCandidateNoteResource::make($note)
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }
}
