<?php

namespace App\Http\Controllers\Api\SearchCandidate\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\Note\UpdateSearchCandidateNoteRequest;
use App\Models\SearchCandidate;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class UpdateSearchCandidateNoteAction extends Controller
{
    /**
     * @param UpdateSearchCandidateNoteRequest $request
     * @param $searchCandidateId
     * @param $noteId
     * @return JsonResponse
     */
    public function __invoke(UpdateSearchCandidateNoteRequest $request, $searchCandidateId, $noteId): JsonResponse
    {
        $validatedData = array_merge($request->validated(), ['updated_by' => Auth::id()]);

        $searchCandidate = SearchCandidate::find($searchCandidateId);
        $note = $searchCandidate->notes()
            ->where('id', $noteId)
            ->where('company_id', Auth::user()->company_id)
            ->first();

        if (!empty($note)) {
            $note->update($validatedData);

            return response()->json(['data' => null]);
        }

        return response()->json([
            'message' => 'The given data was invalid.',
            'errors' => 'Not found.',
        ]);
    }
}
