<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Exports\SearchCandidateExport;
use App\Http\Controllers\Controller;
use Cache;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Str;

class ExportSearchCandidatesAction extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        $requestData = $request->all();

        // throw error if filter unlocked search candidates is false
        throw_unless(!empty($requestData['unlocked_only']), Exception::class, "Invalid Request");

        $randomKey = md5(Str::random());
        Cache::put('export-list-search-candidates-' . $randomKey, $randomKey, 600); // 10 minutes

        // define new job for export
        (new SearchCandidateExport($requestData['search']))->store($randomKey . '.xlsx', 'tmp');

        return response()->json([
            'data' => [
                'download_url' => route('search-candidates.download', ['key' => $randomKey])
            ]
        ]);
    }
}
