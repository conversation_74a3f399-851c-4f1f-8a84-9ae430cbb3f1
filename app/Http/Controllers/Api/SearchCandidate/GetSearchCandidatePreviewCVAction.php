<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\GetSearchCandidatePreviewCVRequest;
use App\Http\Resources\MainCv\MediaCvResource;
use App\Http\Resources\MainCv\MyResumeResource;
use App\Http\Resources\MainCv\UserProfileResource;
use App\Models\SearchCandidate;
use App\Models\UserMainCV;
use App\Services\GenPdfResumeService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GetSearchCandidatePreviewCVAction extends Controller
{
    /**
     * @param  GetSearchCandidatePreviewCVRequest  $request
     * @param $searchCandidateId
     *
     * @return Application|ResponseFactory|\Illuminate\Http\Response|BinaryFileResponse
     * @throws Throwable
     */
    public function __invoke(GetSearchCandidatePreviewCVRequest $request, $searchCandidateId)
    {
        abort_unless($request->get('key', 'TOPDEV') === Cache::pull('preview-cv-search-candidate-'
                . Auth::user()->company_id
                . $searchCandidateId
                . $request->input('type')), Response::HTTP_UNAUTHORIZED);
        $searchCandidate = SearchCandidate::find($searchCandidateId);

        return $this->getRedactedCV($searchCandidate, $request->input('type'));
    }

    /**
     * @param  SearchCandidate  $searchCandidate
     * @param  string  $type
     *
     * @return Application|ResponseFactory|\Illuminate\Http\Response|BinaryFileResponse
     * @throws Throwable
     */
    public function getRedactedCV(SearchCandidate $searchCandidate, string $type)
    {
        /**
         * @var UserMainCV $mainCv
         */
        $mainCv = $searchCandidate->user->mainCv;
        $isUnlocked = $searchCandidate->is_unlocked;
            // && !$searchCandidate->unlockedByCurrentCompany()
            //     ->where(DB::raw('date_add(company_unlock_search_candidates.expired_at, INTERVAL 30 day)'), '>=', now())
            //     ->exists();

        if (!empty($mainCv->cv)) {
            $cvType = $mainCv->getCvTypeRelation($mainCv->cv_type);

            switch ($type) {
                case 'original':
                    if ($cvType == UserMainCV::CV_TYPE_UPLOAD_CV) {
                        /** @var \App\Models\Media $mainCvMedia */
                        $mainCvMedia = $mainCv->cv;

                        if ($isUnlocked) {
                            return response()->file($mainCvMedia->getPath());
                        } elseif (!empty($mainCvMedia->redactFile())) {
                            return response()->file($mainCvMedia->redactFile()->getPath());
                        }
                    }

                    break;
                case 'topdev':
                    $cvData = json_encode('');
                    switch ($cvType) {
                        case UserMainCV::CV_TYPE_UPLOAD_CV:
                            $cvData = MediaCvResource::make($mainCv->cv)->toJson();
                            break;
                        case UserMainCV::CV_TYPE_TOPDEV_CV:
                            $cvData = UserProfileResource::make($mainCv->cv)->toJson();
                            break;
                        case UserMainCV::CV_TYPE_CV_BUILDER:
                            $cvData = MyResumeResource::make($mainCv->cv)->toJson();
                            break;
                    }

                    $genPdfService = new GenPdfResumeService();
                    $pdfContent = $genPdfService->genPdf(json_decode($cvData, true));

                    return response($pdfContent)
                        ->header('Content-Type', 'application/pdf')
                        ->header('Content-Disposition', 'inline; filename="preview_cv.pdf"');
            }
        } else {
            // get preview cv for user who has applied job but does not sign up
            $cvData = MediaCvResource::make($searchCandidate)->toJson();
            $genPdfService = new GenPdfResumeService();
            $pdfContent = $genPdfService->genPdf(json_decode($cvData, true));

            return response($pdfContent)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="preview_cv.pdf"');
        }

        throw new \Exception('CV Not Found!');
    }
}
