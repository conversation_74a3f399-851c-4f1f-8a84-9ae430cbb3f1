<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\GetSearchCandidateRequest;
use App\Http\Resources\SearchCandidate\SearchCandidateResource;
use App\Models\SearchCandidate;
use App\Models\Taxonomy;
use Elastic\Elasticsearch\Client;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\Compound\FunctionScoreQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\Joining\NestedQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\ExistsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Search;
use ONGR\ElasticsearchDSL\Sort\FieldSort;

class GetSearchCandidateAction extends Controller
{
    const SCORE = [
        'HIGH' => 25.0,
        'MID_HIGH' => 8.0,
        'MEDIUM' => 1.0,
        'LOW' => 0.5,
    ];

    /**
     * Handle the incoming request.
     *
     * @param GetSearchCandidateRequest $request
     * @return AnonymousResourceCollection
     */
    public function __invoke(GetSearchCandidateRequest $request): AnonymousResourceCollection
    {
        $keyword = $request->input('keyword') ?? [];
        $isTalentSuccessUser = Auth::user()->isTalentSuccessUser();

        // selectedCandidateID
        $selectedCandidateID = $request->input('selectedCandidateID');
        $page = $request->input('page');
        $pageSize = $request->input('page_size');

        if ($selectedCandidateID) {
            // Calculate page current
            $queryAll = $this->query($request, $keyword, $isTalentSuccessUser, $selectedCandidateID);
            $position = $queryAll->raw()['hits']['total']['value'];

            // Number page
            $page = ceil(($position) / $pageSize);
        }

        $query = $this->query($request, $keyword, $isTalentSuccessUser);

        return SearchCandidateResource::collection(
            $query->paginate(
                $request->get('page_size', 5),
                'page',
                $page
            )
        );
    }

    /**
     * Handle the incoming request.
     *
     * @return \Laravel\Scout\Builder
     */
    public function query($request, $keyword, $isTalentSuccessUser, $selectedCandidateID = 0)
    {
        return SearchCandidate::search('*', function (Client $client, Search $body) use ($request, $keyword, $isTalentSuccessUser, $selectedCandidateID) {
            /**
             * Set the total hits to true to get the total number of hits
             * instead of the default window limit.
             */
            $body->setTrackTotalHits(true);


            // Sort by field updated_at
            $body->addSort(
                new FieldSort(
                    'updated_at',
                    FieldSort::DESC
                )
            );

            if ($selectedCandidateID) {
                $updatedAt = SearchCandidate::find($selectedCandidateID)->updated_at->format('Y-m-d H:i:s');
                $body->addPostFilter(
                    new RangeQuery(
                        'updated_at',
                        [
                            'gte' => $updatedAt,
                        ]
                    )
                );
                $body->setSize(0);
            }

            /**
             * Filter for TalentSuccess users
             *
             * If the user is a TalentSuccess user, we don't need to filter
             * the searchable field because they can see all the candidates.
             */
            if ($isTalentSuccessUser) {
                $talentSuccessQuery = new BoolQuery();
                $talentSuccessQuery->add(new TermQuery('user_applied', true), BoolQuery::SHOULD);
                $talentSuccessQuery->add(new TermQuery('user_has_profile_completed', true), BoolQuery::SHOULD);
                $talentSuccessQuery->add(new TermQuery('user_has_cvbuilder_completed', true), BoolQuery::SHOULD);

                $body->addPostFilter($talentSuccessQuery);
            } else {
                $body->addPostFilter(
                    new TermQuery('searchable', true),
                );
            }

            // Filter by keyword
            if (!blank($keyword)) {
                $listKeyword = collect($keyword)
                    ->filter()
                    ->map(fn($keyword) => trim($keyword));

                // Features for developers only
                if (Auth::user()->email === '<EMAIL>') {
                    $index = $listKeyword->search(function ($keyword) {
                        return preg_match('/^id:/i', $keyword);
                    });

                    if (is_numeric($index)) {
                        // Extract and process the IDs
                        $ids = collect(explode(",", explode(":", $listKeyword[$index])[1]))
                            ->map(fn($id) => trim($id))
                            ->filter();

                        // Remove the keyword from the list
                        $listKeyword->forget($index);

                        // Add post filter if IDs exist
                        if ($ids->isNotEmpty()) {
                            $body->addPostFilter(
                                new TermsQuery('id', $ids->all())
                            );
                        }
                    }
                }

                $listKeyword->each(function ($keyword) use ($body) {
                    $body->addQuery($this->getBaseSearchQuery($keyword));
                });
            }

            // Filter saved candidates only
            $request->whenFilled('filters.saved_only', function () use ($request, $body) {
                $isSavedOnly = $request->boolean('filters.saved_only');

                if (!$isSavedOnly) {
                    return;
                }

                // Filter only saved candidate logic here
                $savedUserIds = Auth::user()->company->savedSearchCandidates->pluck('id');

                $body->addPostFilter(
                    new TermsQuery(
                        'id',
                        $savedUserIds->all()
                    )
                );
            });


            // Filter unlocked candidates only
            $request->whenFilled('filters.unlocked_only', function () use ($request, $body) {
                $isUnlockedOnly = $request->boolean('filters.unlocked_only');

                if (!$isUnlockedOnly) {
                    return;
                }

                // Filter only unlocked candidate logic here
                $unlockedUserIds = Auth::user()->company->unlockedSearchCandidates->pluck('id');

                $body->addPostFilter(
                    new TermsQuery(
                        'id',
                        $unlockedUserIds->all()
                    )
                );
            });

            // Filter by skills
            $request->whenFilled('filters.skill', function ($skillList) use ($body) {
                $boolQuery = new BoolQuery();

                $skills = collect($skillList)
                    ->unique()
                    ->filter();

                $skills->each(function ($skill) use ($boolQuery) {
                    $boolQuery->add(
                        new NestedQuery(
                            'skills.technical_skills',
                            new TermQuery(
                                'skills.technical_skills.skill_id',
                                $skill
                            )
                        )
                    );
                });

                $body->addPostFilter($boolQuery);
            });

            // Filter by working experiences
            $request->whenFilled(
                'filters.experience',
                function ($experience) use ($body) {
                    // Change element to int
                    $experience = collect($experience)->map(fn($exp) => (int)$exp);
                    // Check has 1651 -> >10 years
                    $hasMoreThan10Years = $experience->contains(1651);
                    // Mapping from EXPERIENCE_TAXONOMIES
                    $yearsOfExpList = $experience->mapWithKeys(function ($exp) {
                        return [$exp => Arr::get(Taxonomy::EXPERIENCE_TAXONOMIES, $exp)];
                    })->reject(function ($value) {
                        // Keep value 0
                        return $value === null;
                    })->values()->all();
                    // new boolQuery ES
                    $boolQuery = new BoolQuery();
                    // Condition TermsQuery $yearsOfExpList
                    if (!empty($yearsOfExpList)) {
                        $boolQuery->add(new TermsQuery('years_of_exp', $yearsOfExpList), BoolQuery::SHOULD);
                    }
                    //Has 1651 -> >10 years
                    if ($hasMoreThan10Years) {
                        $boolQuery->add(new RangeQuery('years_of_exp', ['gt' => Arr::get(Taxonomy::EXPERIENCE_TAXONOMIES, 1651)]), BoolQuery::SHOULD);
                    }
                    // Add BoolQuery filter body
                    $body->addPostFilter($boolQuery);
                }
            );

            // Filter by location
            $request->whenFilled(
                'filters.location',
                fn($provinceCode) => $body->addPostFilter(
                    new TermQuery(
                        'province_code',
                        $provinceCode
                    )
                )
            );

            // Filter by language
            $request->whenFilled(
                'filters.language',
                fn($language) => $body->addPostFilter(
                    new NestedQuery(
                        'languages',
                        new TermQuery(
                            'languages.language',
                            $language
                        )
                    )
                )
            );

            // Filter by last updated time
            if ($request->filled('filters.updated_date_from') && $request->filled('filters.updated_date_to')) {
                $body->addPostFilter(
                    new RangeQuery(
                        'updated_at',
                        [
                            'gte' => Carbon::createFromFormat('Y-m-d', $request->input('filters.updated_date_from'))->startOfDay()->toDateTimeString(),
                            'lte' => Carbon::createFromFormat('Y-m-d', $request->input('filters.updated_date_to'))->endOfDay()->toDateTimeString(),
                        ]
                    )
                );
            }

            // if $request->boolean('filters.unlocked_only') ignore block users
            // else Filter out blocked users ...
            if (!$request->boolean('filters.unlocked_only') && !$isTalentSuccessUser) {
                // (who block this company)
                $blockedUserIds = Auth::user()->company->getUserIdBlockCompany();
                if ($blockedUserIds->isNotEmpty()) {
                    $body->addPostFilter(
                        new TermsQuery(
                            'user_id',
                            $blockedUserIds->all()
                        ),
                        BoolQuery::MUST_NOT
                    );
                }
            }

            return $client->search(['index' => 'search_candidates', 'body' => $body->toArray()])->asArray();
        });
    }

    public function getBaseSearchQuery($keyword): BoolQuery
    {
        $baseSearch = new BoolQuery();

        // Current position - HIGH
        $baseSearch->add(
            $this->positionQuery($keyword),
            BoolQuery::SHOULD
        );

        // Working experience (Title position, Description, skills, project, description project) - MID_HIGH
        $baseSearch->add(
            $this->experienceQuery($keyword),
            BoolQuery::SHOULD
        );

        // Technical skill, Soft skill - MEDIUM
        $baseSearch->add(
            $this->skillQuery($keyword),
            BoolQuery::SHOULD
        );

        // Education (Major, Description) - MEDIUM
        $baseSearch->add(
            $this->educationQuery($keyword),
            BoolQuery::SHOULD
        );

        // Language - MEDIUM
        $baseSearch->add(
            $this->languageQuery($keyword),
            BoolQuery::SHOULD
        );

        // Project (Project Name, Description) - MEDIUM
        $baseSearch->add(
            $this->projectQuery($keyword),
            BoolQuery::SHOULD
        );

        // Hobby - MEDIUM
        $baseSearch->add(
            $this->interestQuery($keyword),
            BoolQuery::SHOULD
        );

        // Activity (Activity Name, Description) - MEDIUM
        $baseSearch->add(
            $this->activityQuery($keyword),
            BoolQuery::SHOULD
        );

        // Certificate(Certificate name, Description) - MEDIUM
        $baseSearch->add(
            $this->certificateQuery($keyword),
            BoolQuery::SHOULD
        );

        // More Information(Title, Description) - MEDIUM
        $baseSearch->add(
            $this->additionalQuery($keyword),
            BoolQuery::SHOULD
        );

        // Summary Introduction - MEDIUM
        $baseSearch->add(
            $this->summaryQuery($keyword),
            BoolQuery::SHOULD
        );

        // Location - LOW
        $baseSearch->add(
            $this->provinceQuery($keyword),
            BoolQuery::SHOULD
        );

        return $baseSearch;
    }

    private function positionQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(
            new FunctionScoreQuery(
                new MatchPhraseQuery(
                    'position',
                    $keyword
                ),
                ['weight' => self::SCORE['HIGH']]
            ),
            BoolQuery::SHOULD
        );

        return $query;
    }

    private function experienceQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $experienceFields = [
            'position',
            'description',
            'skills.skill_name',
            'projects.project_name',
            'projects.description',
        ];

        $keywordFields = [
            'skills.skill_name',
        ];

        foreach ($experienceFields as $field) {
            $path = 'experiences';
            $segments = explode('.', $field);

            if (count($segments) > 1) {
                $path .= '.' . implode('.', array_slice($segments, 0, -1));
            }

            $query->add(
                new FunctionScoreQuery(
                    new NestedQuery(
                        $path,
                        in_array($field, $keywordFields) ? new TermQuery(
                            'experiences.' . $field,
                            $keyword,
                            // Ignore case sensitive to search both uper/lower
                            ['case_insensitive' => true]
                        ) : new MatchPhraseQuery(
                            'experiences.' . $field,
                            $keyword
                        )
                    ),
                    ['weight' => self::SCORE['MID_HIGH']]
                ),
                BoolQuery::SHOULD
            );
        }

        return $query;
    }

    private function skillQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'skills.technical_skills',
            new TermQuery(
                'skills.technical_skills.skill_name',
                $keyword,
                // Ignore case sensitive to search both uper/lower
                ['case_insensitive' => true]
            )
        ), BoolQuery::SHOULD);

        $query->add(
            new MatchPhraseQuery(
                'skills.soft_skills',
                $keyword
            ),
            BoolQuery::SHOULD
        );

        return $query;
    }

    private function educationQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'educations',
            new MultiMatchQuery(
                [
                    'educations.degree',
                    'educations.description',
                ],
                $keyword,
                ['type' => 'phrase']
            )
        ), BoolQuery::SHOULD);

        return $query;
    }

    private function languageQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(
            new NestedQuery(
                'languages',
                new TermQuery(
                    'languages.language',
                    $keyword,
                    // Ignore case sensitive to search both uper/lower
                    ['case_insensitive' => true]
                )
            )
        );

        return $query;
    }

    private function projectQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'projects',
            new MultiMatchQuery(
                [
                    'projects.description',
                    'projects.project_name',
                ],
                $keyword,
                ['type' => 'phrase']
            )
        ), BoolQuery::SHOULD);

        return $query;
    }

    private function interestQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(
            new MatchPhraseQuery(
                'interests',
                $keyword,
            )
        );

        return $query;
    }

    private function activityQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'activities',
            new MultiMatchQuery(
                [
                    'activities.activity',
                    'activities.achievement',
                ],
                $keyword,
                ['type' => 'phrase']
            )
        ), BoolQuery::SHOULD);

        return $query;
    }

    private function certificateQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'certificates',
            new MultiMatchQuery(
                [
                    'certificates.name',
                    'certificates.description',
                ],
                $keyword,
                ['type' => 'phrase']
            )
        ), BoolQuery::SHOULD);

        return $query;
    }

    private function additionalQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(new NestedQuery(
            'additionals',
            new MultiMatchQuery(
                [
                    'additionals.additional',
                    'additionals.description',
                ],
                $keyword,
                ['type' => 'phrase']
            )
        ), BoolQuery::SHOULD);

        return $query;
    }

    private function summaryQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(
            new MatchPhraseQuery(
                'summary',
                $keyword,
            )
        );

        return $query;
    }

    private function provinceQuery($keyword): BoolQuery
    {
        $query = new BoolQuery();

        $query->add(
            new FunctionScoreQuery(
                new MatchPhraseQuery(
                    'province_name',
                    $keyword
                ),
                ['weight' => self::SCORE['LOW']]
            )
        );

        return $query;
    }

}
