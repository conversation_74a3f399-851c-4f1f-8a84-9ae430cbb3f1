<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\RemoveCompanySaveSearchCandidateRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class RemoveCompanySaveSearchCandidateAction extends Controller
{
    /**
     * @param RemoveCompanySaveSearchCandidateRequest $request
     * @param $searchCandidateId
     * @return JsonResponse
     */
    public function __invoke(RemoveCompanySaveSearchCandidateRequest $request, $searchCandidateId): JsonResponse
    {
        $company = Auth::user()->company;

        if ($company->searchCandidates()->detach($searchCandidateId)) {
            return response()->json(['data' => null], 204);
        }

        return response()->json([
            'message' => 'The given data was invalid.',
            'errors' => [
                'search_candidate_id' => 'Not found',
            ],
        ]);
    }
}
