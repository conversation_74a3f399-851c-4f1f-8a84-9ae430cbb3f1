<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\RandCodeSearchCandidateCVRequest;
use App\Models\SearchCandidate;
use App\Models\UserMainCV;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Throwable;

class RandCodeSearchCandidateCVAction extends Controller
{
    /**
     * @param  RandCodeSearchCandidateCVRequest  $request
     * @param $searchCandidateId
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function __invoke(RandCodeSearchCandidateCVRequest $request, $searchCandidateId): JsonResponse
    {
        $searchCandidate = SearchCandidate::find($searchCandidateId);

        $randomKey = md5(Str::random());
        Cache::put('preview-cv-search-candidate-'
            . Auth::user()->company_id
            . $searchCandidateId
            . $request->input('type'), $randomKey);

        return response()->json([
            'data' => [
                'search_candidate_id' => $searchCandidateId,
                'preview_url' => route('search-candidates.preview-cv', [
                    'searchCandidateId' => $searchCandidateId,
                    'key' => $randomKey,
                    'type' => $request->input('type'),
                ]),
                'preview_cv_type' => $this->getPreviewCvType($searchCandidate, $request->input('type')),
            ],
        ]);
    }

    /**
     * @param  SearchCandidate  $searchCandidate
     * @param  string  $type
     *
     * @return string
     * @throws Throwable
     */
    public function getPreviewCvType(SearchCandidate $searchCandidate, string $type): string
    {
        if ($type == 'topdev') {
            return 'pdf';
        }

        /**
         * @var UserMainCV $mainCv
         */
        $mainCv = $searchCandidate->user->mainCv;
        if (!empty($mainCv->cv)
            && $mainCv->getCvTypeRelation($mainCv->cv_type) === UserMainCV::CV_TYPE_UPLOAD_CV) {
            return Str::endsWith($mainCv->cv->file_name, '.pdf') ? 'pdf' : 'docx'; /* @phpstan-ignore-line no need to check this */
        }

        return 'pdf';
    }
}
