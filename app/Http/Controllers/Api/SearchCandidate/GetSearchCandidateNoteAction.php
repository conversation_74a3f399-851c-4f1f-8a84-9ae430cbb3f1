<?php

namespace App\Http\Controllers\Api\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SearchCandidate\GetSearchCandidateNoteRequest;
use App\Http\Resources\SearchCandidate\SearchCandidateNoteResource;
use App\Models\SearchCandidate;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GetSearchCandidateNoteAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param GetSearchCandidateNoteRequest $request
     * @param $searchCandidateId
     * @return AnonymousResourceCollection
     */
    public function __invoke(GetSearchCandidateNoteRequest $request, $searchCandidateId)
    {
        $searchCandidate = SearchCandidate::findOrFail($searchCandidateId);

        return SearchCandidateNoteResource::collection(
            $searchCandidate->notes()->where('company_id', $request->user()->company_id)
                ->orderBy('created_at', 'desc')->get()
        );
    }
}
