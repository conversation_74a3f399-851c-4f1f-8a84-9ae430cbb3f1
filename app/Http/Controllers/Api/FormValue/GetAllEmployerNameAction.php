<?php

namespace App\Http\Controllers\Api\FormValue;

use App\Http\Controllers\Controller;
use App\Http\Resources\Employer\EmployerNameResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GetAllEmployerNameAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @return AnonymousResourceCollection
     */
    public function __invoke(Request $request): AnonymousResourceCollection
    {
        /** @var Collection $employers */
        $employers = $request
            ->user()
            ->company
            ->employers()
            ->where('email', '!=', config('topdev.creator.default.email'))
            ->onlyApproved()
            ->get();

        $employers->prepend(new User([
            'id' => null,
            'display_name' => 'All users',
            'email' => 'All users',
        ]));

        $employers->push(new User([
            'id' => config('topdev.creator.default.id'),
            'email' => config('topdev.creator.default.email'),
            'display_name' => config('topdev.creator.default.name'),
        ]));

        return EmployerNameResource::collection($employers);
    }
}
