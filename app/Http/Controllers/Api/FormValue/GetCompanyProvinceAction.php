<?php

namespace App\Http\Controllers\Api\FormValue;

use App\Http\Controllers\Controller;
use App\Http\Resources\Company\CompanyProvinceResource;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GetCompanyProvinceAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request  $request
     * @return AnonymousResourceCollection
     */
    public function __invoke(Request $request): AnonymousResourceCollection
    {
        /** @var Company $company */
        $company = $request->user()->company;
        $formValues = $company
            ->addresses
            ->map(fn ($address) => $address->district)
            ->unique('id');

        return CompanyProvinceResource::collection(
            $formValues
        );
    }
}
