<?php

namespace App\Http\Controllers\Api\FormValue;

use App\Http\Controllers\Controller;
use App\Http\Resources\Candidate\CandidateProvinceResource;
use App\Models\Candidate;
use App\Models\VietnamArea;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GetCandidateProvinceAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request  $request
     * @return AnonymousResourceCollection
     */
    public function __invoke(Request $request): AnonymousResourceCollection
    {
        $user = $request->user();

        $resumeIds = Candidate::whereIn('job_id', function ($query) use ($user) {
            $query->from('jobs')
                ->select('jobs.id')
                ->where('owned_id', $user->company_id);
        })->pluck('resume_id')->all();

        $formValues = VietnamArea::whereIn('code', function ($query) use ($user, $resumeIds) {
            $query->from('addresses')
                ->select('addresses.province_id')
                ->where('addressable_type', $user->getMorphClass())
                ->whereIn('addressable_id', $resumeIds);
        })->get();

        return CandidateProvinceResource::collection($formValues);
    }
}
