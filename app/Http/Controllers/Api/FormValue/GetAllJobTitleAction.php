<?php

namespace App\Http\Controllers\Api\FormValue;

use App\Http\Controllers\Controller;
use App\Http\Resources\Job\JobTitleResource;
use App\Models\Job;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class GetAllJobTitleAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request  $request
     * @return AnonymousResourceCollection
     */
    public function __invoke(Request $request): AnonymousResourceCollection
    {
        /** @var Collection $jobs */
        $jobs = $request->user()->company->jobs()
            ->select('id', 'owned_id', 'title', 'created_at', 'published_at')
            ->whereIn('status', [Job::STATUS_OPEN, Job::STATUS_CLOSED])
            ->orderBy('created_at', 'desc')
            ->get();
        $jobs->prepend(
            new Job(['id' => null, 'title' => 'All jobs'])
        );

        return JobTitleResource::collection($jobs);
    }
}
