<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\JobSaveDraftRequest;
use App\Http\Resources\Job\JobResource;
use App\Services\JobService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Throwable;

class JobSaveDraftAction extends Controller
{
    protected JobService $jobService;

    public function __construct(JobService $jobService)
    {
        $this->jobService = $jobService;
    }

    /**
     * Handle the incoming request.
     *
     * @param  JobSaveDraftRequest  $request
     *
     * @return JobResource|JsonResponse
     */
    public function __invoke(JobSaveDraftRequest $request)
    {
        $validated = $request->all();
        try {
            $job = DB::transaction(function () use ($validated) {
                return $this->jobService->storeJob($validated);
            });
        } catch (Throwable $exception) {
            \Log::error($exception->getMessage());
            return response()->json([
                'message' => $exception->getMessage(),
            ], 500);
        }

        return JobResource::make($job);
    }
}
