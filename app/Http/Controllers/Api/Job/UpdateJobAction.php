<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\UpdateJobRequest;
use App\Http\Resources\Job\JobResource;
use App\Jobs\EmployerDash\AMSProcessJobForEmployerDash;
use App\Models\Job;
use App\Models\JobNote;
use App\Models\User;
use App\Services\JobService;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Throwable;

class UpdateJobAction extends Controller
{
    public function __construct(
        public JobService $jobService
    )
    {

    }

    /**
     * Handle the incoming request.
     *
     * @param  UpdateJobRequest  $request
     * @param $jobId
     * @return JobResource
     *
     * @throws AuthorizationException
     * @throws Exception
     */
    public function __invoke(UpdateJobRequest $request, $jobId): JobResource
    {
        $job = Job::query()->find($jobId);

        Gate::authorize('update', $job);

        $validated = $request->validated();
        unset($validated['salary']['value']);

        $job = $this->updateJob($job, $validated, $request->user());

        $action = AMSProcessJobForEmployerDash::ACTION_UPDATE;

        if ((int) $request->get('on_public') === 1) {
            $action = AMSProcessJobForEmployerDash::ACTION_FORCE_PUBLISH;
        }

        AMSProcessJobForEmployerDash::dispatch($job->id, auth()->id(), $action);

        // Think about this? does it actualy need to refresh?
        $job->refresh();

        return JobResource::make($job);
    }

    /**
     * @throws Throwable
     */
    private function updateJob(Job $job, $validated, User $author): Job
    {
        return $this->jobService->update($job, $validated);
    }

    private function setAnnouncement48h(Job $job)
    {
        $announcement = $job->announcements()->withTypeJobAnnouncement()->first();
        if ($announcement && $announcement->expires_at->lte(now())) {
            $announcement->delete();
        }

        $job->announcements()->updateOrCreate(
            ['type' => 'status_job_announcement'],
            [
                'expires_at' => now()->addDays(2),
                'title' => 'Changes in post-check. Within 48 hours from the time of change, TopDev will contact you when we need to check and confirm the information you changed, if not, changes will be approved for displaying',
                'body' => 'Changes in post-check. Within 48 hours from the time of change, TopDev will contact you when we need to check and confirm the information you changed, if not, changes will be approved for displaying',
            ]
        );
    }
}
