<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CheckRequestingPackageTicketStatusAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $company = auth()->user()->company;

        return response()->json([
            'data' => [
                'isRequestingPackageTicket' => $company->isRequestingPackageTicket()
            ]
        ]);
    }
}
