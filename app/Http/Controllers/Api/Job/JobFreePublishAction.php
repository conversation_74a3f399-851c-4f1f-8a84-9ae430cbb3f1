<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\StoreJobRequest;
use App\Http\Resources\Job\JobResource;
use App\Jobs\EmployerDash\AMSProcessJobForEmployerDash;
use App\Models\Job;
use App\Services\JobService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Throwable;

class JobFreePublishAction extends Controller
{
    protected JobService $jobService;

    public function __construct(JobService $jobService)
    {
        $this->jobService = $jobService;
    }

    /**
     * Handle the incoming request.
     *
     * @param  StoreJobRequest  $request
     *
     * @return JobResource|JsonResponse
     */
    public function __invoke(StoreJobRequest $request)
    {
        try {
            $job = DB::transaction(function () use ($request) {
                $validated = $request->validated();
                $job = $this->jobService->storeJob($validated, Job::STATUS_OPEN);
                $this->jobService->createJobAuditLog($job, $request->user());

                AMSProcessJobForEmployerDash::dispatch(
                    $job->id,
                    auth()->id(),
                    AMSProcessJobForEmployerDash::ACTION_FREE_PUBLISH
                );

                return $job;
            });
        } catch (Throwable $exception) {
            \Log::error($exception->getMessage());
            return response()->json([
                'message' => $exception->getMessage(),
            ], 500);
        }

        return JobResource::make($job);
    }
}
