<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\ShowJobRequest;
use App\Http\Resources\Job\JobResource;
use App\Models\Job;
use Illuminate\Support\Facades\Gate;

class ShowJobAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param ShowJobRequest $request
     * @param $jobId
     * @return JobResource
     */
    public function __invoke(ShowJobRequest $request, $jobId): JobResource
    {
        $job = Job::query()->find($jobId);

        Gate::authorize('view', $job);

        return JobResource::make($job);
    }
}
