<?php

namespace App\Http\Controllers\Api\Job;

use App\Exports\JobExport;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ExportJobAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $randomKey = md5(Str::random());
        Cache::put('export-list-jobs-for-employer-' . $randomKey, $randomKey, 300); // 5 minutes

        (new JobExport($request->filled('search') ? $request->search : ''))->store($randomKey . '.xlsx', 'tmp');

        return response()->json([
            'data' => [
                'download_url' => route('jobs.download', ['key' => $randomKey]),
            ],
        ]);
    }
}
