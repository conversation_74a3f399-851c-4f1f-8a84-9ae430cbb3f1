<?php

namespace App\Http\Controllers\Api\Job;

use App\Enums\JobLevelEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\StoreJobRequest;
use App\Http\Resources\Job\JobResource;
use App\Jobs\EmployerDash\AMSProcessJobForEmployerDash;
use App\Models\Job;
use App\Models\User;
use App\Services\JobService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded;
use Throwable;

class JobStoreAction extends Controller
{
    protected JobService $jobService;

    public function __construct(JobService $jobService)
    {
        $this->jobService = $jobService;
    }

    /**
     * Handle the incoming request.
     *
     * @param  StoreJobRequest  $request
     *
     * @return JobResource | JsonResponse
     *
     * @throws Throwable
     */
    public function __invoke(StoreJobRequest $request)
    {
        /*
         * Pass validated & authorization then let's
         * create a new job and fill all the value
         */
        try {
            DB::beginTransaction();

            // Create the job base on request
            $job = $this->createJob($request->validated());

            // Create audit log for job
            $this->createJobAudit($job, $request->user());

            DB::commit();

            // Send to AMS when job is not safe as draft
            if (! $request->boolean('save_draft')) {
                $action = AMSProcessJobForEmployerDash::ACTION_STORE;

                if ((int) $request->get('on_public') === 1) {
                    $action = AMSProcessJobForEmployerDash::ACTION_FORCE_PUBLISH;
                }

                AMSProcessJobForEmployerDash::dispatch($job->id, auth()->id(), $action);
            }
        } catch (Throwable $exception) {
            DB::rollBack();
            // TODO: log error, notify error, do something with this error
            Log::error($exception->getMessage());
            throw $exception;
        }

        /*
         * Many thing to do here and I think it's better to do in
         * AMS source
         */
        // TODO: welcome to vietnam

        /*
         * Finally send back the job to the user!
         * Congratulations to usersss!
         */
        return JobResource::make($job);
    }

    /**
     * @param  array  $validated
     *
     * @return Job
     *
     * @throws FileCannotBeAdded
     */
    public function createJob(array $validated)
    {
        if (isset($validated['package_id']) && isset($validated['package_id'][0]) && $validated['package_id'][0] === 'free') {
            $validated['package_id'] = [];
        }
        return $this->jobService->create($validated);
    }

    /**
     * Create an audit log for the job after creating job.
     *
     * @param Job $job
     * @param User $author
     *
     * @return void
     */
    public function createJobAudit(Job $job, User $author)
    {
        $this->jobService->createJobAuditLog($job, $author);
    }
}
