<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\ShowJobRequest;
use App\Http\Resources\Job\JobCategoryResource;
use App\Http\Resources\Job\JobResource;
use App\Models\Job;
use App\Models\JobCategory;
use Illuminate\Support\Facades\Gate;

class JobGetCategoryAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param ShowJobRequest $request
     * @param $jobId
     * @return JobResource
     */
    public function __invoke(ShowJobRequest $request): JobCategoryResource
    {
        return JobCategoryResource::make([
            'categories' => JobCategory::query()
                ->with('translations')
                ->isCategory()
                ->orderBy('sort_order')
                ->get(),

            'roles' => JobCategory::query()
                ->with('translations')
                ->isRole()
                ->orderBy('sort_order')
                ->get(),
        ]);
    }
}
