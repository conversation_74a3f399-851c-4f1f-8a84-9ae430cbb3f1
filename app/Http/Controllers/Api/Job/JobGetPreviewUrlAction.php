<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Models\Job;
use Illuminate\Http\Request;

class JobGetPreviewUrlAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $job = Job::findOrFail($request->job_id);

        if ($job->status === Job::STATUS_DRAFT) {
            return response()->json([
                'message' => 'Job is in draft status',
            ], 422);
        }

        if ($job->preview_url === null) {
            return response()->json([
                'message' => 'Preview URL not found',
                'status' => 422,
            ]);
        }

        if ($job->content_html_desktop === null || $job->content_html_mobile === null) {
            return response()->json([
                'message' => 'Job content not found',
                'status' => 422,
            ]);
        }

        return response()->json([
            'data' => [
                'preview_url' => $job->preview_url,
            ],
            'status' => 200,
        ]);
    }
}
