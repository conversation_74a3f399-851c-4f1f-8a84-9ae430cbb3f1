<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Job\GetJobRequest;
use App\Http\Resources\Job\JobCollection;

class GetJobAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  GetJobRequest  $request
     * @return JobCollection
     */
    public function __invoke(GetJobRequest $request): JobCollection
    {
        $jobsQuery = $request
            ->user()
            ->company
            ->jobs()
            ->orderByDesc('id')
            ->with([
                'addresses',
                'addresses.district',
                'information',
                'packages',
                'creator',
                'count',
                'taxonomies',
                'taxonomies.term',
                'announcements',
                'qualifiedCandidates',
            ])
            ->withCount('candidateBestMatch');
        $this->filterJobsByRequest($request, $jobsQuery);

        return JobCollection::make(
            $jobsQuery
                ->paginate($request->get('page_size', 10))
                ->onEachSide(1)
        );
    }

    private function filterJobsByRequest(GetJobRequest $request, $jobsQuery)
    {
        // Filter by location_id
        $request->whenFilled(
            'location_id',
            fn ($locationId) => $jobsQuery->whereHas(
                'addresses.district',
                fn ($query) => $query->where('code', $locationId)
            )
        );

        // Filter by job_id
        $request->whenFilled('job_id', fn ($jobId) => $jobsQuery->where('id', $jobId));

        // Filter by skills_id
        $request->whenFilled(
            'skills_id',
            fn ($skillsId) => $jobsQuery->whereHas(
                'taxonomies',
                fn ($query) => $query->whereIn('term_taxonomy.id', explode(',', $skillsId))
            )
        );

        // Filter by status
        $request->whenFilled('status', fn ($status) => $jobsQuery->where('status', $status));

        // Filter by created_by
        // Add filter by topdev creator
        $request->whenFilled(
            'created_by',
            fn ($createdBy) => $createdBy != config('topdev.creator.default.id')
                ? $jobsQuery->where('creator_id', $createdBy)
                : $jobsQuery->doesntHave('creator')
        );

        // Filter by query
        $request->whenFilled(
            'query',
            fn ($query) => $jobsQuery->where(
                function ($builder) use ($query) {
                    $builder
                        ->where('title', 'LIKE', '%' . $query . '%')
                        ->orWhere('id', 'LIKE', '%' . $query . '%');
                }
            )
        );
    }
}
