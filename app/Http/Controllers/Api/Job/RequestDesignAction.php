<?php

namespace App\Http\Controllers\Api\Job;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Services\JobService;
use Illuminate\Http\Request;

class RequestDesignAction extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, JobService $jobService)
    {
        $job = Job::findOrFail($request->job_id);

        $jobService->createRequestDesignTicket($job, $request->user());

        return response()->noContent();
    }
}
