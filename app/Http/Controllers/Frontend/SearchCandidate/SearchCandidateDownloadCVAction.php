<?php

namespace App\Http\Controllers\Frontend\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\Candidate\DownloadCVRequest;
use App\Http\Resources\MainCv\MediaCvResource;
use App\Http\Resources\MainCv\MyResumeResource;
use App\Http\Resources\MainCv\UserProfileResource;
use App\Models\Media;
use App\Models\SearchCandidate;
use App\Models\UserMainCV;
use App\Services\GenPdfResumeService;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class SearchCandidateDownloadCVAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  DownloadCVRequest  $request
     * @param  int  $searchCandidateId
     *
     * @return Application|ResponseFactory|\Illuminate\Http\Response|BinaryFileResponse
     * @throws Throwable
     */
    public function __invoke(DownloadCVRequest $request, int $searchCandidateId)
    {
        // Check key
        abort_unless(
            $request->get('key', 'TOPDEV') === Cache::get('download-cv-search-candidate-' . $searchCandidateId),
            Response::HTTP_UNAUTHORIZED
        );

        $searchCandidate = SearchCandidate::find($searchCandidateId);

        return $this->downloadCVFile($searchCandidate);
    }

    /**
     * @param  SearchCandidate  $searchCandidate
     *
     * @return Application|ResponseFactory|\Illuminate\Http\Response|BinaryFileResponse
     * @throws Throwable
     */
    public function downloadCVFile(SearchCandidate $searchCandidate)
    {
        /* @var UserMainCV $mainCv */
        $mainCv = $searchCandidate->user->mainCv;
        # $isUnlocked = $searchCandidate->is_unlocked
        #    && !$searchCandidate->unlockedByCurrentCompany()->where('expired_at', '<', now())->exists();

        # if (!$isUnlocked) {
        #     throw new Exception('CV is locked!');
        # }

        if (!empty($mainCv->cv)) {
            $cvType = $mainCv->getCvTypeRelation($mainCv->cv_type);

            if ($cvType == UserMainCV::CV_TYPE_UPLOAD_CV) {
                /** @var Media $mainCvMedia */
                $mainCvMedia = $mainCv->cv;

                if (!file_exists($mainCvMedia->getPath())) {
                    return $this->downloadUserMainCV($mainCv, $searchCandidate, $cvType);
                }

                return response()->download($mainCvMedia->getPath());
            } else {
                return $this->downloadUserMainCV($mainCv, $searchCandidate, $cvType);
            }
        }

        throw new Exception('CV Not Found!');
    }

    /**
     * @param  UserMainCV  $mainCv
     * @param  SearchCandidate  $searchCandidate
     * @param  string  $cvType
     *
     * @return Application|ResponseFactory|\Illuminate\Http\Response
     * @throws Exception
     */
    public function downloadUserMainCV(UserMainCV $mainCv, SearchCandidate $searchCandidate, string $cvType)
    {
        $cvData = json_encode('');

        switch ($cvType) {
            case UserMainCV::CV_TYPE_UPLOAD_CV:
                $cvData = MediaCvResource::make($mainCv->cv)->toJson();
                break;
            case UserMainCV::CV_TYPE_TOPDEV_CV:
                $cvData = UserProfileResource::make($mainCv->cv)->toJson();
                break;
            case UserMainCV::CV_TYPE_CV_BUILDER:
                $cvData = MyResumeResource::make($mainCv->cv)->toJson();
                break;
        }

        $genPdfService = new GenPdfResumeService();
        $pdfContent = $genPdfService->genPdf(json_decode($cvData, true));

        return response($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $searchCandidate->user->getName() . '.pdf"',
        ]);
    }
}
