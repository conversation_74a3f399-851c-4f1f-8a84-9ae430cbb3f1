<?php

namespace App\Http\Controllers\Frontend\SearchCandidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\SearchCandidate\DownloadListSearchCandidateRequest;
use Illuminate\Http\Request;
use Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DownloadListSearchCandidate extends Controller
{
    /**
     * Handle the incoming request
     *
     * @param DownloadListSearchCandidateRequest $request
     * @return BinaryFileResponse
     */
    public function __invoke(DownloadListSearchCandidateRequest $request): BinaryFileResponse
    {
        return response()->download(
            Storage::disk('tmp')->path($request->key . '.xlsx'),
            'TopDev-SearchCandidates-' . now()->format('Ymd') . '.xlsx'
        );
    }
}
