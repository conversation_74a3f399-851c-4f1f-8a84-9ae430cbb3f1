<?php

namespace App\Http\Controllers\Frontend\Job;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\Job\DownloadListJobRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class DownloadListJob extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  DownloadListJobRequest  $request
     * @return BinaryFileResponse
     */
    public function __invoke(DownloadListJobRequest $request): BinaryFileResponse
    {
        return response()->download(
            Storage::disk('tmp')->path($request->key . '.xlsx'),
            'TopDev-Jobs-' . now()->format('Ymd') . '.xlsx'
        );
    }
}
