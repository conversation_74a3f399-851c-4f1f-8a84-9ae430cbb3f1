<?php

namespace App\Http\Controllers\Frontend\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\Candidate\DownloadListCandidateRequest;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class CandidateDownloadListAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  DownloadListCandidateRequest  $request
     * @return BinaryFileResponse
     */
    public function __invoke(DownloadListCandidateRequest $request): BinaryFileResponse
    {
        return response()->download(
            Storage::disk('tmp')->path('candidates-' . $request->key . '.xlsx'),
            'TopDev-Candidates-' . now()->format('Ymd') . '.xlsx'
        );
    }
}
