<?php

namespace App\Http\Controllers\Frontend\Candidate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\Candidate\DownloadCVRequest;
use App\Models\Candidate;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

class CandidateDownloadCVAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  DownloadCVRequest  $request
     * @param $candidateId
     * @return BinaryFileResponse
     */
    public function __invoke(DownloadCVRequest $request, $candidateId): BinaryFileResponse
    {
        // Check key
        abort_unless($request->get('key', 'TOPDEV') === Cache::get('download-cv-candidate-' . $candidateId), Response::HTTP_UNAUTHORIZED);

        $candidate = Candidate::find($candidateId);

        return response()->download(
            $candidate->cvMedia->getPath()
        );
    }
}
