<?php

namespace App\Http\Controllers\Frontend\Candidate;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

class CandidatePreviewCVAction extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  Request  $request
     * @param $candidateId
     *
     * @return BinaryFileResponse
     */
    public function __invoke(Request $request, $candidateId)
    {
        abort_unless($request->get('key', 'TOPDEV') === Cache::pull('preview-cv-candidate-' . $candidateId), Response::HTTP_UNAUTHORIZED);

        $candidate = Candidate::find($candidateId);

        return response()->file(
            $candidate->cvMedia->getPath()
        );
    }
}
