<?php

namespace App\Http\Requests\Frontend\SearchCandidate;

use Cache;
use Illuminate\Foundation\Http\FormRequest;

class DownloadListSearchCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->key == Cache::pull('export-list-search-candidates-' . $this->key);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
        ];
    }
}
