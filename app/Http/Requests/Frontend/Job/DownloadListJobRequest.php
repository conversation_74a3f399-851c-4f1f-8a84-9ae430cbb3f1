<?php

namespace App\Http\Requests\Frontend\Job;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Cache;

/**
 * @property mixed $key
 */
class DownloadListJobRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return $this->key == Cache::pull('export-list-jobs-for-employer-' . $this->key);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
        ];
    }
}
