<?php

namespace App\Http\Requests\Frontend\Candidate;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Cache;

/**
 * @property mixed $key
 */
class DownloadListCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return $this->key == Cache::pull('export-list-candidates-for-employer-' . $this->key);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            //
        ];
    }
}
