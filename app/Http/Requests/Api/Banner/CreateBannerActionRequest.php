<?php

namespace App\Http\Requests\Api\Banner;

use App\Models\BannerAction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateBannerActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'banner_id' => [
                'required',
                Rule::in(BannerAction::getBannerIds()),
            ],
            'action' => [
                'required',
                Rule::in(BannerAction::getActions()),
            ],
        ];
    }
}
