<?php

namespace App\Http\Requests\Api\Job;

use App\Models\Address;
use App\Rules\Job\EnoughPackageRule;
use App\Rules\Job\PackageFormatRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class JobSaveDraftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string',
            'addresses_id' => [
                'array',
                Rule::exists(Address::class, 'id')
                    ->where('addressable_id', $this->user()->company_id),
            ],
            'package_id' => [
                'bail',
                'required',
                new PackageFormatRule(),
                new EnoughPackageRule()
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'title.required' => 'job_detail_title_required',
            'addresses_id.required' => 'job_detail_address_select',
            'package_id.required' => 'job_detail_package_select',
        ];
    }
}
