<?php

namespace App\Http\Requests\Api\Job;

use App\Enums\JobLevelEnum;
use App\Models\Address;
use App\Models\Job;
use App\Models\Taxonomy;
use App\Rules\Job\EnoughPackageRule;
use App\Rules\Job\PackageFormatRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class StoreJobRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('create', Job::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'title' => ['required'],
            'content' => ['nullable'],
            'category_id' => ['required'],
            'job_category_id' => ['array', 'required'],
            'skills_ids' => [
                'array',
                'max:5',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'skills'),
            ],
            'job_levels' => [
                'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'job_levels'),
            ],
            'experiences_ids' => [
                'array',
                'max:2',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'experiences'),
            ],
            'salary' => [
                'array',
            ],
            'job_types' => [
                'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'job_types'),
            ],
            'contract_type' => [
                'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'contract_types'),
            ],
            'addresses_id' => [
                'array',
                Rule::exists(Address::class, 'id')
                    ->where('addressable_id', $this->user()->company_id),
            ],
            'requirements' => ['array', 'required'],
            'requirements.*.name' => ['required'],
            'requirements.*.id' => ['required', 'int'],
            'requirements.*.description' => ['required'],
            'responsibilities' => ['array', 'required'],
            'responsibilities.*.id' => ['required', 'int'],
            'responsibilities.*.description' => ['required'],
            'responsibilities.*.name' => ['required'],
            'recruiment_process' => ['array', 'required'],
            'recruiment_process.*.name' => ['required'],
            'recruiment_process.*.id' => ['required', 'int'],
            'recruiment_process.*.description' => ['required'],
            'benefits' => ['array'],
            'benefits.*.name' => ['required'],
            'benefits.*.id' => ['required'],
            'benefits.*.description' => ['required'],
            'emails_cc' => ['array'],
            'note' => 'max:500',
            'education_degree' => ['array'],
            'education_major' => ['array'],
            'education_certificate' => ['nullable'],
            'job_banner' => [
                Rule::requiredIf($this->input('level') === JobLevelEnum::PAID->value),
            ],
            'job_template' => [
                Rule::requiredIf($this->input('level') === JobLevelEnum::PAID->value),
            ],
            'job_template_color' => [
                Rule::requiredIf($this->input('level') === JobLevelEnum::PAID->value),
            ],
            'company_tagline' => ['nullable'],
            'company_logo' => ['nullable'],
            'package_id' => [
                'bail',
                Rule::requiredIf($this->input('level') === JobLevelEnum::PAID->value),
                new PackageFormatRule(),
                new EnoughPackageRule()
            ],
            'level' => [
                'required',
                'in:free,paid'
            ],
            'on_public' => [
                'nullable',
            ],
        ];

        if ($this->boolean('save_draft')) {
            $rules = collect($rules)
                ->map(function ($rule, $key) {
                    return ['nullable'];
                })
                ->all();
        }

        return $rules;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'package_id' => request('package_id') ? explode(':', request('package_id')) : [],
        ]);
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'package_id.required' => 'job_detail_package_select',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     * @return void
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function (Validator $validator) {
            // Only validate if job posting is free
            if ($this->input('level') != JobLevelEnum::FREE->value) {
                return;
            }

            if ($this->user()->company->hasFreeJobPostingQuota()) {
                return;
            }

            $validator->errors()->add('free_job_open_exceeded', 'store_job_already_have_job_free_open');
        });
    }
}
