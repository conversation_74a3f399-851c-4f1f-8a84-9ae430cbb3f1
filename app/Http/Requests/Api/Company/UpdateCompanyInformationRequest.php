<?php

namespace App\Http\Requests\Api\Company;

use App\Helpers\FeatureFlag;
use App\Models\Taxonomy;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCompanyInformationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'display_name' => ['required'],
            'image_logo' => ['required'],
            'description' => ['required'],
            'website' => ['nullable'],
            'tagline' => ['required'],
            'nationalities' => [
                'required',
                'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'nationalities'),
            ],
            'products' => ['array'],
            'social_network' => ['nullable'],
            'addresses' => ['required', 'array'],
            'num_employees' => [
                'required',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'num_employees'),
            ],
            'image_cover' => ['nullable'],
            'image_galleries' => ['array'],
            'benefits' => ['required', 'array'],
            'skills_ids' => [
                'required', 'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'skills'),
            ],
            'industries_ids' => [
                'required', 'array',
                Rule::exists(Taxonomy::class, 'id')
                    ->where('taxonomy', 'industries'),
            ],
            'faqs' => ['nullable', 'array'],
            'erc_file' => ['required']
        ];

        return $rules;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'faqs' => $this->get('faqs', []),
        ]);
    }
}
