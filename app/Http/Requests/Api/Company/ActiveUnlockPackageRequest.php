<?php

namespace App\Http\Requests\Api\Company;

use App\Models\CompanySearchPackage;
use Illuminate\Foundation\Http\FormRequest;

class ActiveUnlockPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_search_package_id' => ['required', function($attribute, $value, $fail) {
                $row = CompanySearchPackage::whereId($value);

                if (!$row->exists()) {
                    return $fail("The unlock package is not found");
                }

                if (!$row->first()->isInvalid()) {
                    return $fail("Can not activate this unlock package");
                }
            }]
        ];
    }
}
