<?php

namespace App\Http\Requests\Api\SearchCandidate;

use App\Http\Controllers\Api\SearchCandidate\UnlockSearchCandidateDraftAction;
use App\Models\SearchCandidate;
use App\Rules\SearchCandidate\EnoughCreditRule;
use App\Rules\SearchCandidate\NotExpiredSectionKeyYetRule;
use App\Rules\SearchCandidate\NotUnlockedSearchCandidateYetRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Validator;

class UnlockSearchCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('unlock-candidate', SearchCandidate::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_candidate_id' => [
                'required',
                'exists:search_candidates,id',
                new NotUnlockedSearchCandidateYetRule,
            ],
            'key' => [
                'required',
                new NotExpiredSectionKeyYetRule,
                new EnoughCreditRule,
            ],
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge(['search_candidate_id' => $this->route('searchCandidateId')]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  Validator  $validator
     * @return void
     */
    public function withValidator(Validator $validator)
    {
        $validator->after(function (Validator $validator) {
            if ($validator->errors()->isNotEmpty()) {
                /**
                 * @var \Illuminate\Support\MessageBag $failedRules
                 */
                $failedRules = $validator->failed();
                if (isset($failedRules['search_candidate_id'][NotUnlockedSearchCandidateYetRule::class])) {
                    $validator->errors()->add('code', (string) UnlockSearchCandidateDraftAction::CODE_RESUME_ALREADY_UNLOCKED);
                } elseif (isset($failedRules['key'][EnoughCreditRule::class])) {
                    $validator->errors()->add('code', (string) UnlockSearchCandidateDraftAction::CODE_NOT_ENOUGHT_CREDIT);
                }
            }
        });
    }
}
