<?php

namespace App\Http\Requests\Api\SearchCandidate;

use App\Models\Taxonomy;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

class GetSearchCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'keyword' => 'nullable|array',
            'keyword.*' => 'nullable|string',
            'filters.skill' => 'nullable|array',
            'filters.skill.*' => 'nullable|integer',
            'filters.experience' => "nullable|array",
            'filters.experience.*' => "nullable|integer",
            'filters.updated_date_from' => 'nullable|required_with:filters.updated_date_to|date',
            'filters.updated_date_to' => 'nullable|required_with:filters.updated_date_from|date|after_or_equal:filters.updated_date_from',
        ];
    }
}
