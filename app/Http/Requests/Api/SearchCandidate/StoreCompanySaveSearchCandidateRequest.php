<?php

namespace App\Http\Requests\Api\SearchCandidate;

use App\Models\CompanySaveSearchCandidate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class StoreCompanySaveSearchCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('create', CompanySaveSearchCandidate::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_candidate_id' => 'required|exists:search_candidates,id',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge(['search_candidate_id' => $this->route('searchCandidateId')]);
    }
}
