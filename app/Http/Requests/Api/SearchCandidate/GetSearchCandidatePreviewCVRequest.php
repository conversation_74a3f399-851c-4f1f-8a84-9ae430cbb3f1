<?php

namespace App\Http\Requests\Api\SearchCandidate;

use App\Rules\SearchCandidate\CheckUserBlockCompanyRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetSearchCandidatePreviewCVRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_candidate_id' => [
                'required',
                'exists:search_candidates,id',
                new CheckUserBlockCompanyRule(),
            ],
            'type' => [
                'required',
                Rule::in(['original', 'topdev']),
            ],
            'key' => 'required',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'search_candidate_id' => $this->route('searchCandidateId'),
            'type' => $this->get('type'),
            'key' => $this->get('key'),
        ]);
    }
}
