<?php

namespace App\Http\Requests\Api\SearchCandidate\Note;

use App\Models\SearchCandidateNote;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class StoreSearchCandidateNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('create', SearchCandidateNote::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_candidate_id' => 'required|exists:search_candidates,id',
            'content' => 'required',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge(['search_candidate_id' => $this->route('searchCandidateId')]);
    }
}
