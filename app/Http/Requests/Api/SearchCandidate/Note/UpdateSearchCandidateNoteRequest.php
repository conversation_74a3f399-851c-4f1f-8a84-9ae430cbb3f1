<?php

namespace App\Http\Requests\Api\SearchCandidate\Note;

use App\Models\SearchCandidateNote;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class UpdateSearchCandidateNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('update', SearchCandidateNote::find($this->route('noteId')));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_candidate_id' => 'required|exists:search_candidates,id',
            'id' => 'required|exists:search_candidate_notes,id',
            'content' => 'sometimes|required',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'search_candidate_id' => $this->route('searchCandidateId'),
            'id' => $this->route('noteId'),
        ]);
    }
}
