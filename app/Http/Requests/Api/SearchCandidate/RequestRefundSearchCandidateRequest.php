<?php

namespace App\Http\Requests\Api\SearchCandidate;

use App\Models\SearchCandidate;
use App\Rules\SearchCandidate\AlreadyUnlockedSearchCandidate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class RequestRefundSearchCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Gate::allows('request-refund', SearchCandidate::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'search_candidate_id' => [
                'required',
                'exists:search_candidates,id',
                new AlreadyUnlockedSearchCandidate,
            ],
            'reason' => ['required', 'max:200'],
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge(['search_candidate_id' => $this->route('searchCandidateId')]);
    }
}
