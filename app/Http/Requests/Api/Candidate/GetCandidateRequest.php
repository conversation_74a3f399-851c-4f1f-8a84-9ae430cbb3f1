<?php

namespace App\Http\Requests\Api\Candidate;

use App\Models\Candidate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class GetCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Gate::allows('viewAny', Candidate::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page_size' => 'nullable|integer',
            'page' => 'nullable|integer',
            'job_id' => 'nullable|integer',
            'application_status' => 'nullable|integer',
            'location_id' => 'nullable|string',
            'applied_date_from' => 'nullable|date_format:Y-m-d',
            'applied_date_to' => 'nullable|date_format:Y-m-d',
            'skills_id' => 'nullable|regex:/(^([1-9][0-9]*,)*([1-9][0-9]*){1}$)/u',
        ];
    }
}
