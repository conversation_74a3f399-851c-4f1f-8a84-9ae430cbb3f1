<?php

namespace App\Http\Requests\Api\Candidate;

use App\Models\Candidate;
use App\States\Candidate\ProcedureStatus\NotMatching;
use App\States\Candidate\ProcedureStatus\ProcedureStatus;
use App\States\Candidate\ProcedureStatus\Matched;
use Illuminate\Foundation\Http\FormRequest;
use Spatie\ModelStates\Validation\ValidStateRule;

/**
 * @property mixed $note
 */
class UpdateCandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'note' => ['nullable', 'string'],
            'procedure_status' => [
                ValidStateRule::make(ProcedureStatus::class)->nullable(),
            ],
        ];
    }
}
