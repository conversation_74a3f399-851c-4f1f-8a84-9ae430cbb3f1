<?php

namespace App\Http\Requests\Api\User;

use App\Models\User;
use App\Rules\ChangeUserPassword;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

/**
 * @property mixed $new_password
 */
class ChangeUserPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Gate::allows('changPassword', User::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'old_password' => [
                'required',
                'string',
                'max:255',
                new ChangeUserPassword,
            ],
            'new_password' => [
                'required',
                'string',
                'max:255',
                'different:old_password',
            ],
        ];
    }
}
