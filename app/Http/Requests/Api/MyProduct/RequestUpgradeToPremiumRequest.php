<?php

namespace App\Http\Requests\Api\MyProduct;

use Illuminate\Foundation\Http\FormRequest;

class RequestUpgradeToPremiumRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_name' => 'required',
            'fullname' => 'required',
            'phone_number' => 'required',
        ];
    }
}
