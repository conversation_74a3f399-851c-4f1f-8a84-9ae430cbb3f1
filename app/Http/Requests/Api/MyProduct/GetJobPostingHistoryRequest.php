<?php

namespace App\Http\Requests\Api\MyProduct;

use Illuminate\Foundation\Http\FormRequest;

class GetJobPostingHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'from_date' => 'date|nullable|required_with:to_date',
            'to_date' => 'date|nullable|after_or_equal:from_date|required_with:from_date',
            'package_order_by' => 'nullable|in:asc,desc',
            'package_order_by_field' => 'nullable|in:use_expired_at',
        ];
    }
}
