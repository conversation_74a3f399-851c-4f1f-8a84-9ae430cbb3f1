<?php

namespace App\Jobs\EmployerDash;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class AMSProcessCompanyForEmployerDash implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $companyId;

    /**
     * Create a new job instance.
     */
    public function __construct($companyId)
    {
        $this->companyId = $companyId;
        $this->onConnection(config('queue.ams'));
    }

    public function handle(): void
    {
        // DO NOTHING HERE
    }
}
