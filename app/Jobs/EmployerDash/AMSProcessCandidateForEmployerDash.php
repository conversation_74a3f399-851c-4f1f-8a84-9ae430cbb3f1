<?php

namespace App\Jobs\EmployerDash;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class AMSProcessCandidateForEmployerDash implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $candidateId;

    /**
     * Create a new job instance.
     */
    public function __construct($candidateId)
    {
        $this->candidateId = $candidateId;

        $this->onConnection(config('queue.ams'));
    }
}
