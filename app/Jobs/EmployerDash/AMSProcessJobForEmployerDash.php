<?php

namespace App\Jobs\EmployerDash;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AMSProcessJobForEmployerDash implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $jobId;

    public int $userId;

    public string $action;

    public const ACTION_UPDATE = 'update';

    public const ACTION_STORE = 'store';

    public const ACTION_FORCE_PUBLISH = 'force_publish';

    public const ACTION_FREE_PUBLISH = 'free_publish';

    /**
     * Create a new job instance.
     */
    public function __construct($jobId, $userId, $action)
    {
        $this->jobId = $jobId;
        $this->userId = $userId;
        $this->action = $action;

        $this->onConnection(config('queue.ams'));

        Log::info('AMSProcessJobForEmployerDash __construct', [
            'jobId' => $this->jobId,
            'userId' => $this->userId,
            'action' => $this->action,
        ]);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        // Do nothing here
    }
}
