<?php


namespace App\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\SystemTopDevMail;

class TopDevMailChannel
{
    use DispatchesJobs;

    /**
     * Send the given notification.
     *
     * @param mixed $notifiable
     * @param \Illuminate\Notifications\Notification $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        // @phpstan-ignore-next-line
        $message = $notification->toSendMailSystem($notifiable);

        dispatch(new SystemTopDevMail($message));
    }
}
