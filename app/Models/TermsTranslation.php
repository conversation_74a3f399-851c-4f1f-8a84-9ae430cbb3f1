<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\Taxonomy\Entities\TermsTranslation
 *
 * @property int $id
 * @property string $locale
 * @property int $term_id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TermsTranslation whereTermId($value)
 * @mixin \Eloquent
 */
class TermsTranslation extends Model
{
    protected $fillable = ['name'];

    /**
     * Set timestamps
     *
     * @var boolean
     */
    public $timestamps = false;

}
