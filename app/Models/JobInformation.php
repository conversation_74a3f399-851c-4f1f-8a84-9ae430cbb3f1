<?php

namespace App\Models;

use App\Models\Concerns\Salary;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

/**
 * App\Models\JobInformation.
 *
 * @property int $id
 * @property int $job_id
 * @property mixed|null $social_post_content
 * @property mixed|null $salary
 * @property string|null $years_of_exp
 * @property mixed|null $benefits
 * @property mixed|null $languages
 * @property mixed|null $other_supports
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation newQuery()
 * @method static \Illuminate\Database\Query\Builder|JobInformation onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation query()
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereBenefits($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereLanguages($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereOtherSupports($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereSocialPostContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobInformation whereYearsOfExp($value)
 * @method static \Illuminate\Database\Query\Builder|JobInformation withTrashed()
 * @method static \Illuminate\Database\Query\Builder|JobInformation withoutTrashed()
 * @mixin \Eloquent
 */
class JobInformation extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'salary', 'benefits',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'benefits' => 'array',
    ];

    public function getSalaryAttribute($value)
    {
        return (new Salary(
            json_decode($value, true)
        ))->toArray();
    }

    public function getRawSalaryAttribute()
    {
        return (new Salary(
            json_decode(Arr::get($this->attributes, 'salary'), true)
        ))->toRawArray();
    }

    public function setSalaryAttribute($value)
    {
        if ($value instanceof Salary) {
            $value = $value->toArray();
        } else {
            $value = (new Salary(
                $value
            ))->toArray();
        }

        $this->attributes['salary'] = json_encode($value);
    }
}
