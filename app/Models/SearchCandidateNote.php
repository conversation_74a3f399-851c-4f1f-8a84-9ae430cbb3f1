<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\SearchCandidateNote.
 *
 * @property int $id
 * @property int $search_candidate_id
 * @property string $content
 * @property int $created_by
 * @property int $updated_by
 * @property int $company_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote query()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereSearchCandidateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchCandidateNote whereUpdatedBy($value)
 * @property-read User|null $createdBy
 * @property-read User|null $updatedBy
 * @mixin \Eloquent
 */
class SearchCandidateNote extends Model
{
    use HasFactory;

    protected $table = 'search_candidate_notes';

    protected $fillable = [
        'company_id',
        'search_candidate_id',
        'content',
        'created_by',
        'updated_by',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
