<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * \App\Models\UserBlockCompanies.
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $company_id
 * @property string|null $email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Company|null $company
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserBlockCompanies whereUserId($value)
 * @mixin \Eloquent
 */
class UserBlockCompanies extends Model
{
    use HasFactory;

    protected $table = 'user_block_companies';

    protected $fillable = [
        'user_id',
        'company_id',
        'email',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function scopeWhereInEmail($query, array $domainEmail)
    {
        return $query->whereIn('email', $domainEmail);
    }

    public function getUserIdBlockCompanyByEmails($emails)
    {
        $domainEmails = array_map(function ($email) {
            return substr($email, strpos($email, '@') + 1);
        }, $emails);

        return $this->whereInEmail($domainEmails)->pluck('user_id');
    }
}
