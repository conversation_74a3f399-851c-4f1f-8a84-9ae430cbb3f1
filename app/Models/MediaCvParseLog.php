<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\MediaCvParseLog.
 *
 * @property int $id
 * @property int $media_id
 * @property string $status
 * @property string|null $error
 * @property string|null $raw_data
 * @property array|null $choice_data
 * @property array|null $profile_data
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Media|null $media
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereChoiceData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereError($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereMediaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereProfileData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereRawData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MediaCvParseLog whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MediaCvParseLog extends Model
{
    protected $table = 'media_cv_parse_logs';

    public const PARSE_STATUS_PROCESSING = 'processing';

    public const PARSE_STATUS_DONE = 'done';

    public const PARSE_STATUS_ERROR = 'error';

    protected $fillable = [
        'media_id',
        'status',
        'error',
        'raw_data',
        'choice_data',
        'profile_data',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'error' => null,
        'raw_data' => null,
        'choice_data' => '[]',
        'profile_data' => '[]',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'choice_data' => 'json',
        'profile_data' => 'json',
    ];

    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id');
    }
}
