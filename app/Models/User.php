<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\User.
 *
 * @property int $company_id
 * @property int $id
 * @property string $uuid
 * @property string|null $firstname
 * @property string|null $lastname
 * @property string|null $display_name
 * @property string $username
 * @property string $email
 * @property string|null $phone
 * @property string|null $gender male|female|homosexual
 * @property string|null $birthday
 * @property string|null $password
 * @property string|null $description
 * @property string|null $type using resume|employer|partner|others
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string|null $approved_at
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string|null $position
 * @property-read Company|null $company
 * @property-read UserProfile|null $userProfile
 * @property-read Meta|null $meta
 * @property mixed $roles
 *
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User query()
 * @method static Builder|User whereApprovedAt($value)
 * @method static Builder|User whereBirthday($value)
 * @method static Builder|User whereCompanyId($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDeletedAt($value)
 * @method static Builder|User whereDescription($value)
 * @method static Builder|User whereDisplayName($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFirstname($value)
 * @method static Builder|User whereGender($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereLastname($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePosition($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereType($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUsername($value)
 * @method static Builder|User whereUuid($value)
 * @property string|null $freeze_at
 * @property int $allow_share_cv
 * @property-read Collection<\App\Models\Address> $addresses
 * @property-read int|null $addresses_count
 * @property-read UserMainCV|null $mainCv
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection<int, \App\Models\MyResume> $resumes
 * @property-read int|null $resumes_count
 * @property-read SearchCandidate|null $searchCandidate
 * @property-write mixed $full_name
 * @property-read Collection<int, \App\Models\Taxonomy> $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read array $skills_ids getSkillsIdsAttribute
 * @method static Builder|User onlyApproved()
 * @method static Builder|User onlyTrashed()
 * @method static Builder|User whereAllowShareCv($value)
 * @method static Builder|User whereFreezeAt($value)
 * @method static Builder|User withTrashed()
 * @method static Builder|User withoutTrashed()
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use SoftDeletes;
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    protected $connection = 'mysql_ams';

    public const RESUME_TYPE = 'resume';

    public const EMPLOYER_TYPE = 'employer';

    public const PARTNER_TYPE = 'partner';

    public const OTHER_TYPE = 'other';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'name',
        'email',
        'password',
        'position',
        'display_name',
        'phone',
        'full_name',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Check if current user is employer.
     *
     * @return bool
     */
    public function isEmployer(): bool
    {
        return $this->type == self::EMPLOYER_TYPE;
    }

    /**
     * Check if current user is employer.
     *
     * @return bool
     */
    public function isEmployerValid(): bool
    {
        return $this->isEmployer() &&
            !is_null($this->company) &&
            $this->company->deleted_at == null;
    }

    /**
     * Check if current user is already approved.
     *
     * @return bool
     */
    public function isEmployerAlreadyApproved(): bool
    {
        return $this->approved_at !== null;
    }

    /**
     * Get company of this user.
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function taxonomies(): MorphToMany
    {
        return $this->morphToMany(Taxonomy::class, 'gables', 'term_relationships', 'gables_id', 'taxonomy_id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * @param $value
     * @return void
     */
    public function setFullNameAttribute($value)
    {
        $this->attributes['display_name'] = $value;
    }

    public function getName()
    {
        if (!blank($this->display_name)) {
            return $this->display_name;
        }

        if (blank($this->firstname) && blank($this->lastname)) {
            return $this->username;
        }

        return trim($this->lastname . ' ' . $this->firstname);
    }

    /**
     * @param $query
     * @return void
     */
    public function scopeOnlyApproved($query)
    {
        $query->whereNotNull('approved_at');
    }

    public function mainCv(): HasOne
    {
        return $this->hasOne(UserMainCV::class, 'user_id');
    }

    public function searchCandidate(): HasOne
    {
        return $this->hasOne(SearchCandidate::class, 'user_id', 'id');
    }

    public function resumes(): HasMany
    {
        return $this->hasMany(MyResume::class, 'user_id');
    }

    /**
     * Check logged in user's email is in the list of talent success email or not.
     *
     * @return bool
     */
    public function isTalentSuccessUser(): bool
    {
        return in_array($this->email, config('candidate.talent_success_user_emails'));
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getSkillsIdsAttribute(): \Illuminate\Support\Collection
    {
        return $this->taxonomies->where('taxonomy', 'skills')->pluck('id');
    }

    /**
     * Get the user's profile.
     *
     * @return HasOne
     */
    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    /**
     * Relationship to the `Meta` model.
     *
     * @return MorphMany
     */
    public function meta(): MorphMany
    {
        return $this->morphMany(Meta::class, 'metable');
    }
}
