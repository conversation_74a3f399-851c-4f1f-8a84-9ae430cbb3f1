<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphPivot;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;

/**
 * Modules\Taxonomy\Entities\Taxable
 *
 * @property int $id
 * @property int $taxonomy_id
 * @property int|null $gables_id
 * @property string|null $gables_type
 * @property string|null $custom_properties
 * @property Carbon|null $expires_at
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read Model|\Eloquent $taxable
 * @property-read Taxonomy $taxonomy
 * @method static Builder|Taxable newModelQuery()
 * @method static Builder|Taxable newQuery()
 * @method static Builder|Taxable query()
 * @method static Builder|Taxable whereCreatedAt($value)
 * @method static Builder|Taxable whereExpiresAt($value)
 * @method static Builder|Taxable whereGablesId($value)
 * @method static Builder|Taxable whereGablesType($value)
 * @method static Builder|Taxable whereId($value)
 * @method static Builder|Taxable whereTaxonomyId($value)
 * @method static Builder|Taxable whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Taxable extends MorphPivot
{
    /**
     * @inheritdoc
     */
    protected $table = 'term_relationships';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'taxonomy_id',
        'gables_id',
        'gables_type',
        'custom_properties',
        'expires_at'
    ];

    /**
     * {@inheritdoc}
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'custom_properties' => 'array',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @inheritdoc
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * @return MorphTo
     */
    public function taxable(): MorphTo
    {
        return $this->morphTo('gables')->withoutGlobalScopes();
    }

    /**
     * @return BelongsTo
     */
    public function taxonomy(): BelongsTo
    {
        return $this->belongsTo(Taxonomy::class, 'taxonomy_id');
    }
}
