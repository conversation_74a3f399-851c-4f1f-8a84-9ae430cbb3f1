<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;
use Throwable;

/**
 * App\Models\SearchCandidate.
 *
 * @property int $id
 * @property int $user_id
 * @property int $credit
 * @property int|null $viewed_count
 * @property int|null $unlocked_count
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, SearchCandidateNote> $notes
 * @property-read int|null $notes_count
 * @property int|null $unique_viewed_count
 * @property string|null $display_name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $position
 * @property string|null $birthday
 * @property int $years_of_exp
 * @property string|null $address
 * @property string|null $province_code
 * @property string|null $province_name
 * @property string|null $github
 * @property string|null $linkedin
 * @property string|null $summary
 * @property array|null $skills
 * @property array|null $experiences
 * @property array|null $educations
 * @property array|null $projects
 * @property array|null $languages
 * @property array|null $interests
 * @property array|null $references
 * @property array|null $activities
 * @property array|null $certificates
 * @property array|null $additionals
 * @property-read bool $is_saved
 * @property-read Carbon|null $disabled_at
 * @property-read bool $is_viewed
 * @property-read string|void $type
 * @property-read Collection<int, Company> $savedByCurrentCompany
 * @property-read int|null $saved_by_current_company_count
 * @property-read Collection<int, Company> $unlockedByCurrentCompany
 * @property-read int|null $unlocked_by_current_company_count
 * @property-read User|null $user
 * @property-read UserMainCV|null $userMainCV
 * @property-read UserProfile|null $userProfile
 * @property-read Collection<int, SearchCandidateViewLog> $viewLogs
 * @property-read CompanyUnlockSearchCandidate|null $companyUnlockedLog
 * @property-read int|null $view_logs_count
 * @property-read Collection<int, User> $viewLogsByCurrentUser
 * @property-read int|null $view_logs_by_current_user_count
 * @property-read Collection<int, SearchCandidateViewLog> $viewLogsUniqueByCompany
 * @property-read int|null $view_logs_unique_by_company_count
 * @property-read bool $is_unlocked
 * @method static Builder|SearchCandidate newModelQuery()
 * @method static Builder|SearchCandidate newQuery()
 * @method static Builder|SearchCandidate query()
 * @method static Builder|SearchCandidate whereCreatedAt($value)
 * @method static Builder|SearchCandidate whereCredit($value)
 * @method static Builder|SearchCandidate whereId($value)
 * @method static Builder|SearchCandidate whereUnlockedCount($value)
 * @method static Builder|SearchCandidate whereUpdatedAt($value)
 * @method static Builder|SearchCandidate whereUserId($value)
 * @method static Builder|SearchCandidate whereViewedCount($value)
 * @method static Builder|SearchCandidate whereActivities($value)
 * @method static Builder|SearchCandidate whereAdditionals($value)
 * @method static Builder|SearchCandidate whereAddress($value)
 * @method static Builder|SearchCandidate whereBirthday($value)
 * @method static Builder|SearchCandidate whereCertificates($value)
 * @method static Builder|SearchCandidate whereDisplayName($value)
 * @method static Builder|SearchCandidate whereEducations($value)
 * @method static Builder|SearchCandidate whereEmail($value)
 * @method static Builder|SearchCandidate whereExperiences($value)
 * @method static Builder|SearchCandidate whereGithub($value)
 * @method static Builder|SearchCandidate whereInterests($value)
 * @method static Builder|SearchCandidate whereLanguages($value)
 * @method static Builder|SearchCandidate whereLinkedin($value)
 * @method static Builder|SearchCandidate wherePhone($value)
 * @method static Builder|SearchCandidate wherePosition($value)
 * @method static Builder|SearchCandidate whereProjects($value)
 * @method static Builder|SearchCandidate whereProvinceCode($value)
 * @method static Builder|SearchCandidate whereProvinceName($value)
 * @method static Builder|SearchCandidate whereReferences($value)
 * @method static Builder|SearchCandidate whereSkills($value)
 * @method static Builder|SearchCandidate whereSummary($value)
 * @method static Builder|SearchCandidate whereUniqueViewedCount($value)
 * @method static Builder|SearchCandidate whereYearsOfExp($value)
 * @mixin \Eloquent
 * @property-read VietnamArea|null $province
 */
class SearchCandidate extends Model
{
    use HasFactory;
    use Searchable;

    protected $fillable = ['user_id', 'credit', 'viewed_count', 'unlocked_count', 'unique_viewed_count', 'created_at', 'updated_at'];

    protected $casts = [
        'skills' => 'json',
        'educations' => 'json',
        'experiences' => 'json',
        'projects' => 'json',
        'languages' => 'json',
        'interests' => 'json',
        'references' => 'json',
        'activities' => 'json',
        'certificates' => 'json',
        'additionals' => 'json',
    ];

    public function notes(): HasMany
    {
        return $this
            ->hasMany(SearchCandidateNote::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class, 'user_id', 'user_id');
    }

    public function getScoutClassname(): string
    {
        return 'Modules\SearchCandidate\Entities\SearchCandidate';
    }

    public function viewLogsByCurrentUser(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            'search_candidates_view_logs',
            'search_candidate_id',
            'viewer_id'
        )
            ->using(SearchCandidateViewLog::class)
            ->where('viewer_id', Auth::id());
    }

    public function companyCreditLogsByCurrentCompany(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_credit_logs', 'candidate_id')
            ->using(CompanyCreditLog::class)
            ->where('company_id', Auth::user()->company_id);
    }

    public function unlockedByCurrentCompany(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_unlock_search_candidates')
            ->using(CompanyUnlockSearchCandidate::class)
            ->where('company_id', Auth::user()->company_id);
    }

    public function savedByCurrentCompany(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_save_search_candidates')
            ->using(CompanySaveSearchCandidate::class)
            ->where('company_id', Auth::user()->company_id);
    }

    public function companyUnlockSearchCandidates(): HasMany
    {
        return $this->hasMany(CompanyUnlockSearchCandidate::class, 'search_candidate_id')
            ->where('company_id', Auth::user()->company_id);
    }

    public function companyUnlockedLog(): HasOne
    {
        return $this->hasOne(CompanyUnlockSearchCandidate::class, 'search_candidate_id')
            ->where('company_id', Auth::user()->company_id)
            ->where(DB::raw('date_add(company_unlock_search_candidates.expired_at, INTERVAL 30 day)'), '>=', now());
    }

    /**
     * Attribute is_unlocked for search candidate. (->is_unlocked)
     *
     * @return bool
     */
    public function getIsUnlockedAttribute(): bool
    {
        /**
         * @var Company|null $company
         */
        $company = $this->unlockedByCurrentCompany->first();
        if (is_null($company)) {
            return false;
        }

        $isUnlocked = $company->id == Auth::user()->company_id && $this->companyUnlockedLog;

        /**
         * check last company credit logs is Refunded or Out.
         */
        $company = $this->companyCreditLogsByCurrentCompany->last();
        if (!is_null($company) && ($lastCreditLog = $company->creditLogs->where('candidate_id', $this->id)->last())) {
            /** @var CompanyCreditLog $lastCreditLog */
            $isUnlocked &= $lastCreditLog->isTypeOut();
        }

        return $isUnlocked;
    }

    /**
     * Attribute is_viewed for search candidate. (->is_viewed)
     *
     * @return bool
     */
    public function getIsViewedAttribute(): bool
    {
        /**
         * @var User|null $viewer
         */
        $viewer = $this->viewLogsByCurrentUser->first();
        if (is_null($viewer)) {
            return false;
        }

        return $viewer->id == Auth::id();
    }

    /**
     * Attribute is_saved for search candidate. (->is_saved)
     *
     * @return bool
     */
    public function getIsSavedAttribute(): bool
    {
        /**
         * @var Company|null $company
         */
        $company = $this->savedByCurrentCompany->first();
        if (is_null($company)) {
            return false;
        }

        return $company->id == Auth::user()->company_id;
    }

    /**
     * @return HasOne
     */
    public function userMainCV(): HasOne
    {
        return $this->hasOne(UserMainCV::class, 'user_id', 'user_id');
    }

    /**
     * @return HasMany
     */
    public function viewLogs(): HasMany
    {
        return $this->hasMany(SearchCandidateViewLog::class, 'search_candidate_id', 'id');
    }

    /**
     * @return HasMany
     */
    public function viewLogsUniqueByCompany(): HasMany
    {
        return $this->viewLogs()->select('company_id')->groupBy('company_id');
    }

    /**
     * Attribute type for search candidate. (->type)
     *
     * @return string|void
     * @throws Throwable
     */
    public function getTypeAttribute()
    {
        if (!$this->user instanceof User) {
            return;
        }

        /**
         * @var UserMainCV $mainCv
         */
        $mainCv = $this->user->mainCv;

        if (empty($mainCv->cv)) {
            return UserMainCV::CV_TYPE_TOPDEV_CV;
        }

        $type = $mainCv->getCvTypeRelation($mainCv->cv_type);

        if ($type === UserMainCV::CV_TYPE_UPLOAD_CV && !$mainCv->cv->redactFile()) { // @phpstan-ignore-line
            return UserMainCV::CV_TYPE_TOPDEV_CV;
        }

        return $type;
    }

    /**
     * Get province for the user profile.
     *
     * @return BelongsTo
     */
    public function province(): BelongsTo
    {
        return $this->belongsTo(VietnamArea::class, 'province_code', 'code')
            ->whereIn('vietnam_area.type', VietnamArea::VIETNAM_AREA_PROVINCE)
            ->where([
                'vietnam_area.parent_code' => 0,
                'vietnam_area.status' => 1,
            ]);
    }
}
