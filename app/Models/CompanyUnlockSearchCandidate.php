<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Support\Carbon;

/**
 * App\Models\CompanyUnlockSearchCandidate.
 *
 * @property int $id
 * @property int $company_id
 * @property int $user_id
 * @property int $search_candidate_id
 * @property int $credit
 * @property int $profile_cv_id
 * @property int $main_cv_id
 * @property string $expired_at
 * @property string|null $refunded_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|CompanyUnlockSearchCandidate newModelQuery()
 * @method static Builder|CompanyUnlockSearchCandidate newQuery()
 * @method static Builder|CompanyUnlockSearchCandidate query()
 * @method static Builder|CompanyUnlockSearchCandidate whereCompanyId($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereCreatedAt($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereCredit($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereExpiredAt($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereId($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereMainCvId($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereProfileCvId($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereRefundedAt($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereSearchCandidateId($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereUpdatedAt($value)
 * @method static Builder|CompanyUnlockSearchCandidate whereUserId($value)
 * @mixin \Eloquent
 */
class CompanyUnlockSearchCandidate extends Pivot
{
    use HasFactory;

    protected $table = 'company_unlock_search_candidates';
}
