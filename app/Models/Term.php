<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Term.
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $slug
 * @property int $parent_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $feature
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Term newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Term newQuery()
 * @method static \Illuminate\Database\Query\Builder|Term onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Term query()
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereFeature($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Term whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Term withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Term withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Term selectTerm($taxonomy = null)
 * @mixin \Eloquent
 */
class Term extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'slug',
        'parent_id',
        'feature',
    ];

    public function scopeSelectTerm($query, $taxonomy = null)
    {
        $query->join('term_taxonomy', 'terms.id', '=', 'term_taxonomy.term_id')
            ->select('terms.id', 'term_taxonomy.id as ttid', 'taxonomy', 'name', 'feature');

        if (is_array($taxonomy)) {
            $query->whereIn('taxonomy', $taxonomy);
        } elseif (!empty($taxonomy)) {
            $query->where('taxonomy', $taxonomy);
        }

        return $query;
    }

    public function translates()
    {
        return $this->hasMany(TermsTranslation::class);
    }

    public function getNameAttribute()
    {
        $name = $this->translates()->where('locale', app()->getLocale())->first();

        if ($name) {
            return $name->name;
        }

        return $this->translates()->first()->name;
    }
}
