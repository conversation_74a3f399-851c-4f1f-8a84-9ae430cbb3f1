<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

/**
 * App\Modules\CvProfile.
 *
 * @property int $id
 * @property string $username
 * @property string|null $email
 * @property string|null $firstname
 * @property string|null $lastname
 * @property string|null $display_name
 * @property string|null $phone
 * @property string|null $province_code
 * @property int|null $year_of_exp
 * @property array $best_skills
 * @property string|null $position
 * @property array $experiences
 * @property array $educations
 * @property array $projects
 * @property array $languages
 * @property array $activities
 * @property array $certificates
 * @property array $interests
 * @property array $references
 * @property int $media_id
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int $user_id
 * @property string|null $cvbuilder_id
 * @property int $on_ready
 * @property Carbon|null $ams_profile_updated_at
 * @property-read Media $media
 * @property-read User|null $resume
 * @method static Builder|CvProfile newModelQuery()
 * @method static Builder|CvProfile newQuery()
 * @method static Builder|CvProfile query()
 * @method static Builder|CvProfile whereActivities($value)
 * @method static Builder|CvProfile whereAmsProfileUpdatedAt($value)
 * @method static Builder|CvProfile whereBestSkills($value)
 * @method static Builder|CvProfile whereCertificates($value)
 * @method static Builder|CvProfile whereCreatedAt($value)
 * @method static Builder|CvProfile whereCvbuilderId($value)
 * @method static Builder|CvProfile whereDisplayName($value)
 * @method static Builder|CvProfile whereEducations($value)
 * @method static Builder|CvProfile whereEmail($value)
 * @method static Builder|CvProfile whereExperiences($value)
 * @method static Builder|CvProfile whereFirstname($value)
 * @method static Builder|CvProfile whereId($value)
 * @method static Builder|CvProfile whereInterests($value)
 * @method static Builder|CvProfile whereLanguages($value)
 * @method static Builder|CvProfile whereLastname($value)
 * @method static Builder|CvProfile whereMediaId($value)
 * @method static Builder|CvProfile whereOnReady($value)
 * @method static Builder|CvProfile wherePhone($value)
 * @method static Builder|CvProfile wherePosition($value)
 * @method static Builder|CvProfile whereProjects($value)
 * @method static Builder|CvProfile whereProvinceCode($value)
 * @method static Builder|CvProfile whereReferences($value)
 * @method static Builder|CvProfile whereStatus($value)
 * @method static Builder|CvProfile whereUpdatedAt($value)
 * @method static Builder|CvProfile whereUserId($value)
 * @method static Builder|CvProfile whereUsername($value)
 * @method static Builder|CvProfile whereYearOfExp($value)
 * @mixin \Eloquent
 * @property-read Collection|Candidate[] $candidates
 * @property-read int|null $candidates_count
 */
class CvProfile extends Model
{
    protected $fillable = [
        'user_id',
        'username',
        'email',
        'firstname',
        'lastname',
        'display_name',
        'phone',
        'province_code',
        'year_of_exp',
        'best_skills',
        'position',
        'experiences',
        'educations',
        'projects',
        'languages',
        'activities',
        'certificates',
        'interests',
        'references',
        'media_id',
        'cvbuilder_id',
        'status',
        'ams_profile_updated_at',
    ];

    protected $casts = [
        'best_skills' => 'json',
        'experiences' => 'json',
        'educations' => 'json',
        'projects' => 'json',
        'languages' => 'json',
        'references' => 'json',
        'activities' => 'json',
        'certificates' => 'json',
        'interests' => 'json',
        'additionals' => 'json',
        'completed_sections' => 'json',
        'ams_profile_updated_at' => 'datetime',
    ];

    public const ARRAY_STATUS = [
        'Processing' => 'Processing',
        'Waiting' => 'Waiting',
        'Ready' => 'Ready',
        'Not enough info' => 'Not Enough info',
        'Spam' => 'Spam',
    ];

    public const ARRAY_STATUS_CANDIDATE = [
        'Processing' => -1,
        'Waiting' => Candidate::CANDIDATE_WAITING,
        'Ready' => Candidate::CANDIDATE_READY,
        'Not enough info' => Candidate::CANDIDATE_NOT_ENOUGH_INFORMATION,
        'Spam' => Candidate::CANDIDATE_SPAM,
    ];

    public const PROCESSING = 'Processing';
    public const WAITING = 'Waiting';
    public const READY = 'Ready';
    public const NOT_ENOUGH_INFO = 'Not Enough info';
    public const SPAM = 'Spam';

    public static function boot()
    {
        parent::boot();

        static::creating(function (self $model) {
            $model->status = $model->isContentReady() ? self::READY : self::WAITING;
        });
    }

    public function resume(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function isContentReady(): bool
    {
        return !empty($this->username)
            && !empty($this->email)
            && !empty($this->phone)
            && !empty($this->province_code)
            && !empty(Arr::get($this->best_skills, 'technical_skills'))
            && !is_null($this->year_of_exp)
            && !empty($this->position);
    }

    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id', 'id');
    }

    public function isReady(): bool
    {
        return $this->status === self::READY;
    }

    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }
}
