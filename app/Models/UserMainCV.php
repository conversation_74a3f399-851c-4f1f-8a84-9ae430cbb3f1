<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Models\UserMainCV.
 *
 * @property int $id
 * @property int $user_id
 * @property int $cv_id
 * @property string $cv_type
 * @property int|null $redacted_cv_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $cv
 * @method static Builder|UserMainCV newModelQuery()
 * @method static Builder|UserMainCV newQuery()
 * @method static Builder|UserMainCV query()
 * @method static Builder|UserMainCV whereCreatedAt($value)
 * @method static Builder|UserMainCV whereCvId($value)
 * @method static Builder|UserMainCV whereCvType($value)
 * @method static Builder|UserMainCV whereId($value)
 * @method static Builder|UserMainCV whereRedactedCvId($value)
 * @method static Builder|UserMainCV whereUpdatedAt($value)
 * @method static Builder|UserMainCV whereUserId($value)
 * @mixin \Eloquent
 */
class UserMainCV extends Model
{
    use HasFactory;

    public const CV_TYPE_TOPDEV_CV = 'topdev_cv';

    public const CV_TYPE_CV_BUILDER = 'cv_builder';

    // Cvs came from apply job, auto fill in user profile, user cv management
    public const CV_TYPE_UPLOAD_CV = 'upload_cv';

    protected $table = 'user_main_cvs';

    protected $fillable = [
        'user_id',
        'cv_id',
        'cv_type',
    ];

    /**
     * Its belongs to the following models by cv_type & cv_id
     *   Modules\User\Entities\MyResume
     *   Modules\User\Entities\User
     *   Modules\File\Entities\Media.
     * @return MorphTo
     */
    public function cv(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get relation class based on cvType.
     * @param string $cvType type of cv: topdev_cv, cv_builder, upload_cv
     * @return string Class of the relations with namespace
     * @return string
     * @throws \Throwable
     */
    public static function getCvTypeRelation(string $cvType): string
    {
        $typeRelations = self::cvTypeRelations();
        throw_unless(
            isset($typeRelations[$cvType]),
            Exception::class,
            $cvType . ' is invalid!'
        );

        return $typeRelations[$cvType];
    }

    public static function cvTypeRelations(): array
    {
        return [
            'Modules\User\Entities\UserProfile' => self::CV_TYPE_TOPDEV_CV,
            'Modules\File\Entities\Media' => self::CV_TYPE_UPLOAD_CV,
            'Modules\User\Entities\MyResume' => self::CV_TYPE_CV_BUILDER,
        ];
    }
}
