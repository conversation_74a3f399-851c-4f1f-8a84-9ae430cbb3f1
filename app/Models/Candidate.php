<?php

namespace App\Models;

use App\States\Candidate\ProcedureStatus\Failed;
use App\States\Candidate\ProcedureStatus\Hired;
use App\States\Candidate\ProcedureStatus\InterviewAppointment;
use App\States\Candidate\ProcedureStatus\NotMatching;
use App\States\Candidate\ProcedureStatus\Offer;
use App\States\Candidate\ProcedureStatus\ProcedureStatus;
use App\States\Candidate\ProcedureStatus\Matched;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\ModelStates\HasStates;

/**
 * App\Models\Candidate.
 *
 * @property int $id
 * @property int|null $job_id
 * @property int|null $creator_id
 * @property int $resume_id
 * @property string|null $status
 * @property string|null $sid
 * @property int|null $partner_id
 * @property string|null $cover_letter
 * @property string|null $utm_source
 * @property string|null $utm_medium
 * @property string|null $utm_campaign
 * @property string|null $source
 * @property string|null $device_apply
 * @property string|null $query_src Tracking src in url query param, not utm_source or any utm
 * @property string|null $query_medium Tracking medium in url query param, not utm_medium or any utm
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $group
 * @property string|null $cvbuilder_id
 * @property int $media_id
 * @property int $has_offer
 * @property string|null $confirmed_at
 * @property string|null $sent_candidate_at
 * @property string|null $delivered_at
 * @property string|null $sent_employer_at
 * @property string|null $sent_autoflow_at
 * @property string|null $employer_note
 * @property int $cv_profile_id
 * @property float|null $avg_skill_match
 * @property string|null $deleted_at
 * @property Carbon|null $employer_read_at
 * @property Carbon|null $recalled_at
 * @property string|null $procedure_status
 * @property Media $cvMedia
 * @property CvProfile $cv_profile
 * @property CvProfile $cvProfile
 * @property User $resume
 * @property Job $job
 * @property-read bool $is_remove_cv
 * @property-read string $download_cv_url
 * @property-read array $cv_profile_experiences getCvProfileExperiencesAttribute
 * @property-read array $cv_profile_educations getCvProfileEducationsAttribute
 *
 * @method static Builder|Candidate newModelQuery()
 * @method static Builder|Candidate newQuery()
 * @method static Builder|Candidate query()
 * @method static Builder|Candidate whereConfirmedAt($value)
 * @method static Builder|Candidate whereCoverLetter($value)
 * @method static Builder|Candidate whereCreatedAt($value)
 * @method static Builder|Candidate whereCreatorId($value)
 * @method static Builder|Candidate whereCvbuilderId($value)
 * @method static Builder|Candidate whereDeletedAt($value)
 * @method static Builder|Candidate whereDeliveredAt($value)
 * @method static Builder|Candidate whereDeviceApply($value)
 * @method static Builder|Candidate whereGroup($value)
 * @method static Builder|Candidate whereHasOffer($value)
 * @method static Builder|Candidate whereId($value)
 * @method static Builder|Candidate whereJobId($value)
 * @method static Builder|Candidate whereMediaId($value)
 * @method static Builder|Candidate wherePartnerId($value)
 * @method static Builder|Candidate whereQueryMedium($value)
 * @method static Builder|Candidate whereQuerySrc($value)
 * @method static Builder|Candidate whereResumeId($value)
 * @method static Builder|Candidate whereSentAutoflowAt($value)
 * @method static Builder|Candidate whereSentCandidateAt($value)
 * @method static Builder|Candidate whereSentEmployerAt($value)
 * @method static Builder|Candidate whereSid($value)
 * @method static Builder|Candidate whereSource($value)
 * @method static Builder|Candidate whereStatus($value)
 * @method static Builder|Candidate whereUpdatedAt($value)
 * @method static Builder|Candidate whereUtmCampaign($value)
 * @method static Builder|Candidate whereUtmMedium($value)
 * @method static Builder|Candidate whereUtmSource($value)
 * @mixin \Eloquent
 */
class Candidate extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasStates;
    use SoftDeletes;

    public const CANDIDATE_WAITING = 0; // -> Chờ xử lý

    public const CANDIDATE_READY = 1; // -> 1 tiếng sau gửi cv cho khách hàng

    public const CANDIDATE_SPAM = 2; // -> chờ update thêm thông tin

    public const CANDIDATE_NOT_ENOUGH_INFORMATION = 3; //-> reject không đủ điều kiện apply job

    public const CANDIDATE_DRAFT = 4;

    public const CANDIDATE_UNQUALIFIED = 5;

    public const CANDIDATE_RECALL = 6;

    /**
     * @inheritdoc
     */
    protected $casts = [
        'sent_employer_at' => 'datetime',
        'procedure_status' => ProcedureStatus::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::updating(function (self $model) {
            if ($model->isDirty('status')) {
                $model->statusLogs()->create([
                    'status_before' => $model->getOriginal('status') ?? 0,
                    'status' => $model->status,
                    'user_id' => $model->resume_id,
                    'reason' => 'updated',
                    'created_at' => now(),
                ]);
            }
        });
    }

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'status', 'employer_note', 'employer_read_at', 'procedure_status',
    ];

    public function resume(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resume_id')
            ->withDefault();
    }

    public static function getStatusDescription(): array
    {
        return [
            static::CANDIDATE_WAITING => 'Waiting',
            static::CANDIDATE_READY => 'Ready',
            static::CANDIDATE_UNQUALIFIED => 'Not matching',
            static::CANDIDATE_NOT_ENOUGH_INFORMATION => 'Not Enough Infomation',
            static::CANDIDATE_SPAM => 'Spam',
            static::CANDIDATE_RECALL => 'Recall',
        ];
    }

    public function getStatusText(): string
    {
        return static::getStatusDescription()[$this->status];
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class)
            ->withDefault();
    }

    public function statusLogs(): MorphMany
    {
        return $this->morphMany(StatusLog::class, 'model');
    }

    /**
     * download_cv_url attribute.
     *
     * @return string
     */
    public function getDownloadCvUrlAttribute(): string
    {
        return route('candidates.download-cv', ['id' => $this->id]);
    }

    public function cvMedia(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    public function getCVFile()
    {
        $this->getFirstMedia('files_cv');
    }

    //----------Scope Filter----------

    /**
     * Scope a query to only include popular users.
     *
     * @param  Builder  $builder
     *
     * @return Builder
     */
    public function scopeWithStatusReadyAndUnqualified(Builder $builder): Builder
    {
        return $builder
            ->where(function ($builder) {
                $builder
                    ->where('candidates.status', static::CANDIDATE_READY)
                    ->whereNotNull('sent_employer_at');
            })
            ->orWhere('candidates.status', static::CANDIDATE_UNQUALIFIED);
    }

    /**
     * Scope a query to order sent_employer_at or created_at.
     *
     * @param  Builder  $builder
     * @param  string  $sort
     *
     * @return Builder
     */
    public function scopeOrderSentEmployerOrCreated(Builder $builder, string $sort = 'DESC'): Builder
    {
        return $builder->orderByRaw(
            'IFNULL(candidates.sent_employer_at, candidates.created_at)' . $sort
        );
    }

    /**
     * is_remove_cv attribute.
     *
     * @return bool
     */
    public function getIsRemoveCvAttribute(): bool
    {
        return !$this->cvMedia()->exists();
    }

    public function scopeWithStatusReadyAndUnqualifiedAndRecall($builder)
    {
        return $builder
            ->where(function ($builder) {
                $builder
                    ->where('candidates.status', static::CANDIDATE_READY)
                    ->whereNotNull('sent_employer_at');
            })
            ->orWhere('candidates.status', static::CANDIDATE_UNQUALIFIED)
            ->orWhere('candidates.status', static::CANDIDATE_RECALL);
    }

    public function cvProfile(): BelongsTo
    {
        return $this->belongsTo(CvProfile::class);
    }

    public function cv_profile(): BelongsTo
    {
        return $this->cvProfile();
    }

    /**
     * cv_profile_experiences attribute.
     *
     * @return array
     */
    public function getCvProfileExperiencesAttribute(): array
    {
        if (isset($this->cv_profile)) {
            $experiences = $this->cv_profile->experiences;
            if ($experiences) {
                return collect($experiences)->map(function ($experience) {
                    if (isset($experience['skills'])) {
                        $experience['skills'] = collect($experience['skills'])->map(function ($skill) {
                            return $skill['skill_name'] ?? '';
                        })->toArray();
                    }

                    if (isset($experience['is_working_here']) && $experience['is_working_here']) {
                        $experience['is_working_here'] = ['1'];
                    }

                    if (isset($experience['from'])) {
                        $experience['from'] = date('Y/m', strtotime($experience['from']));
                    }
                    if (isset($experience['to'])) {
                        $experience['to'] = date('Y/m', strtotime($experience['to']));
                    }

                    return $experience;
                })->toArray();
            }
        }

        return [];
    }

    /**
     * cv_profile_educations attribute.
     *
     * @return array
     */
    public function getCvProfileEducationsAttribute(): array
    {
        if (isset($this->cv_profile)) {
            $educations = $this->cv_profile->educations;
            if ($educations) {
                return collect($educations)->map(function ($education) {
                    if (isset($education['is_studying_here']) && $education['is_studying_here']) {
                        $education['is_studying_here'] = ['1'];
                    }

                    if (isset($education['from'])) {
                        $education['from'] = date('Y/m', strtotime($education['from']));
                    }
                    if (isset($education['to'])) {
                        $education['to'] = date('Y/m', strtotime($education['to']));
                    }

                    return $education;
                })->toArray();
            }
        }

        return [];
    }

    public function scopeHasProcedureStatus(Builder $builder, $procedureStatus)
    {
        switch($procedureStatus) {
            case 'not_started':
                return $builder->whereNull('procedure_status');

            case NotMatching::$name:
                return $builder->where('procedure_status', NotMatching::$name);

            case Matched::$name:
                return $builder->whereState(
                    'procedure_status',
                    [
                        Matched::class,
                        InterviewAppointment::class,
                        Offer::class, Hired::class,
                        Failed::class
                    ]
                );

            case InterviewAppointment::$name:
                return $builder->whereState(
                    'procedure_status',
                    [
                        InterviewAppointment::class,
                        Offer::class,
                        Hired::class,
                        Failed::class
                    ]
                );

            case InterviewAppointment::$name:
                return $builder->whereState('procedure_status', [InterviewAppointment::class]);

            case Offer::$name:
                return $builder->whereState('procedure_status', [Offer::class, Hired::class, Failed::class]);

            case Hired::$name:
                return $builder->whereState('procedure_status', [Hired::class]);

            case Failed::$name:
                return $builder->whereState('procedure_status', [Failed::class]);

            default:
                return $builder;
        }
    }

    public function scopeBestMatch(Builder $builder): Builder
    {
        return $builder
            ->where('candidates.avg_skill_match', 1);
    }

    /**
     * Get year of experiences from meta
     *
     * @param \App\Models\Candidate $row
     * @return float
     */
    public function getYoeFromMeta()
    {
        $meta = $this->resume->meta;

        $yearOfExperience = null;
        if ($meta) {
            $yearOfExperience = $meta->where('key', 'years_of_exp')->first();
        }
        $yearOfExperience = str_replace(',', '.', $yearOfExperience ? $yearOfExperience->value : 0);

        return (float) $yearOfExperience;
    }
}
