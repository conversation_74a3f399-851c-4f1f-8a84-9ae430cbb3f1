<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;

/**
 * App\Models\SearchPackage
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $credit
 * @property int $is_active
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $product_id
 * @property int|null $expires_in
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage query()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereExpiresIn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereUpdatedBy($value)
 * @property-read \App\Models\SearchPackageTranslation|null $translation
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\SearchPackageTranslation> $translations
 * @property-read int|null $translations_count
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage listsTranslations(string $translationField)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage notTranslatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage orWhereTranslation(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage orWhereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage orderByTranslation(string $translationField, string $sortMethod = 'asc')
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage translated()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage translatedIn(?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereTranslation(string $translationField, $value, ?string $locale = null, string $method = 'whereHas', string $operator = '=')
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereTranslationLike(string $translationField, $value, ?string $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage withTranslation()
 * @mixin \Eloquent
 */
class SearchPackage extends Model implements TranslatableContract
{
    use Translatable;
    use HasFactory;

    /**
     * Transted attributes
     *
     * @var string[]
     */
    public $translatedAttributes = ['name', 'description'];

    /**
     * Set specific foreign key
     *
     * @var string
     */
    protected $translationForeignKey = 'search_package_id';

    protected $translationModel = SearchPackageTranslation::class;
}
