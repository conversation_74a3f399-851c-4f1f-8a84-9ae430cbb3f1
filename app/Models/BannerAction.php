<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BannerAction extends Model
{
    use HasFactory;

    protected $table = 'banner_actions';

    protected $connection = 'mysql_emp';

    protected $fillable = ['banner_id', 'action', 'username'];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    // Define actions for banner
    public const BANNER_CLICK_ACTION = 'click';

    // Define banner ids
    public const BLOCK_SIDE_BANNER_ID = 'block-side-banner';

    /**
     * Get actions for the model.
     *
     * @return array
     */
    public static function getActions()
    {
        return [
            static::BANNER_CLICK_ACTION,
        ];
    }

    /**
     * Get banner id for the model.
     *
     * @return array
     */
    public static function getBannerIds()
    {
        return [
            static::BLOCK_SIDE_BANNER_ID,
        ];
    }
}
