<?php

namespace App\Models;

use App\Enums\JobLevelEnum;
use App\Facades\Unleash;
use App\Helpers\CrmApi;
use App\Helpers\FeatureFlag;
use App\Http\Resources\Audit\AuditCompanyResource;
use Auth;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Support\Facades\DB;

/**
 * App\Models\Company.
 *
 * @property int $id
 * @property Media $logoImage
 * @property Collection $meta
 * @property string $uuid
 * @property string|null $display_name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $description
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $parent_id
 * @property string $slug
 * @property int|null $user_id
 * @property int $status
 * @property int $free_quota
 * @property int $free_quota_used
 * @property int $remaining_free_quota
 * @property int $free_job_opening_count
 * @property-read Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property mixed $benefits
 * @property-read string $detail_url
 * @property mixed $faqs
 * @property-read mixed $image_cover
 * @property-read \Illuminate\Support\Collection $image_galleries
 * @property-read Media $image_logo
 * @property-read Media $erc_file
 * @property-read mixed $industries_ids
 * @property-read mixed $nationalities
 * @property-read mixed $num_employees
 * @property-read mixed $skills_ids
 * @property mixed $social_network
 * @property mixed $tagline
 * @property mixed $website
 * @property-read Collection|Media[] $media
 * @property-read int|null $media_count
 * @property-read int|null $meta_count
 * @property-read Collection|CompanyProduct[] $products
 * @property-read int|null $products_count
 * @property-read Collection|Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property Collection $candidates
 * @method static Builder|Company newModelQuery()
 * @method static Builder|Company newQuery()
 * @method static Builder|Company query()
 * @method static Builder|Company whereCreatedAt($value)
 * @method static Builder|Company whereDeletedAt($value)
 * @method static Builder|Company whereDescription($value)
 * @method static Builder|Company whereDisplayName($value)
 * @method static Builder|Company whereEmail($value)
 * @method static Builder|Company whereId($value)
 * @method static Builder|Company whereParentId($value)
 * @method static Builder|Company wherePhone($value)
 * @method static Builder|Company whereSlug($value)
 * @method static Builder|Company whereStatus($value)
 * @method static Builder|Company whereUpdatedAt($value)
 * @method static Builder|Company whereUserId($value)
 * @method static Builder|Company whereUuid($value)
 * @property string|null $tax_number
 * @property-read Collection<int, \App\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read Collection<int, \App\Models\User> $blockedByUser
 * @property-read int|null $blocked_by_user_count
 * @property-read int|null $candidates_count
 * @property-read Collection<int, \App\Models\CompanySearchPackage> $companySearchPackage
 * @property-read int|null $company_search_package_count
 * @property-read Collection<int, \App\Models\CompanyCreditLog> $creditLogs
 * @property-read int|null $credit_logs_count
 * @property-read Collection<int, \App\Models\User> $employers
 * @property-read int|null $employers_count
 * @property-read mixed $available_credit
 * @property-read mixed $available_package
 * @property-read string $description_str
 * @property-read array $full_addresses
 * @property-read mixed $recruitment_process
 * @property-read string $status_display
 * @property-read Collection<int, \App\Models\Job> $jobs
 * @property-read int|null $jobs_count
 * @property-read Collection<int, \App\Models\SearchCandidate> $savedSearchCandidates
 * @property-read int|null $saved_search_candidates_count
 * @property-read Collection<int, \App\Models\SearchCandidate> $searchCandidates
 * @property-read int|null $search_candidates_count
 * @property-read Collection<int, \App\Models\SearchCandidate> $unlockedSearchCandidates
 * @property-read int|null $unlocked_search_candidates_count
 * @method static Builder|Company onlyTrashed()
 * @method static Builder|Company whereTaxNumber($value)
 * @method static Builder|Company withTrashed()
 * @method static Builder|Company withoutTrashed()
 * @mixin Eloquent
 */
class Company extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use SoftDeletes;

    public const FEATURE_ENABLE = 'on';

    public const FEATURE_DISABLE = 'off';

    public const STATUS_ACTIVE = 1;

    public const STATUS_INACTIVE = 2;

    public const STATUS_REVIEW = 3;

    public const STATUS_WAITING = 4;

    public const LIMITED_FREE_QUOTA = 5;

    protected $fillable = [
        'display_name',
        'description',
        'website',
        'tagline',
        'social_network',
        'benefits',
        'faqs',
        'email',
        'phone',
        'company_request_buy_package_ticket_id',
        'free_quota',
        'free_quota_used',
    ];

    protected static function boot()
    {
        parent::boot();

        /* Bring meta to live */
        self::saved(function (self $company) {
            $company->meta->each(function (Meta $meta) {
                if ($meta->isDirty()) {
                    $meta->save();
                }
            });
        });
    }

    public function meta(): MorphMany
    {
        return $this->morphMany(Meta::class, 'metable');
    }

    protected function getMetaByKey($key, $type = null, $defaultValue = null): Meta
    {
        $meta = $this->meta->first(fn ($meta) => $meta->key == $key);

        if ($meta == null) {
            $meta = $this->meta()->make([
                'key' => $key,
            ]);

            if ($type) {
                $meta->type = $type;
            }

            if ($defaultValue) {
                $meta->value = $defaultValue;
            }

            $this->meta->push($meta);
        }

        return $meta;
    }

    public function products(): HasMany
    {
        return $this->hasMany(CompanyProduct::class)
            ->with([
                'media',
            ]);
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable')
            ->orderBy('order')
            ->with([
                'ward',
                'province',
                'district',
            ]);
    }

    public function taxonomies(): MorphToMany
    {
        return $this->morphToMany(Taxonomy::class, 'gables', 'term_relationships', 'gables_id', 'taxonomy_id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image_logo')->singleFile();
        $this->addMediaCollection('image_cover')->singleFile();
        $this->addMediaCollection('image_galleries');
        $this
            ->addMediaCollection('erc')
            ->acceptsMimeTypes(['application/pdf'])
            ->singleFile();
    }

    public function getImageLogoAttribute()
    {
        return $this->getMedia('image_logo')->first();
    }

    public function getImageCoverAttribute()
    {
        return $this->getMedia('image_cover')->first();
    }

    public function getImageGalleriesAttribute(): \Illuminate\Support\Collection
    {
        return $this->getMedia('image_galleries');
    }

    public function getErcFileAttribute()
    {
        return $this->getMedia('erc')->first();
    }

    public function getDetailUrlAttribute(): string
    {
        return 'https://topdev.vn/companies/' .
            $this->slug .
            '-' .
            $this->id;
    }

    public function getWebsiteAttribute()
    {
        return $this->getMetaByKey('website')->value;
    }

    public function setWebsiteAttribute($value)
    {
        $website = $this->getMetaByKey('website');

        $website->value = $value;
        $website->type = 'string';
        $website->key = 'website';
    }

    public function getTaglineAttribute()
    {
        return $this->getMetaByKey('tagline')->value;
    }

    public function setTaglineAttribute($value)
    {
        $tagline = $this->getMetaByKey('tagline');

        $tagline->value = $value;
        $tagline->type = 'string';
        $tagline->key = 'tagline';
    }

    public function getSocialNetworkAttribute()
    {
        $socials = collect(json_decode($this->getMetaByKey('social_network')->value))
            ->pluck('url', 'social');

        foreach (['facebook', 'linkedin', 'youtube', 'share-alt'] as $social) {
            if (!isset($socials[$social])) {
                $socials[$social] = '';
            }
        }

        return $socials;
    }

    public function setSocialNetworkAttribute($value)
    {
        $socialNetworks = $this->getMetaByKey('social_network');

        $newSocialNetworks = collect($value)
            ->map(function ($item, $key) {
                return [
                    'social' => $key,
                    'url' => $item,
                ];
            })
            ->filter(fn ($item) => $item['url'] != '')
            ->values();

        $socialNetworks->value = json_encode($newSocialNetworks);
        $socialNetworks->type = 'array';
        $socialNetworks->key = 'social_network';
    }

    public function getBenefitsAttribute()
    {
        return json_decode($this->getMetaByKey('benefits')->value);
    }

    public function setBenefitsAttribute($value)
    {
        $benefits = $this->getMetaByKey('benefits');

        $benefits->value = json_encode($value);
        $benefits->type = 'array';
        $benefits->key = 'benefits';
    }

    public function getFaqsAttribute()
    {
        return json_decode($this->getMetaByKey('faqs')->value) ?? [];
    }

    public function setFaqsAttribute($value)
    {
        $faqs = $this->getMetaByKey('faqs');

        $faqs->value = json_encode(Arr::wrap($value));
        $faqs->type = 'array';
        $faqs->key = 'faqs';
    }

    public function getRecruitmentProcessAttribute()
    {
        return json_decode(
            $this->getMetaByKey('recruitment_process', 'array', '[]')->value
        );
    }

    public function getNationalitiesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'nationalities')->pluck('id');
    }

    public function getNumEmployeesAttribute()
    {
        $taxonomy = $this->taxonomies->first(fn ($taxonomy) => $taxonomy->taxonomy == 'num_employees');

        if (!$taxonomy) {
            return null;
        }

        return $taxonomy->id;
    }

    public function getSkillsIdsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'skills')->pluck('id');
    }

    public function getIndustriesIdsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'industries')->pluck('id');
    }

    public function getFullAddressesAttribute(): array
    {
        return $this->addresses->pluck('full_address')->all();
    }

    public function getDescriptionStrAttribute(): string
    {
        return preg_replace("/[\n\r]/", '', html_entity_decode(strip_tags($this->description)));
    }

    public static function getStatusDescription(): array
    {
        return [
            static::STATUS_ACTIVE => 'Active',
            static::STATUS_INACTIVE => 'Inactive',
        ];
    }

    public function getStatusDisplayAttribute(): string
    {
        return static::getStatusDescription()[$this->status] ?? 'None';
    }

    //default get Job has status in [closed, review, open]
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'owned_id')
            ->whereIn('jobs.status', [Job::STATUS_CLOSED, Job::STATUS_REVIEW, Job::STATUS_OPEN, Job::STATUS_DRAFT])
            ->whereHas('note', fn($query) => $query->whereNull('link_crawl'));
    }

    public function candidates(): HasManyThrough
    {
        return $this->hasManyThrough(
            Candidate::class,
            Job::class,
            'owned_id',
            'job_id',
            'id',
            'id'
        );
    }

    public function employers(): HasMany
    {
        return $this->hasMany(User::class, 'company_id')
            ->where('type', User::EMPLOYER_TYPE);
    }

    public function audits(): MorphMany
    {
        return $this->morphMany(
            Audit::class,
            'auditable'
        );
    }

    public function getAuditCompanyResource()
    {
        return AuditCompanyResource::make($this->loadMissing(['meta', 'addresses.province', 'addresses.district', 'addresses.ward']))->toArray(request());
    }

    public function isInactive(): bool
    {
        return $this->status == self::STATUS_INACTIVE;
    }

    public function searchCandidates(): BelongsToMany
    {
        return $this->belongsToMany(SearchCandidate::class, 'company_save_search_candidates')
            ->using(CompanySaveSearchCandidate::class);
    }

    public function unlockedSearchCandidates(): BelongsToMany
    {
        return $this->belongsToMany(SearchCandidate::class, 'company_unlock_search_candidates')
            ->using(CompanyUnlockSearchCandidate::class)
            ->wherePivot('company_unlock_search_candidates.expired_at', '>', Carbon::now());
    }

    public function savedSearchCandidates(): BelongsToMany
    {
        return $this->belongsToMany(SearchCandidate::class, 'company_save_search_candidates')
            ->using(CompanySaveSearchCandidate::class);
    }

    public function companySearchPackage(): HasMany
    {
        return $this->hasMany(CompanySearchPackage::class);
    }

    /**
     * Get the available packages for the company.
     * Accessor to get the available packages.
     * ->valid_available_credit
     *
     * @return \Illuminate\Support\Collection
     */
    public function getValidAvailableCreditAttribute()
    {
        return $this->companySearchPackage()
            ->isValid();
    }

    /**
     * Get the available credit for the company.
     * Accessor to get the available credit.
     * ->available_credit
     *
     * @return int
     */
    public function getAvailableCreditAttribute(): int
    {
        return $this->companySearchPackage()
            ->isValid()
            ->sum('remain_credit');
    }

    public function creditLogs(): HasMany
    {
        return $this->hasMany(CompanyCreditLog::class);
    }

    /**
     * Get the remaining free job quota for the company.
     * Accessor to get the remaining free job quota.
     * ->remaining_free_quota
     *
     * @return int
     */
    public function getRemainingFreeQuotaAttribute(): int
    {
        return max(0, $this->free_quota);
    }

    public function blockedByUser(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_block_companies');
    }

    public function getUserIdBlockCompany(): \Illuminate\Support\Collection
    {
        $userIdBlockByDomain = (new UserBlockCompanies())->getUserIdBlockCompanyByEmails([
            Auth::user()->email, /* @phpstan-ignore-line */
            $this->email,
        ]);

        return $this
            ->blockedByUser()
            ->pluck('user_id')
            ->merge($userIdBlockByDomain);
    }

    public function jobPostingLogs(): HasMany
    {
        return $this->hasMany(CompanyJobPostingLog::class);
    }

    public function isRequestingPackageTicket()
    {
        if (! $this->company_request_buy_package_ticket_id) {
            return false;
        }

        $ticket = CrmApi::getTicket($this->company_request_buy_package_ticket_id);

        return Arr::get($ticket, 'data.ticket.status') != '5'; // Ticket closed
    }

    public function hasFreeJobPostingOpened()
    {
        // Logic gì mà phi logic zay
        return $this->jobs()->where('jobs.status', Job::STATUS_OPEN)->doesntExist();
    }

    /**
     * Accessor to get the free job opening count.
     * ->free_job_opening_count
     *
     * @return int
     */
    public function getFreeJobOpeningCountAttribute(): int
    {
        return $this->jobs()
            ->where('level', JobLevelEnum::FREE->value)
            ->whereIn('status', [Job::STATUS_OPEN])
            ->count();
    }

    public function hasFreeITJobPostingOpening(): bool
    {
        return $this->jobs()
            ->where('level', JobLevelEnum::FREE->value)
            ->whereIn('status', [Job::STATUS_OPEN])
            ->whereHas('job_categories', function ($query) {
                $query->where('job_category_id', JobCategory::IT_CATEGORY_ID);
            })
            ->exists();
    }

    public function hasFreeNonITJobPostingOpening(): int
    {
        $jobCount = $this->jobs()
            ->where('level', JobLevelEnum::FREE->value)
            ->whereIn('status', [Job::STATUS_OPEN])
            ->whereHas('job_categories', function ($query) {
                $query->whereNot('job_category_id', JobCategory::IT_CATEGORY_ID);
            })
            ->count();

        return min(self::LIMITED_FREE_QUOTA, $jobCount);
    }

    /**
     * Check if the company has free job posting quota.
     *
     * @return bool
     */
    public function hasFreeJobPostingQuota(): bool
    {
        if ($this->remaining_free_quota <= 0) {
            return false;
        }

        $jobOpenQuery = $this->jobs()
            ->where('level', JobLevelEnum::FREE->value)
            ->whereIn('status', [Job::STATUS_OPEN]);

        $jobNonITOpened = $jobOpenQuery
            ->whereHas('job_categories', function ($query) {
                $query->whereNot('job_category_id', JobCategory::IT_CATEGORY_ID);
            })
            ->count();
        if ($jobNonITOpened >= self::LIMITED_FREE_QUOTA) {
            return false;
        }

        // check Job has IT job category has been opening
        $usedITFreeJobs = $jobOpenQuery
            ->whereHas('job_categories', function ($query) {
                $query->where('job_category_id', JobCategory::IT_CATEGORY_ID);
            })
            ->count();

        if ($usedITFreeJobs >= 1) {
            return false;
        }

        return true;
    }
}
