<?php

namespace App\Models\Concerns;

class Salary
{
    protected $is_negotiable = 0;

    protected $unit = 'MONTH';

    protected $value = '';

    protected $estimate = '';

    /**
     * Official min value.
     *
     * @var mixed
     */
    protected $min = 0;

    /**
     * Official max value.
     *
     * @var mixed
     */
    protected $max = 0;

    /**
     * Official currency.
     *
     * @var mixed
     */
    protected $currency = 'VND';

    /**
     * Min estimate value.
     *
     * @var mixed
     */
    protected $min_estimate = 0;

    /**
     * Max estimate value.
     *
     * @var mixed
     */
    protected $max_estimate = 0;

    /**
     * Estimate currency.
     *
     * @var string
     */
    protected string $currency_estimate = 'VND';

    /**
     * Salary constructor.
     *
     * @param  mixed  $value
     */
    public function __construct($value = [])
    {
        collect($value)->each(function ($item, $key) {
            $this->{$key} = $item;
        });

        //set is_negotiable for old data (not has is_negotiable) or new data with is_negotiable == 1
        if ($this->value === 'Negotiable' || blank($this->currency)) {
            $this->is_negotiable = true;
        }
    }

    /**
     * Returns params payload.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'is_negotiable' => (int) $this->is_negotiable,
            'unit' => $this->unit,

            'min' => (int) $this->min,
            'max' => (int) $this->max,
            'currency' => $this->currency,

            'min_estimate' => (int) $this->min_estimate,
            'max_estimate' => (int) $this->max_estimate,
            'currency_estimate' => $this->currency_estimate,

            'value' => $this->is_negotiable
                ? 'Negotiable'
                : $this->formatMinMaxString($this->min, $this->max, $this->currency),

            'estimate' => $this->formatMinMaxString($this->min_estimate, $this->max_estimate, $this->currency_estimate),
        ];
    }

    public function toRawArray(): array
    {
        return [
            'is_negotiable' => (bool) $this->is_negotiable,
            'unit' => $this->unit,

            'min' => (int) $this->min,
            'max' => (int) $this->max,
            'currency' => $this->currency,

            'min_estimate' => (int) $this->min_estimate,
            'max_estimate' => (int) $this->max_estimate,
            'currency_estimate' => $this->currency_estimate,
        ];
    }

    public function formatMinMaxString($min, $max, $currency): string
    {
        if (!$min && !$max) {
            return '';
        }

        if (!$min && $max) {
            return 'Up to ' . $this->formatMoney($max, $currency);
        }

        if ($min && !$max) {
            return 'From ' . $this->formatMoney($min, $currency);
        }

        return $this->formatMoney($min, $currency) . ' to ' . $this->formatMoney($max, $currency);
    }

    public function formatMoney($value, $currency): string
    {
        return number_format($value, 0, ',', '.') . ' ' . $currency;
    }
}
