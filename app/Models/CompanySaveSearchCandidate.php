<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * App\Models\CompanySaveSearchCandidate.
 *
 * @property int $id
 * @property int $company_id
 * @property int $search_candidate_id
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereSearchCandidateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySaveSearchCandidate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CompanySaveSearchCandidate extends Pivot
{
    use HasFactory;

    protected $table = 'company_save_search_candidates';

    protected $fillable = [
        'id',
        'company_id',
        'search_candidate_id',
        'created_by',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function (self $model) {
            $model->created_by = auth()->user()->id;
            $model->created_at = now();
        });
    }
}
