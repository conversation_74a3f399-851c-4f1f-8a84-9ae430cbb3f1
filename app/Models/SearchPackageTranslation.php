<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\SearchPackageTranslation
 *
 * @property int $id
 * @property string $locale
 * @property int $search_package_id
 * @property string|null $name
 * @property string|null $description
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackageTranslation whereSearchPackageId($value)
 * @mixin \Eloquent
 */
class SearchPackageTranslation extends Model
{
    protected $fillable = ['name', 'description'];
    
    /**
     * Set timestamps
     * 
     * @var boolean
     */
    public $timestamps = false;
}
