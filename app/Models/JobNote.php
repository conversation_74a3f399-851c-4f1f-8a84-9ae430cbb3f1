<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\JobNote.
 *
 * @property int $id
 * @property int $job_id
 * @property string|null $link_crawl
 * @property array|null $recruiment_process
 * @property mixed|null $notes_label
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 *
 * @method static Builder|JobNote newModelQuery()
 * @method static Builder|JobNote newQuery()
 * @method static \Illuminate\Database\Query\Builder|JobNote onlyTrashed()
 * @method static Builder|JobNote query()
 * @method static Builder|JobNote whereCreatedAt($value)
 * @method static Builder|JobNote whereDeletedAt($value)
 * @method static Builder|JobNote whereId($value)
 * @method static Builder|JobNote whereJobId($value)
 * @method static Builder|JobNote whereLinkCrawl($value)
 * @method static Builder|JobNote whereNotesLabel($value)
 * @method static Builder|JobNote whereRecruimentProcess($value)
 * @method static Builder|JobNote whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|JobNote withTrashed()
 * @method static \Illuminate\Database\Query\Builder|JobNote withoutTrashed()
 * @mixin \Eloquent
 */
class JobNote extends Model
{
    use HasFactory;
    use SoftDeletes;

    public const NOTE_LABEL_FREE = 'Free';

    protected $casts = [
        'recruiment_process' => 'array',
        'notes_label' => 'json',
    ];

    protected $fillable = [
        'recruiment_process',
        'note',
        'notes_label',
        'employer_notes',
    ];

    public function setNoteAttribute($value)
    {
        $this->attributes['employer_notes'] = $value;
    }
}
