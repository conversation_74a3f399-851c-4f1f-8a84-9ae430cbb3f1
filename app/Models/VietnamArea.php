<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\VietnamArea.
 *
 * @property int $id
 * @property string $code
 * @property string $parent_code
 * @property string $name
 * @property string $name_with_type
 * @property string|null $path
 * @property string|null $path_with_type
 * @property string $type
 * @property string $slug
 * @property int $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string $postal_code
 *
 * @method static Builder|VietnamArea newModelQuery()
 * @method static Builder|VietnamArea newQuery()
 * @method static Builder|VietnamArea query()
 * @method static Builder|VietnamArea whereCode($value)
 * @method static Builder|VietnamArea whereCreatedAt($value)
 * @method static Builder|VietnamArea whereDeletedAt($value)
 * @method static Builder|VietnamArea whereId($value)
 * @method static Builder|VietnamArea whereName($value)
 * @method static Builder|VietnamArea whereNameWithType($value)
 * @method static Builder|VietnamArea whereParentCode($value)
 * @method static Builder|VietnamArea wherePath($value)
 * @method static Builder|VietnamArea wherePathWithType($value)
 * @method static Builder|VietnamArea wherePostalCode($value)
 * @method static Builder|VietnamArea whereSlug($value)
 * @method static Builder|VietnamArea whereStatus($value)
 * @method static Builder|VietnamArea whereType($value)
 * @method static Builder|VietnamArea whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class VietnamArea extends Model
{
    use HasFactory;

    public const VIETNAM_AREA_PROVINCE = ['tinh', 'thanh-pho'];
    public const VIETNAM_AREA_DISTRICT = ['quan', 'huyen'];
    public const VIETNAM_AREA_WARD = ['xa', 'phuong', 'thi-xa'];

    protected $table = 'vietnam_area';
}
