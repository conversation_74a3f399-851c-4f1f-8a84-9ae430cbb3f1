<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\JobCounts.
 *
 * @property int $id
 * @property int $job_id
 * @property int $num_viewers
 * @property int $candidate_ready_count
 * @property int $candidates_count
 * @property int $click_apply_count
 * @property int $refresh_count
 * @property int $candidates_not_matching_count
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts newQuery()
 * @method static \Illuminate\Database\Query\Builder|JobCounts onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts query()
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereCandidateReadyCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereCandidatesCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereCandidatesNotMatchingCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereClickApplyCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereNumViewers($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereRefreshCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|JobCounts whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|JobCounts withTrashed()
 * @method static \Illuminate\Database\Query\Builder|JobCounts withoutTrashed()
 * @mixin \Eloquent
 */
class JobCounts extends Model
{
    use HasFactory;
    use SoftDeletes;
}
