<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class SearchCandidateViewLog extends Pivot
{
    use HasFactory;

    protected $table = 'search_candidates_view_logs';

    protected $fillable = [
        'search_candidate_id',
        'viewer_id',
        'company_id',
        'credit',
        'created_at',
        'updated_at',
    ];
}
