<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * App\Models\Media.
 *
 * @property mixed $created_at
 * @property mixed $file_name
 * @property mixed $url
 * @property int $id
 * @property string $model_type
 * @property int $model_id
 * @property string $collection_name
 * @property string $name
 * @property string|null $raw_text
 * @property string|null $raw_text_at
 * @property string|null $mime_type
 * @property string $disk
 * @property int $size
 * @property array $manipulations
 * @property array $custom_properties
 * @property array $responsive_images
 * @property int|null $order_column
 * @property int|null $creator_id
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $path_folder
 * @property-read string $extension
 * @property-read string $human_readable_size
 * @property-read string $type
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $model
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Media newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Media newQuery()
 * @method static \Illuminate\Database\Query\Builder|Media onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Media ordered()
 * @method static \Illuminate\Database\Eloquent\Builder|Media query()
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereCollectionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereCustomProperties($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereDisk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereManipulations($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereMimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereOrderColumn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media wherePathFolder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereRawText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereRawTextAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereResponsiveImages($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Media withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Media withoutTrashed()
 * @property string|null $md5_hash
 * @property-read MediaCvParseLog|null $cvParseLog
 * @property-read UserMainCV|null $mainCv
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereMd5Hash($value)
 * @mixin \Eloquent
 */
class Media extends \Spatie\MediaLibrary\MediaCollections\Models\Media implements HasMedia
{
    use HasFactory;
    use SoftDeletes;
    use InteractsWithMedia;

    /**
     * Get url property.
     *
     * @return string
     */
    public function getUrlAttribute(): string
    {
        return 'https://assets.topdev.asia/images/' .
            $this->created_at->format('Y/m/d/') .
            $this->file_name;
    }

    /**
     * Get "path_folder".
     */
    public function getPathFolder($type): ?string
    {
        if (!empty($this->path_folder)) {
            return $this->path_folder;
        }

        $directory = $this->created_at->format('Y/m/d');

        return empty($type) ? $directory : $type . '/' . $directory;
    }

    public function mainCv(): MorphOne
    {
        return $this->morphOne(UserMainCV::class, 'cv');
    }

    public function cvParseLog(): HasOne
    {
        return $this->hasOne(MediaCvParseLog::class, 'media_id');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('redact_cv')->singleFile();
    }

    public function redactFile()
    {
        return $this->getMedia('redact_cv')->first();
    }
}
