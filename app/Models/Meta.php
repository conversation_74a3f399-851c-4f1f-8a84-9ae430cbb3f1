<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Meta.
 *
 * @property mixed $value
 * @property int $id
 * @property string $metable_type
 * @property int $metable_id
 * @property string|null $type
 * @property string|null $key
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static Builder|Meta newModelQuery()
 * @method static Builder|Meta newQuery()
 * @method static Builder|Meta query()
 * @method static Builder|Meta whereCreatedAt($value)
 * @method static Builder|Meta whereId($value)
 * @method static Builder|Meta whereKey($value)
 * @method static Builder|Meta whereMetableId($value)
 * @method static Builder|Meta whereMetableType($value)
 * @method static Builder|Meta whereType($value)
 * @method static Builder|Meta whereUpdatedAt($value)
 * @method static Builder|Meta whereValue($value)
 * @mixin \Eloquent
 */
class Meta extends Model
{
    use HasFactory;

    /**
     * Table name of this model.
     *
     * @var string
     */
    protected $table = 'meta';

    protected $fillable = ['type', 'key', 'value'];
}
