<?php

namespace App\Models;

use App\Enums\JobCategoryType;
use App\Http\Resources\Audit\AuditJobResource;
use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\InteractsWithMedia;
use Str;

/**
 * App\Models\Job.
 *
 * @method static find($jobId)
 * @method static create(array $only)
 * @property int $id
 * @property string $uuid
 * @property string $slug
 * @property int $status
 * @property string $title
 * @property string|null $content
 * @property string|null $requirements
 * @property string|null $responsibilities
 * @property string|null $company_tagline
 * @property int|null $crm_request_design_id
 * @property string|null $company_logo
 * @property int|null $owned_id
 * @property int|null $parent_id
 * @property int|null $creator_id
 * @property string|null $content_html_desktop
 * @property string|null $content_html_mobile
 * @property string|null $expires_at
 * @property string|null $emails_cc
 * @property mixed|null $hackerrank
 * @property string|null $lever_id
 * @property string|null $hot
 * @property string|null $refreshed_at
 * @property string|null $published_at
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read JobCounts|null $count
 * @property-read mixed $addresses_id
 * @property-read mixed $benefits
 * @property-read mixed $experiences
 * @property-read mixed $role
 * @property-read mixed $job_levels
 * @property-read mixed $job_types
 * @property-read mixed $recruiment_process
 * @property-read mixed $taxonomy_requirements
 * @property-read mixed $taxonomy_responsibilities
 * @property-read mixed $taxonomy_recruitment_processes
 * @property-read mixed $education_degree
 * @property-read mixed $taxonomy_benefits
 * @property-read mixed $job_banner
 * @property-read mixed $job_template
 * @property-read mixed $job_template_color
 * @property-read mixed $salary
 * @property-read mixed $skills_ids
 * @property-read JobInformation|null $information
 * @property-read JobNote|null $note
 * @property-read \Illuminate\Database\Eloquent\Collection|Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read string $detail_url
 * @method static Builder|Job newModelQuery()
 * @method static Builder|Job newQuery()
 * @method static \Illuminate\Database\Query\Builder|Job onlyTrashed()
 * @method static Builder|Job query()
 * @method static Builder|Job whereContent($value)
 * @method static Builder|Job whereCreatedAt($value)
 * @method static Builder|Job whereCreatorId($value)
 * @method static Builder|Job whereDeletedAt($value)
 * @method static Builder|Job whereEmailsCc($value)
 * @method static Builder|Job whereExpiresAt($value)
 * @method static Builder|Job whereHackerrank($value)
 * @method static Builder|Job whereHot($value)
 * @method static Builder|Job whereId($value)
 * @method static Builder|Job whereLeverId($value)
 * @method static Builder|Job whereOwnedId($value)
 * @method static Builder|Job whereParentId($value)
 * @method static Builder|Job wherePublishedAt($value)
 * @method static Builder|Job whereRefreshedAt($value)
 * @method static Builder|Job whereRequirements($value)
 * @method static Builder|Job whereResponsibilities($value)
 * @method static Builder|Job whereSlug($value)
 * @method static Builder|Job whereStatus($value)
 * @method static Builder|Job whereTitle($value)
 * @method static Builder|Job whereUpdatedAt($value)
 * @method static Builder|Job whereUuid($value)
 * @method static \Illuminate\Database\Query\Builder|Job withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Job withoutTrashed()
 * @property string|null $blog_tags
 * @property mixed|null $blog_posts
 * @property string|null $sidebar_image_link
 * @property int|null $crm_invoice_id
 * @property string|null $level
 * @property string|null $original_published_at
 * @property string|null $education_certificate
 * @property string|null $recruitment_processes
 * @property string|null $benefits_description
 * @property bool|null $is_content_image
 * @property JobCategory|null $job_category
 * @property int|null $job_category_id
 * @property-read array $job_category_role_ids
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Announcement> $announcements
 * @property-read int|null $announcements_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Candidate> $candidates
 * @property-read int|null $candidates_count
 * @property-read \App\Models\Company|null $company
 * @property-read \App\Models\User $creator
 * @property-read mixed $contract_type_ids
 * @property-read mixed $contract_types
 * @property-read mixed $experiences_ids
 * @property-read array $full_addresses
 * @property-read array $education_major
 * @property-read mixed $is48h
 * @property-read mixed $levels
 * @property-read mixed $package_id
 * @property-read mixed $salary_value
 * @property-read mixed $skills
 * @property-read mixed $types
 * @property-read string $preview_url
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Package> $packages
 * @property-read int|null $packages_count
 * @method static Builder|Job findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static Builder|Job whereBlogPosts($value)
 * @method static Builder|Job whereBlogTags($value)
 * @method static Builder|Job whereCrmInvoiceId($value)
 * @method static Builder|Job whereLevel($value)
 * @method static Builder|Job whereOriginalPublishedAt($value)
 * @method static Builder|Job whereSidebarImageLink($value)
 * @method static Builder|Job withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @mixin \Eloquent
 */
class Job extends Model implements HasMedia
{
    use HasFactory;
    use SoftDeletes;
    use Sluggable;
    use InteractsWithMedia;

    public const STATUS_DRAFT = 0;

    public const STATUS_CLOSED = 1;

    public const STATUS_REVIEW = 2;

    public const STATUS_OPEN = 3;

    protected $fillable = [
        'title',
        'content',
        'content_html_desktop',
        'content_html_mobile',
        'level',
        'requirements',
        'responsibilities',
        'emails_cc',
        'uuid',
        'status',
        'creator_id',
        'refreshed_at',
        'published_at',
        'crm_invoice_id',
        'education_certificate',
        'company_tagline',
        'crm_request_design_id',
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'premium_upgraded_at' => 'datetime:Y-m-d H:i:s',
        'premium_enabled_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => ['title', 'company.display_name'],
            ],
        ];
    }

    public function taxonomies(): MorphToMany
    {
        return $this
            ->morphToMany(Taxonomy::class, 'gables', 'term_relationships', 'gables_id', 'taxonomy_id')
            ->using(Taxable::class)->withPivot('expires_at', 'custom_properties');
    }

    public function information(): HasOne
    {
        return $this->hasOne(JobInformation::class)
            ->withDefault();
    }

    public function count(): HasOne
    {
        return $this->hasOne(JobCounts::class)->withDefault([
            'num_viewers' => 0,
            'candidate_ready_count' => 0,
            'candidates_count' => 0,
            'click_apply_count' => 0,
            'refresh_count' => 0,
            'candidates_not_matching_count' => 0,
        ]);
    }

    public function note(): HasOne
    {
        return $this->hasOne(JobNote::class);
    }

    public function packages(): MorphToMany
    {
        return $this->morphToMany(
            Package::class,
            'subscriptionable',
            'subscriptionables',
            'subscriptionable_id',
            'package_id'
        );
    }

    public function creator(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'creator_id')
            ->withDefault(config('topdev.creator.default'));
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaCollection('company_logo')
            ->singleFile();
    }

    /**
     * Accessor for experiences attribute. (->experiences)
     */
    public function getExperiencesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'experiences')->pluck('term.name');
    }

    /**
     * Accessor for role attribute. (->role)
     */
    public function getRoleAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'role')->pluck('term.name');
    }

    public function getRoleIdAttribute()
    {
        $role = $this->taxonomies->where('taxonomy', 'role')->first();

        return $role?->getKey();
    }

    /**
     * Accessor for education_major attribute. (->education_major)
     */
    public function getEducationMajorAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education_major')->pluck('term.name');
    }

    public function getEducationMajorIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education_major')->pluck('id');
    }

    /**
     * Accessor for experiences_ids attribute. (->experiences_ids)
     */
    public function getExperiencesIdsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'experiences')->pluck('id');
    }

    /**
     * Accessor for requirements attribute. (->taxonomy_requirements)
     */
    public function getTaxonomyRequirementsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'requirements')->pluck('term.name');
    }

    /**
     * Accessor for taxonomy_responsibilities attribute. (->taxonomy_responsibilities)
     */
    public function getTaxonomyResponsibilitiesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'responsibilities')->pluck('term.name');
    }

    /**
     * Accessor for taxonomy_recruitment_processes attribute. (->taxonomy_recruitment_processes)
     */
    public function getTaxonomyRecruitmentProcessesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'recruitment_processes')->pluck('term.name');
    }

    /**
     * Accessor for education_degree attribute from education taxonomy. (->education_degree)
     */
    public function getEducationDegreeAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education')->pluck('term.name');
    }

    public function getEducationDegreeIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'education')->pluck('id');
    }

    /**
     * Accessor for taxonomy_benefits attribute. (->taxonomy_benefits)
     */
    public function getTaxonomyBenefitsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'benefits')->pluck('term.name');
    }

    /**
     * Accessor for job_banner attribute. (->job_banner)
     */
    public function getJobBannerAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_banner')->pluck('term.name');
    }

    /**
     * Accessor for job_template attribute. (->job_template)
     */
    public function getJobTemplateAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template')->pluck('term.name');
    }

    /**
     * Accessor for job_template_color attribute. (->job_template_color)
     */
    public function getJobTemplateColorAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template_color')->pluck('term.name');
    }

    /**
     * Accessor for company_logo attribute. (->company_logo)
     */
    public function getCompanyLogoAttribute()
    {
        return $this->getMedia('company_logo')->first();
    }

    /**
     * Accessor for contract_types attribute. (->contract_types)
     */
    public function getContractTypesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'contract_types')->pluck('term.name');
    }

    /**
     * Accessor for contract_type_ids attribute. (->contract_type_ids)
     */
    public function getContractTypeIdsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'contract_types')->pluck('id');
    }

    /**
     * Accessor for salary attribute. (->salary)
     */
    public function getSalaryAttribute()
    {
        return $this->information->salary;
    }

    /**
     * Accessor for salary_value attribute. (->salary_value)
     */
    public function getSalaryValueAttribute()
    {
        return Arr::get(
            $this->information->salary,
            'value',
            'Negotiable'
        );
    }

    /**
     * Accessor for recruiment_process attribute. (->recruiment_process)
     */
    public function getRecruimentProcessAttribute()
    {
        return $this->note->recruiment_process;
    }

    /**
     * Accessor for skills attribute. (->skills)
     */
    public function getSkillsAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'skills')
            ->pluck('term.name');
    }

    /**
     * Accessor for skills_ids attribute. (->skills_ids)
     *
     * @return Collection
     */
    public function getSkillsIdsAttribute(): Collection
    {
        return $this->taxonomies->where('taxonomy', 'skills')->pluck('id');
    }

    /**
     * Accessor for levels attribute. (->levels)
     */
    public function getLevelsAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'job_levels')
            ->pluck('term.name');
    }

    /**
     * Accessor for job_levels attribute. (->job_levels)
     */
    public function getJobLevelsAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_levels')->pluck('id');
    }

    /**
     * Accessor for types attribute. (->types)
     */
    public function getTypesAttribute()
    {
        return $this
            ->taxonomies
            ->where('taxonomy', 'job_types')
            ->pluck('term.name');
    }

    /**
     * Accessor for job_types attribute. (->job_types)
     */
    public function getJobTypesAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_types')->pluck('id');
    }

    public function addresses(): BelongsToMany
    {
        return $this->belongsToMany(Address::class);
    }

    /**
     * Accessor for addresses_id attribute. (->addresses_id)
     */
    public function getAddressesIdAttribute()
    {
        return $this->addresses->pluck('id');
    }

    /**
     * Accessor for benefits attribute. (->benefits)
     */
    public function getBenefitsAttribute()
    {
        return $this
        ->taxonomies()
        ->where('taxonomy', 'benefits')
        ->get()
        ->map(function ($taxonomy) {
            return [
                'id' => $taxonomy->id,
                'name' => $taxonomy->term->name,
                'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                'icon' => Arr::first($taxonomy->thumbnail_url),
            ];
        })
        ->values();
    }

    /**
     * Accessor for emails_cc attribute. (->emails_cc)
     */
    public function getEmailsCcAttribute()
    {
        return explode(',', Arr::get($this->attributes, 'emails_cc'));
    }

    /**
     * Mutator for emails_cc attribute. (->emails_cc)
     */
    public function setEmailsCcAttribute($value)
    {
        $this->attributes['emails_cc'] = implode(',', $value);
    }

    /**
     * Accessor for full_addresses attribute. (->full_addresses)
     */
    public function getFullAddressesAttribute(): array
    {
        return $this->addresses->pluck('full_address')->all();
    }

    /**
     * Get status for the model.
     *
     * @return array
     */
    public static function getStatusDescription(): array
    {
        return [
            static::STATUS_DRAFT => 'Draft',
            static::STATUS_CLOSED => 'Closed',
            static::STATUS_REVIEW => 'Review',
            static::STATUS_OPEN => 'Open',
        ];
    }

    public function getStatusText(): string
    {
        return static::getStatusDescription()[$this->status];
    }

    /**
     * Get link detail (detail_url attribute).
     *
     * @return string
     */
    public function getDetailUrlAttribute(): string
    {
        return config('app.frontend_url') . '/detail-jobs/' . $this->slug . '-' . $this->getKey();
    }

    public function audits(): MorphMany
    {
        return $this->morphMany(
            Audit::class,
            'auditable'
        );
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'owned_id')
            ->withDefault();
    }

    public function getAuditJobResource()
    {
        return AuditJobResource::make($this->loadMissing(['taxonomies.term', 'addresses.province', 'addresses.district', 'addresses.ward', 'information', 'note', 'company']))->toArray(request());
    }

    public function isOpen(): bool
    {
        return $this->status == self::STATUS_OPEN;
    }

    public function announcements(): MorphMany
    {
        return $this->morphMany(Announcement::class, 'model')->withTypeJobAnnouncement();
    }

    public function getIs48hAttribute()
    {
        $announcement = $this->announcements->first();
        if (blank($announcement)) {
            return false;
        }

        return $announcement->expires_at->gte(now());
    }

    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }

    public function qualifiedCandidates()
    {
        return $this
            ->candidates()
            ->withStatusReadyAndUnqualified();
    }

    public function candidateBestMatch()
    {
        return $this
            ->candidates()
            ->withStatusReadyAndUnqualified()
            ->bestMatch();
    }

    /**
     * Accessor for package_id attribute. (->package_id)
     */
    public function getPackageIdAttribute()
    {
        $package = $this->taxonomies()
            ->withPivot('is_free_package')
            ->where('taxonomy', 'packages')
            ->latest('term_relationships.id')
            ->first();

        return $package && $this->crm_invoice_id ? $package->id . ':' . $this->crm_invoice_id . ':' . ($package->pivot->is_free_package ? 1 : 0) : '';
    }

    public function isJobReview(): bool
    {
        return $this->status === self::STATUS_REVIEW;
    }

    public function getResponsibilitiesAttribute()
    {
        return $this
            ->taxonomies()
            ->where('taxonomy', 'responsibilities')
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->values();
    }


    public function getResponsibilitiesIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'responsibilities')->pluck('id');
    }

    public function getRequirementsAttribute()
    {
        return $this
        ->taxonomies()
        ->where('taxonomy', 'requirements')
        ->get()
        ->map(function ($taxonomy) {
            return [
                'id' => $taxonomy->id,
                'name' => $taxonomy->term->name,
                'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                'icon' => Arr::first($taxonomy->thumbnail_url),
            ];
        })
        ->values();
    }

    public function getRequirementsIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'requirements')->pluck('id');
    }

    public function getRecruimentProcessesAttribute()
    {
        return $this
            ->taxonomies()
            ->where('taxonomy', 'recruitment_processes')
            ->get()
            ->map(function ($taxonomy) {
                return [
                    'id' => $taxonomy->id,
                    'name' => $taxonomy->term->name,
                    'description' => Arr::get($taxonomy->pivot->custom_properties ?? [], 'description'),
                    'icon' => Arr::first($taxonomy->thumbnail_url),
                ];
            })
            ->values();
    }

    public function getRecruimentProcessesIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'recruitment_processes')->pluck('id');
    }

    public function getBenefitsIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'benefits')->pluck('id');
    }

    public function getJobBannerIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_banner')->first()?->getKey();
    }

    public function getJobTemplateIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template')->first()?->getKey();
    }

    public function getJobTemplateColorIdAttribute()
    {
        return $this->taxonomies->where('taxonomy', 'job_template_color')->first()?->getKey();
    }

    public function views()
    {
        return $this->morphToMany(View::class, 'viewable');
    }
    public function getPreviewUrlAttribute(): string
    {
        return config('app.frontend_url') . '/preview-jobs/' . $this->slug . '-' . $this->getKey();
    }

    public function job_categories(): BelongsToMany
    {
        return $this->belongsToMany(JobCategory::class, 'job_category_job');
    }

    /**
     * Accessor for job_category attribute. (->job_category)
     */
    public function getJobCategoryAttribute()
    {
        return $this->job_categories
            ->where('type', JobCategoryType::CATEGORY)
            ->first();
    }

    /**
     * Accessor for job_category_id attribute. (->job_category_id)
     */
    public function getJobCategoryIdAttribute()
    {
        return $this->job_category?->id;
    }

    /**
     * Accessor for job_category_role_ids attribute. (->job_category_role_ids)
     */
    public function getJobCategoryRoleIdsAttribute()
    {
        return $this->job_categories->where('type', JobCategoryType::ROLE)->pluck('id');
    }

    /**
     * Accessor for is_content_image attribute. (->is_content_image)
     */
    public function getIsContentImageAttribute()
    {
        return ! is_null($this->premium_upgraded_at);
    }

    public function setIsContentImageAttribute($value)
    {
        if ($this->is_content_image == $value) {
            return;
        }

        $this->premium_upgraded_at = $value ? now() : null;
        $this->premium_enabled_at = $value ? now() : null;
    }
}
