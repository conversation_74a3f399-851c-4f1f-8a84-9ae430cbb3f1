<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * App\Models\CompanyCreditLog.
 *
 * @property int $id
 * @property int $company_id
 * @property int $search_package_id
 * @property string $type
 * @property int $credit
 * @property int|null $candidate_id
 * @property string|null $candidate_name
 * @property string|null $candidate_email
 * @property int|null $created_by
 * @property string|null $created_by_name
 * @property string|null $created_by_email
 * @property int|null $updated_by
 * @property string|null $updated_by_email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $company_search_package_id
 * @method static Builder|CompanyCreditLog createdBetween($from, $to)
 * @method static Builder|CompanyCreditLog newModelQuery()
 * @method static Builder|CompanyCreditLog newQuery()
 * @method static Builder|CompanyCreditLog query()
 * @method static Builder|CompanyCreditLog typeIn()
 * @method static Builder|CompanyCreditLog whereCandidateEmail($value)
 * @method static Builder|CompanyCreditLog whereCandidateId($value)
 * @method static Builder|CompanyCreditLog whereCandidateName($value)
 * @method static Builder|CompanyCreditLog whereCompanyId($value)
 * @method static Builder|CompanyCreditLog whereCompanySearchPackageId($value)
 * @method static Builder|CompanyCreditLog whereCreatedAt($value)
 * @method static Builder|CompanyCreditLog whereCreatedBy($value)
 * @method static Builder|CompanyCreditLog whereCreatedByEmail($value)
 * @method static Builder|CompanyCreditLog whereCreatedByName($value)
 * @method static Builder|CompanyCreditLog whereCredit($value)
 * @method static Builder|CompanyCreditLog whereId($value)
 * @method static Builder|CompanyCreditLog whereSearchPackageId($value)
 * @method static Builder|CompanyCreditLog whereType($value)
 * @method static Builder|CompanyCreditLog whereUpdatedAt($value)
 * @method static Builder|CompanyCreditLog whereUpdatedBy($value)
 * @method static Builder|CompanyCreditLog whereUpdatedByEmail($value)
 * @mixin \Eloquent
 */
class CompanyCreditLog extends Pivot
{
    use HasFactory;

    const TYPE_IN = 'In';

    const TYPE_OUT = 'Out';

    const TYPE_REFUND = 'Refund';

    protected $table = 'company_credit_logs';

    protected $fillable = [
        'company_id',
        'search_package_id',
        'type',
        'credit',
        'candidate_id',
        'candidate_name',
        'candidate_email',
        'created_by',
        'created_by_name',
        'created_by_email',
        'updated_by',
        'updated_by_email',
        'company_search_package_id',
        'refund_requested_at',
        'refund_approved_at',
        'refund_requested_by',
        'refund_approved_by',
    ];

    public function scopeCreatedBetween(Builder $builder, $from, $to): void
    {
        $builder->whereBetween('created_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()]);
    }

    public function scopeTypeIn($query)
    {
        $query->where('type', self::TYPE_IN);
    }

    public function scopeTypeOut($query)
    {
        $query->where('type', self::TYPE_OUT);
    }

    public function isTypeOut()
    {
        return $this->type === self::TYPE_OUT;
    }
}
