<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\CompanyJobPostingLog
 *
 * @property int $id
 * @property int $company_id
 * @property int $job_id
 * @property string $job_title
 * @property string $package_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereJobId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereJobTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog wherePackageName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyJobPostingLog whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CompanyJobPostingLog extends Model
{
    use HasFactory;
    protected $fillable = [
        'company_id',
        'job_id',
        'job_title',
        'package_name',
    ];

    public function scopeCreatedBetween(Builder $builder, $from, $to): void
    {
        $builder->whereBetween('created_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()]);
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }
}
