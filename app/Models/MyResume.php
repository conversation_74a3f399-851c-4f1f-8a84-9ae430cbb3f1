<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Carbon;

/**
 * App\Models\MyResume.
 *
 * @property string $uuid
 * @property int $id
 * @property int $id_user
 * @property string $name_resume
 * @property int $price
 * @property array|null $content
 * @property string|null $lang
 * @property string $sections
 * @property array $edit
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $template_name
 * @property string|null $deleted_at
 * @property int|null $experience
 * @property int|null $activitylog_completed_id
 * @property int|null $user_id
 * @property int|null $media_id
 * @property int|null $is_share
 * @property string|null $convert_by
 * @property string|null $show_survey_at
 * @property-read UserMainCV|null $mainCv
 * @property-read User|null $user
 * @method static Builder|MyResume newModelQuery()
 * @method static Builder|MyResume newQuery()
 * @method static Builder|MyResume query()
 * @method static Builder|MyResume whereActivitylogCompletedId($value)
 * @method static Builder|MyResume whereContent($value)
 * @method static Builder|MyResume whereConvertBy($value)
 * @method static Builder|MyResume whereCreatedAt($value)
 * @method static Builder|MyResume whereDeletedAt($value)
 * @method static Builder|MyResume whereEdit($value)
 * @method static Builder|MyResume whereExperience($value)
 * @method static Builder|MyResume whereId($value)
 * @method static Builder|MyResume whereIdUser($value)
 * @method static Builder|MyResume whereIsShare($value)
 * @method static Builder|MyResume whereLang($value)
 * @method static Builder|MyResume whereMediaId($value)
 * @method static Builder|MyResume whereNameResume($value)
 * @method static Builder|MyResume wherePrice($value)
 * @method static Builder|MyResume whereSections($value)
 * @method static Builder|MyResume whereShowSurveyAt($value)
 * @method static Builder|MyResume whereTemplateName($value)
 * @method static Builder|MyResume whereUpdatedAt($value)
 * @method static Builder|MyResume whereUserId($value)
 * @method static Builder|MyResume whereUuid($value)
 * @mixin \Eloquent
 */
class MyResume extends Model
{
    use HasFactory;

    protected $table = 'nd_my_resume';

    protected $connection = 'mysql_cvbuilder';

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'content' => '[]',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'edit' => 'array',
        'content' => 'json',
    ];

    public function mainCv(): MorphOne
    {
        return $this->morphOne(UserMainCV::class, 'cv');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
