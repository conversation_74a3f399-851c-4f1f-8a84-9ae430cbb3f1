<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\Address.
 *
 * @property int $id
 * @property string $addressable_type
 * @property int $addressable_id
 * @property string|null $display_name
 * @property string|null $street
 * @property string|null $postal_code
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $ward_id
 * @property string|null $district_id
 * @property string|null $province_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read VietnamArea|null $district
 * @property-read string $full_address
 * @property-read VietnamArea|null $province
 * @property-read VietnamArea|null $ward
 *
 * @method static Builder|Address newModelQuery()
 * @method static Builder|Address newQuery()
 * @method static \Illuminate\Database\Query\Builder|Address onlyTrashed()
 * @method static Builder|Address query()
 * @method static Builder|Address whereAddressableId($value)
 * @method static Builder|Address whereAddressableType($value)
 * @method static Builder|Address whereCreatedAt($value)
 * @method static Builder|Address whereDeletedAt($value)
 * @method static Builder|Address whereDisplayName($value)
 * @method static Builder|Address whereDistrictId($value)
 * @method static Builder|Address whereId($value)
 * @method static Builder|Address whereLatitude($value)
 * @method static Builder|Address whereLongitude($value)
 * @method static Builder|Address wherePostalCode($value)
 * @method static Builder|Address whereProvinceId($value)
 * @method static Builder|Address whereStreet($value)
 * @method static Builder|Address whereUpdatedAt($value)
 * @method static Builder|Address whereWardId($value)
 * @method static \Illuminate\Database\Query\Builder|Address withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Address withoutTrashed()
 * @mixin \Eloquent
 */
class Address extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['ward_id', 'province_id', 'district_id', 'street', 'order'];

    /**
     * Lấy tỉnh/thành phố.
     *
     * @return BelongsTo
     *
     * <AUTHOR> Tran
     */
    public function province(): BelongsTo
    {
        return $this->belongsTo(VietnamArea::class, 'province_id', 'code')
            ->withDefault([
                'name' => null,
                'path_with_type' => null,
            ]);
    }

    /**
     * Lấy quận/huyện.
     *
     * @return BelongsTo
     *
     * <AUTHOR> Tran
     */
    public function district(): BelongsTo
    {
        return $this->belongsTo(VietnamArea::class, 'district_id', 'code')
            ->withDefault([
                'name' => null,
                'path_with_type' => null,
            ]);
    }

    /**
     * Lấy phường/xã.
     *
     * @return BelongsTo
     *
     * <AUTHOR> Tran
     */
    public function ward(): BelongsTo
    {
        return $this->belongsTo(VietnamArea::class, 'ward_id', 'code')
            ->withDefault([
                'name' => null,
                'path_with_type' => null,
            ]);
    }

    /**
     * Lấy địa chỉ đầy đủ.
     *
     * @return string
     *
     * <AUTHOR> Tran
     */
    public function getFullAddressAttribute(): string
    {
        if ($this->province->code === '9999') {
            return 'Remote';
        }

        return trim(trim(
            $this->street . ', ' .
            ($this->ward->path_with_type ?:
                $this->district->path_with_type ?:
                    $this->province->name_with_type)
        ), ',');
    }
}
