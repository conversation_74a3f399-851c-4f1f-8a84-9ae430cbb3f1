<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Arr;

/**
 * App\Models\UserProfile.
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $avatar_url
 * @property int|null $years_of_exp
 * @property string $status
 * @property string|null $address
 * @property string|null $province_code
 * @property string|null $linkedin_link
 * @property string|null $github_link
 * @property string|null $summary
 * @property array $skills
 * @property array $experiences
 * @property array $educations
 * @property array $projects
 * @property array $languages
 * @property array $interests
 * @property array $references
 * @property array $activities
 * @property array $certificates
 * @property array $additionals
 * @property array $completed_sections
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read UserMainCV|null $mainCv
 * @property-read User|null $user
 * @method static Builder|UserProfile newModelQuery()
 * @method static Builder|UserProfile newQuery()
 * @method static Builder|UserProfile query()
 * @method static Builder|UserProfile whereActivities($value)
 * @method static Builder|UserProfile whereAdditionals($value)
 * @method static Builder|UserProfile whereAddress($value)
 * @method static Builder|UserProfile whereAvatarUrl($value)
 * @method static Builder|UserProfile whereCertificates($value)
 * @method static Builder|UserProfile whereCompletedSections($value)
 * @method static Builder|UserProfile whereCreatedAt($value)
 * @method static Builder|UserProfile whereEducations($value)
 * @method static Builder|UserProfile whereExperiences($value)
 * @method static Builder|UserProfile whereGithubLink($value)
 * @method static Builder|UserProfile whereId($value)
 * @method static Builder|UserProfile whereInterests($value)
 * @method static Builder|UserProfile whereLanguages($value)
 * @method static Builder|UserProfile whereLinkedinLink($value)
 * @method static Builder|UserProfile whereProjects($value)
 * @method static Builder|UserProfile whereProvinceCode($value)
 * @method static Builder|UserProfile whereReferences($value)
 * @method static Builder|UserProfile whereSkills($value)
 * @method static Builder|UserProfile whereStatus($value)
 * @method static Builder|UserProfile whereSummary($value)
 * @method static Builder|UserProfile whereUpdatedAt($value)
 * @method static Builder|UserProfile whereUserId($value)
 * @method static Builder|UserProfile whereYearsOfExp($value)
 * @mixin \Eloquent
 */
class UserProfile extends Model
{
    use HasFactory;

    protected $table = 'user_profiles';

    protected $casts = [
        'skills' => 'json',
        'educations' => 'json',
        'experiences' => 'json',
        'projects' => 'json',
        'languages' => 'json',
        'interests' => 'json',
        'references' => 'json',
        'activities' => 'json',
        'certificates' => 'json',
        'additionals' => 'json',
        'completed_sections' => 'json',
    ];

    public function mainCv(): MorphOne
    {
        return $this->morphOne(UserMainCV::class, 'cv');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Extract all from/to from experiences then get min from, max to from the list.
     * Get different days between min/max then convert it to years
     *
     * @param  array  $experiences list experiences
     * @param  int|null  $nullOrZero 0 - for cv builder, otherwise, null
     *
     * @return int|null YOE
     */
    public static function getYearsOfExp(array $experiences, int $nullOrZero = null): ?int
    {
        if (empty($experiences)) {
            return $nullOrZero;
        }
        $from = array_filter(Arr::pluck($experiences, 'from'));
        $to = array_filter(Arr::pluck($experiences, 'to'));
        $isWorkingHere = count(array_filter($experiences, fn($experience) => $experience['is_working_here'] == true)) > 0;

        // Does not have from/to take from exp
        if (empty($from) && (empty($to) && !$isWorkingHere)) {
            return $nullOrZero;
        }

        // Get min/max value from the list
        $minFrom = empty($from) ? date('Y-m-d') : min($from);
        $maxTo = (empty($to) || $isWorkingHere) ? date('Y-m-d') : max($to);

        $minFrom = Carbon::parse($minFrom);
        $maxTo = Carbon::parse($maxTo);
        // Get the different day between from and to then divide by 365 to get years
        $diffInDays = $maxTo->diffInDays($minFrom) / 365;
        // Round up or down based on fraction of the number: >0.5 => 1, else 0
        return intval(floor($diffInDays) + (($diffInDays - floor($diffInDays)) >= 0.5 ? 1 : 0));
    }
}
