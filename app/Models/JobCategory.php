<?php

namespace App\Models;

use App\Enums\JobCategoryType;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kalnoy\Nestedset\NodeTrait;

class JobCategory extends Model implements TranslatableContract
{
    use Translatable;
    use NodeTrait;
    use SoftDeletes;

    public const IT_CATEGORY_ID = 1;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'sort_order',
        'description',
        'activated_at',
        'parent_id',
        'type',
    ];

    protected $casts = [
        'type' => JobCategoryType::class,
        'activated_at' => 'datetime',
    ];

    protected $orderColumn = 'sort_order';

    /**
     * The attributes that are translatable.
     */
    public $translatedAttributes = [
        'name',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    public function isCategoryType(): bool
    {
        return $this->type->isCategory($this->type->value);
    }

    public function isRoleType(): bool
    {
        return $this->type->isRole($this->type->value);
    }

    /**
     * Scope a query to only include categories.
     * E.g.: $query->isCategory();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeIsCategory($query)
    {
        $query->where('type', JobCategoryType::CATEGORY->value);
    }

    /**
     * Scope a query to only include roles.
     * E.g.: $query->isRole();
     *
     * @param $query
     *
     * @return void
     */
    public function scopeIsRole($query)
    {
        $query->where('type', JobCategoryType::ROLE->value);
    }
}
