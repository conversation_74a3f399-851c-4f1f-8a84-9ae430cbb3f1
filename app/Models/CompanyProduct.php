<?php

namespace App\Models;

use Eloquent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * App\Models\CompanyProduct.
 *
 * @property int $id
 * @property int $company_id
 * @property string $name
 * @property string|null $description
 * @property string|null $link
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Media|null $image
 * @property-read Collection|Media[] $media
 * @property-read int|null $media_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct newQuery()
 * @method static Builder|CompanyProduct onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyProduct whereUpdatedAt($value)
 * @method static Builder|CompanyProduct withTrashed()
 * @method static Builder|CompanyProduct withoutTrashed()
 * @mixin Eloquent
 */
class CompanyProduct extends Model implements HasMedia
{
    use HasFactory;
    use SoftDeletes;
    use InteractsWithMedia;

    protected $fillable = ['name', 'description', 'link'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
    }

    /** @noinspection PhpUnused */
    public function getImageAttribute(): ?\Spatie\MediaLibrary\MediaCollections\Models\Media
    {
        return $this->getFirstMedia('images');
    }
}
