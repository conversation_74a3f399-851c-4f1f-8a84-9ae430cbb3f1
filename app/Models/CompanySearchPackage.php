<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CompanySearchPackage.
 *
 * @property int $id
 * @property int $company_id
 * @property int $search_package_id
 * @property \Illuminate\Support\Carbon|null $valid_at
 * @property \Illuminate\Support\Carbon $expired_at
 * @property int|null $updated_by
 * @property string|null $updated_by_email
 * @property int $total_credit
 * @property int|null $used_credit
 * @property int|null $remain_credit
 * @property int|null $crm_itemable_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $status
 * @property-read SearchPackage|null $searchPackage
 * @method static Builder|CompanySearchPackage isValid()
 * @method static Builder|CompanySearchPackage newModelQuery()
 * @method static Builder|CompanySearchPackage newQuery()
 * @method static Builder|CompanySearchPackage query()
 * @method static Builder|CompanySearchPackage validBetween($from, $to)
 * @method static Builder|CompanySearchPackage whereCompanyId($value)
 * @method static Builder|CompanySearchPackage whereCreatedAt($value)
 * @method static Builder|CompanySearchPackage whereExpiredAt($value)
 * @method static Builder|CompanySearchPackage whereId($value)
 * @method static Builder|CompanySearchPackage whereRemainCredit($value)
 * @method static Builder|CompanySearchPackage whereSearchPackageId($value)
 * @method static Builder|CompanySearchPackage whereTotalCredit($value)
 * @method static Builder|CompanySearchPackage whereUpdatedAt($value)
 * @method static Builder|CompanySearchPackage whereUpdatedBy($value)
 * @method static Builder|CompanySearchPackage whereUpdatedByEmail($value)
 * @method static Builder|CompanySearchPackage whereUsedCredit($value)
 * @method static Builder|CompanySearchPackage whereValidAt($value)
 * @mixin \Eloquent
 */
class CompanySearchPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'search_package_id',
        'valid_at',
        'expired_at',
        'updated_by',
        'updated_by_email',
        'total_credit',
        'used_credit',
        'remain_credit',
        'crm_itemable_id',
    ];

    protected $appends = [
        'status',
    ];

    protected $casts = [
        'valid_at' => 'datetime',
        'expired_at' => 'datetime',
    ];

    public function getStatusAttribute(): string
    {
        if ($this->isInvalid()) {
            return 'Inactive';
        } elseif ($this->isValid()) {
            return 'Active';
        } else {
            return 'Expired';
        }
    }

    public function isOutOfCredit(): bool
    {
        return $this->remain_credit == 0;
    }

    public function isValid()
    {
        return !$this->isInvalid() && !$this->isExpired() && !$this->isOutOfCredit();
    }

    public function isInvalid(): bool
    {
        return is_null($this->valid_at) || now()->lt($this->valid_at);
    }

    public function isExpired(): bool
    {
        return now()->gte($this->expired_at);
    }

    public function scopeIsInvalid(Builder $builder): void
    {
        $builder->where(function ($q) {
            return $q->whereNull('valid_at')
                ->orWhere('valid_at', '>' , Carbon::now()->format('Y-m-d 00:00:00'));
        });
    }

    public function scopeIsValid(Builder $builder): void
    {
        $builder->whereNotNull('valid_at')
            ->where('valid_at', '<=', now())
            ->where('expired_at', '>', now())
            ->where('remain_credit', '>', 0);
    }

    public function searchPackage(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(SearchPackage::class);
    }

    public function scopeValidBetween(Builder $builder, $from, $to): void
    {
        $builder->whereBetween('valid_at', [Carbon::parse($from)->startOfDay(), Carbon::parse($to)->endOfDay()]);
    }

    /**
     * Cleanup items that were added by CRM system in case 1 ID CRM - n ID AMS
     * when CS active this package
     * 
     * @return void
     */
    public function cleanRelatedItems()
    {
        if ($this->crm_itemable_id) {
            $relatedSearchPackageIds = static::where('crm_itemable_id', $this->crm_itemable_id)
                ->whereSearchPackageId($this->search_package_id)
                ->where('id', '!=', $this->id)
                ->pluck('id')
                ->toArray();

            CompanyCreditLog::whereIn('company_search_package_id', $relatedSearchPackageIds)->delete();
            static::whereIn('id', $relatedSearchPackageIds)->delete();
        }
    }
}
