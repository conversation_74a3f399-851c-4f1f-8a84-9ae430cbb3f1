<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Audit extends Model
{
    /**
     * @inheritdoc
     */
    protected $fillable = [
        'user_type', 'user_id', 'event', 'old_values', 'new_values', 'url', 'ip_address', 'user_agent',
    ];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'old_values' => 'json',
        'new_values' => 'json',
        // Note: Please do not add 'auditable_id' in here, as it will break non-integer PK models
    ];

    /**
     * @inheritdoc
     */
    public function auditable()
    {
        return $this->morphTo();
    }
}
