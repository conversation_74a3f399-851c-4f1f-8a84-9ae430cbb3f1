<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Taxonomy.
 *
 * @property int $id
 * @property int $term_id
 * @property string $taxonomy using type skill|tag|category|job_service|job_benefit|job_position
 * @property int $parent
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy query()
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy whereParent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy whereTaxonomy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Taxonomy whereTermId($value)
 * @mixin \Eloquent
 */
class Taxonomy extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'term_id',
        'taxonomy',
        'parent',
        'sort_order',
    ];

    protected $table = 'term_taxonomy';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    const OPEN_TO_WORK = 7390;

    const COMPANY_TAXONOMY_CATEGORIES = ['nationalities', 'num_employees', 'skills', 'industries'];

    const EXPERIENCE_TAXONOMIES = [
        1640 => 0, // Not yet
        8667 => 0,    // 3 month
        1641 => 1,    // 6 months
        1642 => 1,    // 1 year
        1643 => 2,    // 2 years
        1644 => 3,    // 3 years
        1645 => 4,    // 4 years
        1646 => 5,    // 5 years
        1647 => 6,    // 6 years
        1648 => 7,    // 7 years
        1649 => 8,    // 8 years
        1650 => 9,    // 9 years
        8288 => 10,   //  10 years
        1651 => 10,   // Above 10 years
    ];

    const JOB_BASIC_JOB_PACKAGE = 3908;
    const JOB_BASIC_PLUS_JOB_PACKAGE = 3910;
    const JOB_DISTINCTION_JOB_PACKAGE = 3912;
    const JOB_TOP_JOB_PACKAGE = 9217;

    public function term(): BelongsTo
    {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    public function scopeWillingToWork($query)
    {
        $query->where('taxonomy_id', self::OPEN_TO_WORK);
    }
}
