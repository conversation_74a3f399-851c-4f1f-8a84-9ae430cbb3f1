<?php

namespace App\Generators;

use Illuminate\Pagination\LengthAwarePaginator as BaseLengthAwarePaginator;
use Illuminate\Support\Collection;

class LengthAwarePaginator extends BaseLengthAwarePaginator
{
    /**
     * Get the paginator links as a collection (for JSON responses).
     *
     * @return Collection
     */
    public function linkCollection(): Collection
    {
        return collect($this->elements())->flatMap(function ($item) {
            if (!is_array($item)) {
                return [['to' => null, 'url' => null, 'label' => '...', 'active' => false]];
            }

            return collect($item)->map(function ($url, $page) {
                return [
                    'to' => (string) $page,
                    'url' => $url,
                    'label' => (string) $page,
                    'active' => $this->currentPage() === $page,
                ];
            });
        });
    }
}
