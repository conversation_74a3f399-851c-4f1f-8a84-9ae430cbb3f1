<?php

namespace App\Generators;

use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaPathGenerator implements \Spatie\MediaLibrary\Support\PathGenerator\PathGenerator
{
    public function getPath(Media $media): string
    {
        /* @phpstan-ignore-next-line */
        if ($media->mime_type == 'image/jpeg' || $media->mime_type == 'image/png') {
            return $this->pathMedia($media, 'images');
        }

        return $this->pathMedia($media, 'files');
    }

    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getPath($media) . 'responsive-images/';
    }

    public function pathMedia(Media $media, $type): string
    {
        /* @phpstan-ignore-next-line */
        return $media->getPathFolder($type) . '/';
    }

    public function getPathForConversions(Media $media): string
    {
        // TODO: Implement getPathForConversions() method.
        return '';
    }
}
