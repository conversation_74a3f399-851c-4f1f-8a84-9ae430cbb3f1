<?php

namespace App\Policies;

use App\Models\Candidate;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CandidatePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User  $user
     * @param Candidate  $candidate
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Candidate $candidate)
    {
        return $user->company_id == $candidate->job->owned_id;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User  $user
     * @param Candidate  $candidate
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Candidate $candidate)
    {
        return $user->isEmployerAlreadyApproved() && $user->company_id == $candidate->job->owned_id;
    }

    public function downloadCv(User $user, Candidate $candidate): bool
    {
        return $user->isEmployerAlreadyApproved() && $user->company_id == $candidate->job->owned_id;
    }

    public function previewCv(User $user, Candidate $candidate): bool
    {
        return $user->isEmployerAlreadyApproved() && $user->company_id == $candidate->job->owned_id;
    }
}
