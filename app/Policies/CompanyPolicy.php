<?php

namespace App\Policies;

use App\Models\Company;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  User  $user
     * @param  Company  $company
     * @return bool
     */
    public function view(User $user, Company $company): bool
    {
        return $user->company_id === $company->id;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  User  $user
     * @param  Company  $company
     * @return bool
     */
    public function update(User $user, Company $company): bool
    {
        return $user->company_id === $company->id;
    }

    /**
     * @param  User  $user
     * @param  Company  $company
     * @param  User  $emloyer
     * @return bool
     */
    public function updateEmployer(User $user, Company $company, $emloyer): bool
    {
        return $user->id === $emloyer->id;
    }
}
