<?php

namespace App\Policies;

use App\Models\Job;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class JobPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User  $user
     * @param Job  $job
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Job $job)
    {
        return $user->company_id == $job->owned_id;
    }

    /**
     * Determine whether the user can create models.
     * As this version not check if user has a
     * permission to create new job or not.
     *
     * @param User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->isEmployerAlreadyApproved();
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User  $user
     * @param Job  $job
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Job $job)
    {
        return $user->company_id == $job->owned_id && $user->isEmployerAlreadyApproved();
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User  $user
     * @param Job  $job
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Job $job)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param User  $user
     * @param Job  $job
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Job $job)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param User  $user
     * @param Job  $job
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Job $job)
    {
        return false;
    }
}
