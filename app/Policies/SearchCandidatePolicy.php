<?php

namespace App\Policies;

use App\Models\SearchCandidate;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SearchCandidatePolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine whether the user can unlock the candidate.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function unlockCandidate(User $user)
    {
        return $user->isEmployerAlreadyApproved();
    }

    /**
     * Determine whether the user can unlock the candidate.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function requestRefund(User $user)
    {
        return $user->isEmployerAlreadyApproved();
    }

    /**
     * Determine whether the user can download the search candidate's CV.
     *
     * @param \App\Models\User $user
     * @param \App\Models\SearchCandidate $searchCandidate
     * @return bool
     */
    public function downloadCv(User $user, SearchCandidate $searchCandidate): bool
    {
        return $user->isEmployerAlreadyApproved() && $searchCandidate->isUnlocked;
    }
}
