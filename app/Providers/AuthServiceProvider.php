<?php

namespace App\Providers;

use App\Models\Candidate;
use App\Models\Company;
use App\Models\CompanySaveSearchCandidate;
use App\Models\Job;
use App\Models\SearchCandidate;
use App\Models\SearchCandidateNote;
use App\Models\User;
use App\Policies\CandidatePolicy;
use App\Policies\CompanyPolicy;
use App\Policies\CompanySaveSearchCandidatePolicy;
use App\Policies\JobPolicy;
use App\Policies\SearchCandidateNotePolicy;
use App\Policies\SearchCandidatePolicy;
use App\Policies\UserPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Job::class => JobPolicy::class,
        Company::class => CompanyPolicy::class,
        Candidate::class => CandidatePolicy::class,
        User::class => UserPolicy::class,
        SearchCandidate::class => SearchCandidatePolicy::class,
        CompanySaveSearchCandidate::class => CompanySaveSearchCandidatePolicy::class,
        SearchCandidateNote::class => SearchCandidateNotePolicy::class,
    ];
}
