<?php

namespace App\Providers;

use App\IteratorAggregate\TopDevEloquentHitsIteratorAggregate;
use App\Models\SearchCandidate;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Change custom paginator
        $this->app->bind(
            LengthAwarePaginator::class,
            \App\Generators\LengthAwarePaginator::class
        );

        Str::macro('maskEmail', function ($email) {
            $numKeptCharacters = 2;

            return Str::mask($email, '*', -(strlen($email) - $numKeptCharacters), strpos($email, '@') - $numKeptCharacters);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (app()->environment(['production', 'staging'])) {
            URL::forceScheme('https');
        }

        Relation::enforceMorphMap([
            'Modules\Company\Entities\Company' => 'App\Models\Company',
            'Modules\Company\Entities\Product' => 'App\Models\CompanyProduct',
            'Modules\Job\Entities\Job' => 'App\Models\Job',
            'Modules\User\Entities\User' => 'App\Models\User',
            'Modules\Job\Entities\Candidate' => 'App\Models\Candidate',
            'Modules\User\Entities\UserProfile' => 'App\Models\UserProfile',
            'Modules\File\Entities\Media' => 'App\Models\Media',
            'Modules\User\Entities\MyResume' => 'App\Models\MyResume',
        ]);

        /*
         * Custom mapping elasticsearch response into Model
         */
        $this->app->bind(
            'Matchish\ScoutElasticSearch\ElasticSearch\HitsIteratorAggregate',
            TopDevEloquentHitsIteratorAggregate::class,
        );

        /*
         * Disable eloquent sync model
         */
        SearchCandidate::disableSearchSyncing();
    }
}
