<?php

namespace App\Helpers;



use App\Models\Company;

class Helper
{
    /**
     * @param Company $notifiable
     * @param string $email
     * @return array|\Illuminate\Config\Repository|\Illuminate\Contracts\Foundation\Application
     */
    public static function toCcEmail(Company $notifiable, string $email)
    {
        if (config('app.env') !== 'production') {
            return [];
        }

        $emailCs = config('constant.CS_DEFAULT_MAIL');
        $emailSale = config('constant.SALE_DEFAULT_MAIL');

        // Fetch the email addresses from the CRM API
        $ccEmails = CrmApi::getEmailCrmCustomerAdminCompanies($notifiable->id);

        // Remove the $email from $ccEmails if it exists
        $diffEmail = array_values(array_diff($ccEmails, [$email]));
        return $ccEmails ? array_merge($diffEmail, $emailCs) : array_merge($emailCs, $emailSale);

    }
}
