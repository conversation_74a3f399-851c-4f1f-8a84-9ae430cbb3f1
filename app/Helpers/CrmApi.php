<?php

namespace App\Helpers;

use GuzzleHttp\Exception\BadResponseException;
use Illuminate\Http\Client\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Throwable;

class CrmApi
{
    /**
     * Make request to CRM system via api
     *
     * @param string $uri uri for crm api
     * @param array $options options that follows GuzzleHttp\Client
     * @param string $method http method for request, default is GET
     *
     * @return Response The HTTP response.
     */
    public static function request($uri, $options = [], $method = 'GET')
    {
        // Initialize HTTP Client with headers and base URL
        $client = Http::withHeaders([
            'api-auth-code' => config('services.crm.auth_code')
        ])
            ->baseUrl(config('services.crm.base_url'))
            ->timeout(config('services.crm.timeout'))
            ->withOptions([
                'verify' => config('app.env') === 'production'
            ]);

        // Prepend API version to URI
        $uri = '/api/v3/' . ltrim($uri, "/");

        // Send HTTP request and return response
        return $client->asForm()->{$method}($uri, $options);
    }

    /**
     * API get email customer CRM Companies
     *
     * @param $companyId
     * @return array
     */
    public static function getEmailCrmCustomerAdminCompanies($companyId): array
    {
        $options = [
            'company_id' => $companyId
        ];

        $response = self::request('clients/get_email_customer_admin', $options, 'POST');
        $responseBody = $response->json();

        return $responseBody ? array_filter($responseBody['data']) : [];
    }


    /**
     * @param  array  $data
     *
     * @return array|JsonResponse|mixed
     */
    public static function createCrmTicket(array $data): mixed
    {
        try {
            $response = self::request('/tickets/store', $data, 'POST');
            \Log::info('Create CRM ticket response: ' . $response->body());
            return $response->json();
        } catch (BadResponseException $ex) {
            logger((string) $ex->getResponse()->getBody()->getContents());
            return response()->json([
                'message' => $ex->getMessage(),
                'success' => false,
            ]);
        } catch (Throwable $ex) {
            logger($ex->getMessage());
            return response()->json([
                'message' => $ex->getMessage(),
                'success' => false,
            ]);
        }
    }

    public static function getTicket($id)
    {
        try {
            $response = self::request('/tickets/get/' . $id);
            \Log::info('Create CRM ticket response: ' . $response->body());
            return $response->json();
        } catch (BadResponseException $ex) {
            logger((string) $ex->getResponse()->getBody()->getContents());
            return response()->json([
                'message' => $ex->getMessage(),
                'success' => false,
            ]);
        } catch (Throwable $ex) {
            logger($ex->getMessage());
            return response()->json([
                'message' => $ex->getMessage(),
                'success' => false,
            ]);
        }
    }
}
