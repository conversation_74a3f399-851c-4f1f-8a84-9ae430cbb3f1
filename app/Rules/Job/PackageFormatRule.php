<?php

namespace App\Rules\Job;

use App\Models\Job;
use Illuminate\Contracts\Validation\Rule;

class PackageFormatRule implements Rule
{
    private int $jobId;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($jobId = 0)
    {
        $this->jobId = $jobId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $package
     * @return bool
     */
    public function passes($attribute, $package)
    {
        // If it does not have package_id and status is review then return an error
        if (empty($package) && $this->jobId && Job::whereId($this->jobId)->whereStatus(Job::STATUS_REVIEW)->exists()) {
            return false;
        }

        // Orderwise, return true
        if (empty($package) || $package[0] === 'free') {
            return true;
        }

        return count($package) === 3;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'job_detail_package_select_placeholder';
    }
}
