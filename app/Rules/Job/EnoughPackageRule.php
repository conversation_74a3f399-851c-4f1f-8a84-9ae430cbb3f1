<?php

namespace App\Rules\Job;

use App\Services\CrmService;
use Illuminate\Contracts\Validation\Rule;

class EnoughPackageRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $package)
    {
        if (empty($value)) {
            return true;
        }

        [$packageId, $invoiceId, $isFreePackage] = $package;

        // Make sure they are not cheat by call api directly
        $availablePackages = app(CrmService::class)->getAvailablePackages(request()->user()->company->id, request()->id ? $value : null);

        return collect($availablePackages)
            ->filter(fn($package) 
                => $package['invoice_id'] == $invoiceId &&
                    $package['ams_package_id'] == $packageId &&
                    $package['free_package'] == !!$isFreePackage
            )
            ->count() > 0;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'usage_history_job_posting_package_is_not_available';
    }
}
