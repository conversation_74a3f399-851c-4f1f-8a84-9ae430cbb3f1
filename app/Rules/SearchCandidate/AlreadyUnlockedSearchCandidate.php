<?php

namespace App\Rules\SearchCandidate;

use App\Models\SearchCandidate;
use Illuminate\Contracts\Validation\Rule;

class AlreadyUnlockedSearchCandidate implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $searchCandidate = SearchCandidate::find($value);

        return $searchCandidate->is_unlocked
            && !$searchCandidate->unlockedByCurrentCompany()
                ->where('expired_at', '<', now())
                ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Not unlocked yet.';
    }
}
