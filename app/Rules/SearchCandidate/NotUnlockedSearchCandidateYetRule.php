<?php

namespace App\Rules\SearchCandidate;

use App\Models\SearchCandidate;
use Illuminate\Contracts\Validation\Rule;

class NotUnlockedSearchCandidateYetRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $searchCandidate = SearchCandidate::find($value);
        if ($searchCandidate->is_unlocked
        && !$searchCandidate->unlockedByCurrentCompany()
            ->where('expired_at', '<', now())
            ->exists()) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Unlocked';
    }
}
