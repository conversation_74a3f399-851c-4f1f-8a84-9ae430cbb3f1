<?php

namespace App\Rules\SearchCandidate;

use App\Models\SearchCandidate;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Auth;

class CheckUserBlockCompanyRule implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $userIdBlock = Auth::user()->company->getUserIdBlockCompany();
        $searchCandidate = SearchCandidate::where('id', $value)->whereIn('user_id', $userIdBlock);

        $searchCandidateIsUnlock = SearchCandidate::where('id', $value)->first();
        if ($searchCandidateIsUnlock instanceof SearchCandidate && $searchCandidateIsUnlock->is_unlocked) {
            return true;
        }

        return !$searchCandidate->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'Not found!';
    }
}
