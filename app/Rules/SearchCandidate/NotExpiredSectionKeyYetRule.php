<?php

namespace App\Rules\SearchCandidate;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Cache;

class NotExpiredSectionKeyYetRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return Cache::has($value);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Section has been expired.';
    }
}
