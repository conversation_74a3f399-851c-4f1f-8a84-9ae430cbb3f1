<?php

namespace App\States\Candidate\ProcedureStatus;

use <PERSON><PERSON>\ModelStates\State;
use <PERSON><PERSON>\ModelStates\StateConfig;

abstract class ProcedureStatus extends State
{
    public static function config(): StateConfig
    {
        return parent::config()
            // Change option #1 to option #2
            ->allowTransition(Matched::class, InterviewAppointment::class)
            // Change option #2 to option #3
            ->allowTransition(InterviewAppointment::class, Offer::class)
            // Change option #3 to option #4
            ->allowTransition(Offer::class, Hired::class)
            // Change option #3 to option #5
            ->allowTransition(Offer::class, Failed::class);
    }
}
