variables:
  SECRET_DETECTION_DISABLED: 1
  CODE_QUALITY_DISABLED: 1
  TEST_DISABLED: 1
  BROWSER_PERFORMANCE_DISABLED: 1

include:
  - template: Auto-DevOps.gitlab-ci.yml  # https://gitlab.com/gitlab-org/gitlab/blob/master/lib/gitlab/ci/templates/Auto-DevOps.gitlab-ci.yml

.auto-deploy:
  variables:
    #DB_MIGRATE: /cnb/lifecycle/launcher php artisan migrate
    HELM_UPGRADE_EXTRA_ARGS: --values .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
  before_script:
    - |
      export K8S_SECRET_DOTENV_FILE=$(cat $DOTENV_FILE)
      set -ex;
      [[ -n "${VOLUME_NFS_PATH}" ]] && SED_SCRIPT=";s#path: /srv/data/resources#path: ${VOLUME_NFS_PATH}#"
      [[ -n "${VOLUME_NFS_SERVER}" ]] && SED_SCRIPT="${SED_SCRIPT:-};s#server: ************#server: ${VOLUME_NFS_SERVER:-************}#"
      sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-secret/g ${SED_SCRIPT:-}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml

stop_review:
  variables:
    HELM_UPGRADE_EXTRA_ARGS: ""
    GIT_STRATEGY: none
  before_script: []

build:
  # Use the Docker executor with Docker socket binding
  tags: ["102-stg-docker"]
  services: []
  variables:
    AUTO_DEVOPS_BUILD_IMAGE_CNB_BUILDER: heroku/builder:22
    #BUILDPACK_URL: heroku/nodejs,heroku/php,heroku/procfile
  before_script:
    - echo "Environment variables are also needed when build"
    - cp $DOTENV_FILE ./.env


.base_deploy_production:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  only:
    - main
  script:
    - echo "HELLO"
    #- cd /srv/employer-dash-v2
    #- git pull origin main
    #- php artisan optimize
    #- yarn && yarn prod
  when: always
  environment:
    name: production
    url: https://dash.topdev.vn

deploy production web1:
  extends: .base_deploy_production
  tags:
    - 115-web1

deploy production web2:
  extends: .base_deploy_production
  tags:
    - 117-web2

build tesing:
  stage: build
  tags:
    - 102-stg
  only:
    - develop
  variables:
    GIT_STRATEGY: none
  before_script:
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 14
  script:
    - cd /var/www/employer-dash-v2
    - git pull origin develop
    - /usr/bin/php8.2 /usr/bin/composer install
    - /usr/bin/php8.2 artisan queue:restart
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php8.2-fpm.sock
    - yarn && yarn dev
  environment:
    name: testing
    url: https://dash-v2.topdev.asia

