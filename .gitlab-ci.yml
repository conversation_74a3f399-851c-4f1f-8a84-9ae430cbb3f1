stages:
  - deploy
  - merge

variables:
  GIT_STRATEGY: none

# Deploy testing on stg server
deploy tesing:
  stage: deploy
  tags:
    - 102-stg
  only:
    - develop
  before_script:
    - source "$HOME/.nvm/nvm.sh"
    - nvm use 14
  script:
    - cd /var/www/employer-dash-v2
    - git pull origin develop
    - /usr/bin/php8.2 /usr/bin/composer install
    - /usr/bin/php8.2 artisan queue:restart
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php8.2-fpm.sock
    - yarn && yarn dev
  environment:
    name: testing
    url: https://amsdev.topdev.asia

# Deploy production on web 1 and web 2 servers
.base_deploy_production:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  only:
    - main
  script:
    - echo "HELLO"
    #- cd /srv/employer-dash-v2
    #- git pull origin main
    #- php artisan optimize
    #- yarn && yarn prod
  when: always
  environment:
    name: production
    url: https://dash.topdev.vn

deploy production web1:
  extends: .base_deploy_production
  tags:
    - 115-web1

deploy production web2:
  extends: .base_deploy_production
  tags:
    - 117-web2

# Merge main into develop
merge main to develop:
  stage: merge
  when: on_success
  tags:
    - 102-stg
  only:
    - main
  script:
    - rm -rf /var/www/employer-dash-v2-sync
    - git clone ssh://*******************:8229/topdev/employer-dash-v2.git /var/www/employer-dash-v2-sync
    - cd /var/www/employer-dash-v2-sync
    - git checkout develop
    - |
      git merge --no-ff main -m "chore: auto-merge main into develop after deployment" || { echo "Merge conflict detected!"; exit 1; }
    - git push origin develop
