{"name": "topdev/employer-dash-v2", "type": "project", "description": "Employer dashboard ver 2", "keywords": ["topdev", "employer-dashboard", "laravel"], "license": "proprietary", "require": {"php": "~8.2.0", "ext-json": "*", "ext-pdo": "*", "astrotomic/laravel-translatable": "^11.9", "cocur/slugify": "4.3", "cviebrock/eloquent-sluggable": "^10.0", "enqueue/amqp-lib": "^0.10.9", "grkamil/laravel-telegram-logging": "^1.10", "guzzlehttp/guzzle": "^7.8", "itsgoingd/clockwork": "^5.1", "kalnoy/nestedset": "^6.0", "laravel-notification-channels/google-chat": "^3.1", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/scout": "^9.8", "laravel/socialite": "^5.6", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "matchish/laravel-scout-elasticsearch": "^7.0", "predis/predis": "^2.2", "rap2hpoutre/laravel-log-viewer": "^2.2", "sentry/sentry-laravel": "^3.2", "socialiteproviders/laravelpassport": "^4.3", "socialiteproviders/manager": "^4.3", "spatie/laravel-medialibrary": "^10.0", "spatie/laravel-model-states": "^2.4", "symfony/cache": "^7.1", "unleash/client": "^2.6", "vladimir-yuldashev/laravel-queue-rabbitmq": "^13.0"}, "require-dev": {"roave/security-advisories": "dev-latest", "barryvdh/laravel-ide-helper": "^2.12", "spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.9", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "nunomaduro/larastan": "^2.4", "pestphp/pest": "^2.13", "pestphp/pest-plugin-laravel": "^2.2", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^10.0", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Database\\Factories\\": "database/factories/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": [], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "analyse": ["./vendor/bin/phpstan --memory-limit=512M analyse"], "test": ["@php artisan test"], "sniff": ["./tools/vendor/bin/php-cs-fixer fix -vvv --dry-run --show-progress=dots"], "fix": ["./tools/vendor/bin/php-cs-fixer fix -vvv --show-progress=dots"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}