const mix = require("laravel-mix");
const path = require("path");

require('mix-tailwindcss');

// Config
mix.webpackConfig({
  output: {
    chunkFilename: mix.inProduction() ? 'emp-v2/chunks/[id].[contenthash].js' : 'emp-v2/chunks/[id].js'
  },
  resolve: {
    alias: {
      "@": path.resolve(
        __dirname,
        "frontend"
      ),
      "@public": path.resolve(__dirname, "public")
    }
  }
});

// Compile source code
mix
  .ts("frontend/app.ts", "public/emp-v2")
  .extract()
  .vue({version: 3});

// Compile scss
mix
  .sass("frontend/assets/sass/style.scss", "public/emp-v2")
  .tailwind();

// Copy assets folder to public
mix.copyDirectory("frontend/assets/icons", "public/assets/icons");
mix.copyDirectory("frontend/assets/images", "public/assets/images");

// Production setting
if (mix.inProduction()) {
  mix.version();
}

mix.browserSync({
  proxy: "127.0.0.1:8000"
});
