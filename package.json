{"private": true, "engines": {"node": "14.x"}, "scripts": {"development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "build": "npm run production --scripts-prepend-node-path", "production": "mix --production", "dev": "npm run development", "format": "prettier frontend --write", "test": "jest"}, "devDependencies": {"@popperjs/core": "^2.11.5", "@rushstack/eslint-patch": "^1.1.0", "@types/bootstrap": "^5.1.12", "@types/jest": "^28.1.6", "@types/js-cookie": "^3.0.2", "@types/jsdom": "^16.2.14", "@types/node": "^18.7.6", "@types/vue-i18n": "^7.0.0", "@types/yaireo__tagify": "^4.12.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.0-rc.18", "@vue/tsconfig": "^0.1.3", "@vue/vue3-jest": "^28.0.1", "axios": "^0.27.2", "babel-jest": "^28.1.3", "bootstrap": "5", "browser-sync": "^2.27.10", "browser-sync-webpack-plugin": "2.3.0", "camelcase": "^7.0.0", "cypress": "^10.1.0", "deepmerge": "^4.2.2", "eslint": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-vue": "^9.0.0", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "js-cookie": "^3.0.1", "jsdom": "^20.0.0", "laravel-mix": "^6.0.49", "laravel-mix-eslint": "^0.2.0", "lodash": "^4.17.19", "npm-run-all": "^4.1.5", "object-path": "^0.11.8", "path": "^0.12.7", "pinia": "^2.0.14", "postcss": "^8.5.1", "prettier": "^2.5.1", "resolve-url-loader": "^5.0.0", "sass": "^1.52.2", "sass-loader": "^13.0.0", "start-server-and-test": "^1.14.0", "ts-jest": "^28.0.7", "ts-loader": "^9.3.1", "typescript": "~4.7.4", "vue": "^3.2.37", "vue-i18n": "^9.2.2", "vue-inline-svg": "3.1.0", "vue-loader": "^17.0.0", "vue-router": "^4.0.16", "vue-template-compiler": "^2.6.14", "vue-tsc": "^0.38.1"}, "resolutions": {"autoprefixer": "10.4.5"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.1.1", "@sentry/vue": "^6.0.0", "@tinymce/tinymce-vue": "^5.0.0", "@vueform/multiselect": "^2.4.2", "@yaireo/tagify": "^3.6.8", "apexcharts": "^4.5.0", "autoprefixer": "^10.4.20", "mix-tailwindcss": "^1.3.0", "moment": "^2.29.4", "sweetalert2": "^11.4.20", "swiper": "^8.3.2", "tailwindcss": "^3.4.17", "tinymce": "^6.1.0", "unleash-proxy-client": "^3.3.0", "v-calendar": "^3.0.0-alpha.8", "vee-validate": "^4.6.7 ", "vue-currency-input": "^3.0.0", "vue-pdf-embed": "^1.1.5", "vue-skeletor": "^1.0.6", "vue3-apexcharts": "^1.8.0", "vuedraggable": "^4.1.0", "yup": "^0.32.11"}}