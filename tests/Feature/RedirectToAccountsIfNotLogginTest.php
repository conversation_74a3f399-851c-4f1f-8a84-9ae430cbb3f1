<?php

namespace Tests\Feature;

use App\Models\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RedirectToAccountsIfNotLogginTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    public function test_redirect_to_accounts()
    {
        $response = $this->get('/');

        $response
            ->assertStatus(302)
            ->assertRedirect(config('app.accounts_url'));
    }

    public function test_no_redirect_to_accounts_if_logged_in()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
            ->for(Company::factory())
            ->create();

        $this->actingAs($user);

        $response = $this->get('/');

        $response->assertStatus(200);
    }
}
