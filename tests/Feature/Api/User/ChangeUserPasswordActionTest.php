<?php

namespace Tests\Feature\Api\User;

use App\Models\Company;
use App\Models\User;
use Hash;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChangeUserPasswordActionTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test changing user password.
     *
     * @return void
     */
    public function testChangeUserPassword()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
            ->for(Company::factory())
            ->create([
                'password' => bcrypt('old_password'),
            ]);

        // Login the user
        $this->actingAs($user);

        // Make a request to change the password
        $response = $this->putJson('/api/user/password', [
            'old_password' => 'old_password',
            'new_password' => 'new_password',
        ]);

        // Assert the response status code
        $response->assertStatus(200);

        /** @var User $user */
        $user->refresh();

        // Assert new password was updated
        $this->assertTrue(Hash::check('new_password', $user->password));

        // Assert the response data
        $response->assertJson([
            'data' => [
                'id' => $user->id,
                'full_name' => $user->display_name,
                'position' => $user->position,
                'email' => $user->email,
                'phone' => $user->phone,
            ],
        ]);
    }
}
