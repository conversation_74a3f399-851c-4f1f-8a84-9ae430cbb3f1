<?php

namespace Tests\Feature\Api\User;

use App\Models\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GetUserProfileActionTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    public function test_get_user_profile()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
            ->for(Company::factory())
            ->create([
                'display_name' => $this->faker->name(),
            ]);

        $this->actingAs($user);

        $response = $this->getJson('/api/user');

        $response->assertStatus(200);

        $response->assertExactJson([
            'data' => [
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->display_name,
                'roles' => [$user->type],
                'company_id' => $user->company_id,
                'approved' => true,
                'full_name' => $user->display_name,
                'position' => $user->position,
                'phone' => $user->phone,
                'username' => $user->username,
                'is_denied' => false,
                'available_credit' => 0,
                'is_unlocked' => false,
            ],
        ]);
    }
}
