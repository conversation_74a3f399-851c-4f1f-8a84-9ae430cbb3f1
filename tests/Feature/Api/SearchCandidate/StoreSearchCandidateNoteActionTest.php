<?php

namespace Tests\Feature\Api\SearchCandidate;

use App\Models\Company;
use App\Models\SearchCandidate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\HttpFoundation\Response;
use Tests\TestCase;

class StoreSearchCandidateNoteActionTest extends TestCase
{
    use RefreshDatabase;

    public function test_create_note_successfully()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
            ->for(Company::factory())
            ->create();

        $this->actingAs($user);

        $searchCandidate = SearchCandidate::factory()
            ->for(User::factory())
            ->create();

        $response = $this->postJson('api/search-candidates/' . $searchCandidate->id . '/notes', [
            'content' => 'test',
        ]);

        $response->assertStatus(Response::HTTP_CREATED);
    }

    public function test_create_note_without_required_data()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
            ->for(Company::factory())
            ->create();

        $this->actingAs($user);

        $searchCandidate = SearchCandidate::factory()
            ->for(User::factory())
            ->create();

        $this->postJson('api/search-candidates/' . $searchCandidate->id . '/notes')
            ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
            ->assertJson([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'content' => ['The content field is required.'],
                ],
            ]);
    }

    public function test_create_note_with_unauthorized_user()
    {
        $this->postJson('api/search-candidates/1/notes')
            ->assertStatus(Response::HTTP_UNAUTHORIZED);
    }

    public function test_create_note_for_non_exists_search_candidate()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
        ->for(Company::factory())
        ->create();

        $this->actingAs($user);

        $this
            ->postJson('api/search-candidates/1/notes', [
                'content' => 'Content message',
            ])
            ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function test_create_note_with_invalid_data()
    {
        /** @var \Illuminate\Contracts\Auth\Authenticatable */
        $user = User::factory()
        ->for(Company::factory())
        ->create();

        $this->actingAs($user);

        $this
            ->postJson('api/search-candidates/1/notes', [
                'content' => '',
            ])
            ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
            ->assertJson([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'content' => ['The content field is required.'],
                ],
            ]);
    }
}
