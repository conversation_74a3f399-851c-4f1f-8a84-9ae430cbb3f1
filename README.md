# Employer Dash v2  Documentation

## Introduction
Employer dash v2 is dashboard for Employer manages their information, manages jobs, create/edit jobs and manages their candidates

## Getting Started

### Framework and packages
Package we are using:
- `laravel/framework ^8.75`
- `vue ^3.2.37`
- `metronic 8`

### Software requirement
Make sure you have read & install minimum requirement software
- https://github.com/laravel/docs/blob/8.x/deployment.md#server-requirements
- https://vuejs.org/guide/typescript/overview.html#using-vue-with-typescript

## Installation
This is basic installation steps for you to begin

_Step 1_: Clone repository

```shell
<NAME_EMAIL>:topdev/employer-dash-v2.git
```

_Step 2_: Install composer packages & node packages

```shell
cd employer-dash-v2 
composer install
yarn
```

_Step 3_: Configuration

```shell
cp .env.example .env
php artisan key:generate
```
**Note**: please change `.env` file to your local development environment

## Up & Running
_Step 1_: Compiling assets
```shell
yarn dev
```

_Step 2_: Run integrate server
```shell
php artisan serve
```

Terminal will be
```shell
Starting Laravel development server: http://127.0.0.1:8000
[Wed Jul 13 16:39:15 2022] PHP 7.4.29 Development Server (http://127.0.0.1:8000) started
```

Then you can open the browser `http://127.0.0.1:8000`

### Build and deploy to local k3s

configure ../../devops/compose-topdev-environments/.env for local k3s
```shell
EMP_DASH_DOTENV_FILE=.env.dash.topdev.com
VOLUME_NFS_SERVER=*************
VOLUME_NFS_PATH=/private/srv/data/assets
```

```shell
docker compose --project-directory ../../devops/compose-topdev-environments run --rm build-dash-v2 && \
docker compose --project-directory ../../devops/compose-topdev-environments run --rm local-dash-v2
```

## Authentication & Local development
Step 1: Get an Employer user from ams database
```mysql
SELECT
    *
FROM
    users
WHERE
    `type` = 'employer'
  AND `deleted_at` IS NULL
  AND EXISTS( SELECT
                  1
              FROM
                  companies
              WHERE
                  companies.id = users.`company_id`
                AND companies.`deleted_at` IS NULL
                AND companies.status = 1)
LIMIT 1;
```

Step 2: In \App\Providers\AppServiceProvider::boot
```php
\Auth::loginUsingId(49828); // Enter your ID from step 1
```


## Test diagram drawing
Sequence diagram
```mermaid
sequenceDiagram
Alice->>John: Hello John, how are you?
loop Healthcheck
    John->>John: Fight against hypochondria
end
Note right of John: Rational thoughts!
John-->>Alice: Great!
John->>Bob: How about you?
Bob-->>John: Jolly good!
```

Grant chart
```mermaid
gantt
    section Section
    Completed :done,    des1, 2014-01-06,2014-01-08
    Active        :active,  des2, 2014-01-07, 3d
    Parallel 1   :         des3, after des1, 1d
    Parallel 2   :         des4, after des1, 1d
    Parallel 3   :         des5, after des3, 1d
    Parallel 4   :         des6, after des4, 1d
```

