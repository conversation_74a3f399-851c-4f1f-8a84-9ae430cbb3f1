<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'laravelpassport' => [
        'client_id' => env('LARAVELPASSPORT_CLIENT_ID'),
        'client_secret' => env('LARAVELPASSPORT_CLIENT_SECRET'),
        'redirect' => env('LARAVELPASSPORT_REDIRECT_URI'),
        'host' => env('LARAVELPASSPORT_HOST'),
        'userinfo_uri' => env('LARAVELPASSPORT_USERINFO_URL'),
    ],

    'genpdf' => [
        'api_url' => env('GENPDF_API', 'https://api.topdev.vn/export/v2/theme/template1'),
    ],
    'crm' => [
        'base_url' => env('CRM_API_BASE_URL', 'https://crmdev.topdev.asia'),
        'timeout' => 30,
        'auth_code' => env('CRM_API_AUTH_CODE', 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZ3d0RRWUpLb1pJaHZjTkFRRUJCUUFEU3dBd1NBSkJBTkdWNGdzUHk3bzlEcVI3TzQ3K0ZpM0ltNEROa2h3dAp2Nk9OSkJoLytjYkJSZlRnSGRGcExSS1NQTDZ4eHozSjFRK0Y2WlBRR0YrZ2dlald0YStXVHZjQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo='),
    ],

    'unleash' => [
        'app_url' => env('UNLEASH_APP_URL', 'https://git.topdev.asia/api/v4/feature_flags/unleash/155'),
        'instance_id' => env('UNLEASH_INSTANCE_ID', 'w5zjmb-MySGvaED8Dhm3'),
    ],
];
