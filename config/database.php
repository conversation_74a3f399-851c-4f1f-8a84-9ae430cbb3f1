<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql_ams'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'mysql_emp' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DATABASE_HOST', '127.0.0.1'),
            'port' => env('DATABASE_PORT', '3306'),
            'database' => env('DATABASE_DATABASE', 'forge'),
            'username' => env('DATABASE_USERNAME', 'forge'),
            'password' => env('DATABASE_PASSWORD', ''),
            'unix_socket' => env('DATABASE_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'mysql_ams' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_APPLANCER_HOST', '127.0.0.1'),
            'port' => env('DB_APPLANCER_PORT', '3306'),
            'database' => env('DB_APPLANCER_DATABASE', 'forge'),
            'username' => env('DB_APPLANCER_USERNAME', 'forge'),
            'password' => env('DB_APPLANCER_PASSWORD', ''),
            'unix_socket' => env('DB_APPLANCER_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'mysql_cvbuilder' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_CVBUILDER_HOST', '127.0.0.1'),
            'port' => env('DB_CVBUILDER_PORT', '3306'),
            'database' => env('DB_CVBUILDER_DATABASE', 'forge'),
            'username' => env('DB_CVBUILDER_USERNAME', 'forge'),
            'password' => env('DB_CVBUILDER_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'mysql_mail' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_MAIL_HOST', 'mysql'),
            'port' => env('DB_MAIL_PORT', '3306'),
            'database' => env('DB_MAIL_DATABASE', 'mail'),
            'username' => env('DB_MAIL_USERNAME', 'root'),
            'password' => env('DB_MAIL_PASSWORD', 'root'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'client' => 'predis',

        'options' => [
            'cluster' => 'redis',
        ],

        'clusters' => [
            'accounts' => [
                ...explode(',', env('REDIS_ACCOUNTS_URL', '')),

                'options' => [
                    'prefix' => env('REDIS_ACCOUNTS_PREFIX', '{accounts}:')
                ]
            ],

            'cache' => [
                ...explode(',', env('REDIS_CACHE_URL', '')),

                'options' => [
                    'prefix' => env('REDIS_CACHE_PREFIX', '{emp}:')
                ]
            ],
        ],

        'emp' => [
            'url' => env('REDIS_EMP_URL'),
            'host' => env('REDIS_EMP_HOST', '127.0.0.1'),
            'password' => env('REDIS_EMP_PASSWORD'),
            'port' => env('REDIS_EMP_PORT', '6379'),
            'database' => env('REDIS_EMP_DB', '0'),

            'options' => [
                'prefix' => env('REDIS_API_PREFIX', '{emp}:')
            ]
        ],

        'ams' => [
            'url' => env('REDIS_AMS_URL'),
            'host' => env('REDIS_AMS_HOST', '127.0.0.1'),
            'username' => env('REDIS_AMS_USERNAME'),
            'password' => env('REDIS_AMS_PASSWORD'),
            'port' => env('REDIS_AMS_PORT', '6379'),
            'database' => env('REDIS_AMS_DB', '0'),

            'options' => [
                'prefix' => env('REDIS_AMS_PREFIX', '{ams}:')
            ]
        ],

        'system_mail' => [
            'url' => env('REDIS_MAIL_URL'),
            'host' => env('REDIS_MAIL_HOST', '127.0.0.1'),
            'username' => env('REDIS_MAIL_USERNAME'),
            'password' => env('REDIS_MAIL_PASSWORD'),
            'port' => env('REDIS_MAIL_PORT', '6379'),
            'database' => env('REDIS_MAIL_DB', '0'),

            'options' => [
                'prefix' => env('REDIS_MAIL_PREFIX', '{mail}:')
            ]
        ],
    ],


];
