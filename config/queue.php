<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | Lara<PERSON>'s queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for every one. Here you may define a default connection.
    |
    */

    'default' => env('QUEUE_CONNECTION', 'database'),

    'ams' => env('QUEUE_AMS_CONNECTION', 'database_ams'),

    'mail' => env('QUEUE_MAIL_CONNECTION', 'database_mail'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    | Drivers: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'queue_jobs',
            'queue' => env('QUEUE_NAME', 'emp-default'),
            'retry_after' => 90,
            'after_commit' => true,
        ],

        'database_ams' => [
            'driver' => 'database',
            'table' => 'queue_jobs',
            'queue' => env('QUEUE_NAME_AMS', 'ams-default'),
            'retry_after' => 90,
            'after_commit' => true,
        ],

        'database_mail' => [
            'driver' => 'database',
            'connection' => 'mysql_mail',
            'table' => 'jobs',
            'queue' => env('QUEUE_NAME_MAIL', 'mail-default'),
            'retry_after' => 90,
            'after_commit' => true,
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'emp',
            'queue' => env('QUEUE_NAME', '{emp-default}'),
            'retry_after' => 90,
            'block_for' => null,
        ],

        'redis_ams' => [
            'driver' => 'redis',
            'connection' => 'ams',
            'queue' => env('QUEUE_NAME_AMS', '{ams-default}'),
            'retry_after' => 90,
            'block_for' => null,
        ],

        'redis_system_mail' => [
            'driver' => 'redis',
            'connection' => 'system_mail',
            'queue' => env('QUEUE_NAME_MAIL', '{mail-default}'),
            'retry_after' => 90,
            'block_for' => null,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql_ams'),
        'table' => 'queue_failed_jobs',
    ],

];
