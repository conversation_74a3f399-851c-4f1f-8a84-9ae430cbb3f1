{"compilerOptions": {"noImplicitAny": false, "strictFunctionTypes": false, "target": "esnext", "module": "esnext", "strict": false, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "useUnknownInCatchVariables": false, "resolveJsonModule": true, "useDefineForClassFields": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./frontend/*"]}, "types": ["@vue/runtime-dom", "@types/node"]}, "exclude": ["frontend/assets/**/*"]}