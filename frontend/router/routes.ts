import { RouterView } from "vue-router";
import approve from "../middleware/approve";

const routes = [
  {
    name: "home",
    path: "/",
    redirect: "/dashboard",
    component: RouterView,
  },
  {
    path: "/company",
    redirect: "/company/profile",
    name: "company",
    component: RouterView,
    children: [
      {
        path: "/company/profile",
        name: "company-profile",
        component: () =>
          import(
            /* webpackChunkName: "company-profile" */ "@/views/CompanyProfileView.vue"
          ),
      },
      {
        path: "/company/contact",
        name: "company-contact",
        component: () =>
          import(
            /* webpackChunkName: "company-contact" */ "@/views/CompanyContactView.vue"
          ),
      },
    ],
  },
  {
    path: "/jobs",
    name: "manage-jobs",
    beforeEnter: [approve],
    component: RouterView,
    children: [
      {
        path: "",
        name: "jobs",
        component: () =>
          import(
            /* webpackChunkName: "jobs-index" */ "@/views/JobsIndexView.vue"
          ),
      },
      {
        path: "create",
        name: "post-job",
        component: () =>
          import(
            /* webpackChunkName: "jobs-create" */ "@/views/JobsCreateView.vue"
          ),
      },
      {
        path: ":id/edit",
        name: "edit-job",
        component: () =>
          import(
            /* webpackChunkName: "jobs-edit" */ "@/views/JobsEditView.vue"
          ),
      },
    ],
  },
  {
    path: "/dashboard",
    name: "dashboard",
    beforeEnter: [approve],
    component: () => import("@/views/DashBoard.vue"),
  },
  {
    path: "/candidates",
    name: "candidates",
    beforeEnter: [approve],
    component: () => import("@/views/CandidatesIndexView.vue"),
  },
  {
    path: "/candidates/:id",
    name: "candidate-detail",
    beforeEnter: [approve],
    component: () => import("@/views/CandidatesShowView.vue"),
  },
  {
    path: "/search-candidates",
    name: "search-candidates",
    beforeEnter: [approve],
    component: () => import("@/views/SearchResumesIndexView.vue"),
  },
  {
    path: "/search-candidates/:id",
    name: "search-candidates-detail",
    beforeEnter: [approve],
    component: () => import("@/views/SearchResumesShowView.vue"),
  },
  {
    path: "/unlock-candidate-management",
    name: "unlock-candidate-management",
    beforeEnter: [approve],
    component: () => import("@/views/UnlockCandidateManagementView.vue"),
  },
  {
    path: "/my-products",
    name: "my-products",
    beforeEnter: [approve],
    component: RouterView,
    children: [
      {
        path: "/my-products/job-postings",
        name: "my-products-job-postings",
        component: () => import("@/views/JobPosting.vue"),
      }
    ],
  },
  {
    path: "/unauthorized",
    name: "unauthorized",
    component: () => import("@/views/UnauthorizedView.vue"),
  },
  {
    path: "/:catchAll(.*)",
    name: "page-not-found",
    component: () => import("@/views/PageNotFoundView.vue"),
  },
];

export default routes;
