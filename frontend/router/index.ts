import { createRouter, createWebHistory } from "vue-router";

import routes from "./routes";

import { confirmNavigation } from "@/helpers";
import {
  useCandidatesStore,
  useLayoutStore,
  useSearchResumesStore
} from "@/stores";

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 };
  },
});


router.beforeEach(async (to, from, next) => {
  if (to.name == "unauthorized") {
    next();
    return;
  }

  //Reset candidates params
  const candidatesStore = useCandidatesStore();
  if (to.name !== "candidates" && to.name !== "candidate-detail") {
    if (candidatesStore.isChange) {
      candidatesStore.resetCandidatesParams();
    }
  }

  //Reset search resumes params
  const searchResumesStore = useSearchResumesStore();
  if (
    to.name !== "search-candidates" &&
    to.name !== "search-candidates-detail"
  ) {
    if (searchResumesStore.isChange) {
      searchResumesStore.resetResumesParams();
    }
  }

  // Check naviation confirmation
  const layoutStore = useLayoutStore();
  if (layoutStore.isConfirmNavigation) {
    const confirm = await confirmNavigation();

    if (confirm.isConfirmed) {
      layoutStore.resetConfirmNavigation();

      next();
      return;
    } else {
      return false;
    }
  }

  // Looks fine, let's block users behaviour
  layoutStore.blockPage();

  // Ok, nothing to fetch from server, unblock now
  layoutStore.unBlockPage();

  // Finally can't do anything else then let user go through :D
  next();
});

export default router;
