<template>
  <!--begin::Action group-->
  <div class="d-flex align-items-stretch overflow-auto">
    <!--begin::Theme mode-->
    <div class="d-flex align-items-center gap-25px">
      <div>
        <AppButtonPostJob size="sm"/>
      </div>

      <!--begin::Locale switcher-->
      <div>
        <LocaleSwitcher />
      </div>
      <!--end::Locale switcher-->
      <!--begin::Notify-->
      <div class="d-none">
        <Notify />
      </div>
      <!--end::Notify-->
      <div>
        <span
          class="cursor"
          id="dropdownAccount"
          data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-end"
          data-kt-menu-flip="center, bottom"
        >
          <inline-svg src="/assets/icons/user-profile.svg" />
        </span>
        <UserMenu />
      </div>
    </div>
    <!--end::Theme mode-->
  </div>
  <!--end::Action group-->
</template>

<script lang="ts" setup>
import AppButtonPostJob from "@/components/AppButtonPostJob.vue";
import LocaleSwitcher from "@/layout/header/partials/LocaleSwitcher.vue";
import UserMenu from "@/layout/header/partials/UserMenu.vue";
import Notify from "@/layout/header/partials/TheNotify.vue";
</script>
