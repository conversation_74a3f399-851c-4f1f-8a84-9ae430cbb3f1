<template>
  <!--begin::Notification bar-->
  <div v-if="shouldShowNotification" class="notification-bar" ref="notificationBar">
    <div class="notification-bar__content">
      <div class="notification-bar__text">
        <p v-html="translate('layout_notification_bar_title')"></p>
      </div>
      <button class="notification-bar__close" @click="closeNotification">
        <svg width="19" height="19" viewBox="0 0 19 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M0.439668 1.4384C0.72096 1.15719 1.10242 0.999221 1.50017 0.999221C1.89791 0.999221 2.27938 1.15719 2.56067 1.4384L9.00017 7.8779L15.4397 1.4384C15.578 1.29514 15.7436 1.18086 15.9266 1.10225C16.1096 1.02364 16.3064 0.982256 16.5056 0.980525C16.7047 0.978795 16.9023 1.01675 17.0866 1.09217C17.2709 1.16759 17.4384 1.27897 17.5793 1.41981C17.7201 1.56065 17.8315 1.72813 17.9069 1.91247C17.9823 2.09681 18.0203 2.29433 18.0185 2.4935C18.0168 2.69267 17.9754 2.8895 17.8968 3.07251C17.8182 3.25551 17.7039 3.42103 17.5607 3.5594L11.1212 9.9989L17.5607 16.4384C17.8339 16.7213 17.9851 17.1002 17.9817 17.4935C17.9783 17.8868 17.8205 18.263 17.5424 18.5411C17.2643 18.8192 16.8881 18.977 16.4948 18.9804C16.1015 18.9838 15.7226 18.8326 15.4397 18.5594L9.00017 12.1199L2.56067 18.5594C2.27776 18.8326 1.89886 18.9838 1.50557 18.9804C1.11227 18.977 0.736052 18.8192 0.45794 18.5411C0.179828 18.263 0.0220741 17.8868 0.0186565 17.4935C0.0152389 17.1002 0.166431 16.7213 0.439668 16.4384L6.87917 9.9989L0.439668 3.5594C0.158462 3.27811 0.000488281 2.89665 0.000488281 2.4989C0.000488281 2.10115 0.158462 1.71969 0.439668 1.4384Z" fill="currentColor"/>
        </svg>
      </button>
    </div>
  </div>
  <!--end::Notification bar-->
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
import { translate } from "@/helpers";

const notificationClosedAt = ref<string>(localStorage.getItem("notificationClosedAt"));
const notificationHeight = ref<number>(0);
const notificationBar = ref(null);

const shouldShowNotification = computed(() => {
  if (!notificationClosedAt.value) return true;
  const hoursPassed = (Date.now() - parseInt(notificationClosedAt.value)) / (1000 * 60 * 60);
  return hoursPassed >= 24;
});

const closeNotification = () => {
  const currentTime = Date.now().toString();
  localStorage.setItem("notificationClosedAt", currentTime);
  notificationClosedAt.value = currentTime;
  adjustAsideAndWrapperHeight(false);
};

const adjustAsideAndWrapperHeight = (add: boolean) => {
  nextTick(() => {
    const elementHeight = notificationHeight.value
      = notificationBar.value ? notificationBar.value.offsetHeight : notificationHeight.value;
    const ktAside = document.getElementById("kt_aside");
    const ktWrapper = document.getElementById("kt_wrapper");

    if (!!ktAside) {
      const currentTop = parseInt(window.getComputedStyle(ktAside).top, 10) || 0;
      ktAside.style.top = add ? `${currentTop + elementHeight}px` : `${currentTop - elementHeight}px`;
    }

    if (!!ktWrapper) {
      const currentPaddingTop = parseInt(window.getComputedStyle(ktWrapper).paddingTop, 10) || 0;
      ktWrapper.style.paddingTop = add ? `${currentPaddingTop + elementHeight}px` : `${currentPaddingTop - elementHeight}px`;
    }
  });
};

onMounted(() => {
  if (shouldShowNotification.value) {
    adjustAsideAndWrapperHeight(true);
  }
});
</script>
