<template>
  <button @click="switchLocale" class="btn btn-link p-0">
    <inline-svg src="/assets/icons/flags/vie.svg" v-show="locale === 'en'" />
    <inline-svg src="/assets/icons/flags/en.svg" v-show="locale === 'vi'" />
  </button>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";

import { useTaxonomiesStore } from "@/stores";

const { locale } = useI18n();
const taxonomiesStore = useTaxonomiesStore();

const switchLocale = () => {
  locale.value = locale.value === "vi" ? "en" : "vi";
  localStorage.setItem("lang", locale.value);
  // call action to update label of multi select options
  taxonomiesStore.setOptionsByLang(locale.value);
};
onMounted(() => {
  if (localStorage.getItem("lang")) {
    locale.value = localStorage.getItem("lang");
  } else {
    localStorage.setItem("lang", locale.value);
  }
});
</script>
