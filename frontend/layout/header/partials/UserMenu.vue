<template>
  <!--begin::Menu-->
  <div
    class="menu menu-sub menu-sub-dropdown menu-column menu-gray-600 menu-state-bg-light-primary fw-bold py-4 mt-5 fs-6 w-225px"
    data-kt-menu="true"
  >
    <!--begin::Menu item-->
    <div class="menu-item px-2">
      <div class="menu-content d-flex align-items-center px-3">
        <!--begin::Username-->
        <div class="d-flex flex-column w-100">
          <div class="fs-5 text-overflow-ellipsis">
            {{ `${translate("layout_hi")}, ${authStore.user.username}` }}
          </div>
          <span class="fs-7 text-gray-800 fw-light text-overflow-ellipsis">
            {{ authStore.user.email }}
          </span>
          <div
            v-if="authStore.user.is_unlocked"
            class="fw-normal text-overflow-ellipsis color-credits d-flex align-items-center"
          >
            {{
              translate("layout_credits_number", [
                authStore.user.available_credit.toLocaleString("en"),
              ])
            }}
            <button type="button" class="px-0 px-0 border-0 bg-white ms-1">
              <inline-svg
                src="/assets/icons/question.svg"
                width="15px"
                height="15px"
              />
            </button>
          </div>
        </div>
        <!--end::Username-->
      </div>
    </div>
    <!--end::Menu item-->

    <!--begin::Menu separator-->
    <div class="separator my-1"></div>
    <!--end::Menu separator-->

    <!--begin::Menu item-->
    <div class="menu-item px-3">
      <a
        @click="openAccountSettingsModal"
        class="menu-link fw-light px-1 w-100 cursor"
        >{{ translate("layout_account_settings") }}</a
      >
    </div>
    <!--end::Menu item-->

    <!--begin::Menu separator-->
    <div class="separator my-1"></div>
    <!--end::Menu separator-->

    <!--begin::Menu separator-->
    <!-- <div class="separator my-1"></div> -->
    <!--end::Menu separator-->

    <!--begin::Menu item-->
    <div class="menu-item px-3">
      <a :href="logoutUrl" class="menu-link fw-light px-2">{{
        translate("layout_signout")
      }}</a>
    </div>
    <!--end::Menu item-->
  </div>
  <!--end::Menu-->
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { Modal } from "bootstrap";

import { translate } from "@/helpers";
import { useAuthStore, useSearchResumesStore } from "@/stores";

const authStore = useAuthStore();
const searchResumesStore = useSearchResumesStore();
const logoutUrl = ref("");

const openAccountSettingsModal = () => {
  Modal.getOrCreateInstance("#modal-account-settings").show();
};

const openCreditsUsageModal = () => {
  Modal.getOrCreateInstance("#modal-credits-usage").show();
};

onMounted(() => {
  logoutUrl.value = process.env.MIX_OAUTH2_URL_LOGOUT;
});
</script>
