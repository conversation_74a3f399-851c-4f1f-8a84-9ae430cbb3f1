<template>
  <div id="kt_header" class="header align-items-stretch">
    <div class="header-brand">
      <!--begin::Aside minimize-->
      <div
        @click="handleToggleSidebar"
        id="kt_aside_toggle"
        class="btn btn-icon px-0 btn-active-color-primary aside-minimize justify-content-start"
      >
        <span v-if="isActiveSidebar" class="svg-icon svg-icon-1 me-n1">
          <inline-svg src="/assets/icons/hamburger.svg" />
        </span>

        <span v-else class="svg-icon svg-icon-1">
          <inline-svg src="/assets/icons/aside/arr077.svg" />
        </span>
      </div>
      <!--end::Aside minimize-->

      <!--begin::Aside toggle-->
      <div
        class="d-flex align-items-center d-lg-none ms-n3 me-1"
        title="Show aside menu"
      >
        <div
          @click="handleToggleSidebar"
          id="kt_aside_mobile_toggle"
          class="btn btn-icon btn-active-color-primary py-3 px-4 aside-minimize-mobile"
        >
          <span class="svg-icon svg-icon-1 me-n1 minimize-active">
            <inline-svg src="/assets/icons/hamburger.svg" />
          </span>

          <span class="svg-icon svg-icon-1 minimize-default">
            <inline-svg src="/assets/icons/aside/arr077.svg" />
          </span>
        </div>
      </div>
      <!--end::Aside toggle-->

      <!--begin::Logo-->
      <router-link :to="{ name: 'dashboard' }">
        <img
          alt="Logo"
          id="topdev-logo"
          src="/assets/images/logo.png"
          class="h-25px h-lg-25px"
        />
      </router-link>
      <!--end::Logo-->
    </div>

    <div class="toolbar d-flex align-items-stretch">
      <div
        class="container-fluid py-3 py-lg-0 d-flex flex-column flex-lg-row align-items-lg-stretch justify-content-lg-between"
        id="kt_toolbar_container"
      >
        <PageTitle :title="title"></PageTitle>
        <KTTopbar />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, watch, ref } from "vue";

import PageTitle from "@/layout/page-title/PageTitle.vue";
import KTTopbar from "@/layout/header/TheTopbar.vue";

import { useLayoutStore } from "@/stores/layout";

const layoutStore = useLayoutStore();
const isActiveSidebar = computed(() => layoutStore.isActiveSidebar);

const title = computed(() => {
  return layoutStore.pageTitle;
});

const handleToggleSidebar = () => {
  layoutStore.toggleActiveSidebar(!layoutStore.isActiveSidebar);
};

watch(
  () => layoutStore.isActiveSidebar,
  (isActive) => {
    if (!isActive) {
      document.body.setAttribute("data-kt-aside-minimize", "on");
    } else {
      document.body.removeAttribute("data-kt-aside-minimize");
    }
  }
);
</script>
