<template>
  <!--begin::Page title-->
  <div
    data-kt-swapper="true"
    data-kt-swapper-mode="prepend"
    data-kt-swapper-parent="{default: '#kt_content_container', 'lg': '#kt_toolbar_container'}"
    class="page-title d-flex justify-content-center flex-column me-5"
  >
    <!--begin::Title-->
    <h1 class="d-flex align-items-center fw-bolder my-1">
      {{ translate(title) }}
    </h1>
    <!--end::Title-->
  </div>
  <!--end::Page title-->
</template>

<script lang="ts" setup>
import { translate } from "@/helpers";

interface Props {
  title: string;
}
defineProps<Props>();
</script>
