<template>
  <!--begin::Menu wrapper-->
  <div id="kt_aside_menu_wrapper" ref="scrollElRef" class="hover-scroll-overlay-y mt-5 mt-lg-5">
    <!--begin::Menu-->
    <div id="#kt_aside_menu"
      class="menu menu-column menu-title-gray-800 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500 menu-sidebar"
      data-kt-menu="true">
      <!--begin::Main menu config-->
      <template v-for="(item, index) in mainMenuConfig" :key="index">
        <template v-if="item.heading">
          <div class="menu-item position-relative has-sub-menu" v-if="!item.route && item.childItems && item.childItems.length"
            :class="{
              expanded: expanables.includes(index),
            }">
            <div class="menu-link here" @click="onClickExpandBtn(index)" :class="{
              active: item.activeRoutes.includes(route.name.toString()),
              expanded: item.activeRoutes.includes(route.name.toString()),
            }">
              <span class="menu-icon svg-icon svg-icon-2">
                <inline-svg :src="item.svgIcon" />
              </span>
              <span class="menu-title">{{ translate(item.heading) }}</span>
              <span class="menu-expanable"></span>
            </div>
            <div class="menu-sub">
              <template v-for="(childItem, childIndex) in item.childItems" :key="childIndex">
                <router-link class="menu-item menu-link" :class="{
                  active: childItem.activeRoutes.includes(route.name.toString()),
                }" :to="childItem.route">
                  <span v-if="childItem.svgIcon" class="menu-icon svg-icon svg-icon-2">
                    <inline-svg :src="childItem.svgIcon" />
                  </span>
                  <span class="menu-title">{{ translate(childItem.heading) }}</span>
                  <span v-if="childItem.route === '/search-candidates'" class="menu-new-badge">{{
                    translate("layout_new_badge") }}
                  </span>
                </router-link>
              </template>
            </div>
          </div>
          <div v-else class="menu-item position-relative">
            <router-link class="menu-link" :class="{
              active:
                (route.name === 'candidate-detail' &&
                  item.route === '/candidates') ||
                (route.name === 'search-candidates-detail' &&
                  item.route === '/search-candidates'),
            }" active-class="active" :to="item.route" @click="onClickExpandBtn(-1)">
              <span class="menu-icon svg-icon svg-icon-2">
                <inline-svg :src="item.svgIcon" />
              </span>
              <span class="menu-title">{{ translate(item.heading) }}</span>
              <span v-if="item.route === '/search-candidates'" class="menu-new-badge">{{ translate("layout_new_badge")
                }}
              </span>
            </router-link>
          </div>
        </template>
      </template>
      <!--end::Main menu config-->

      <!--begin::Post job button-->
      <div class="menu-post-job-container">
        <AppButtonPostJob class="menu-link" size="md" />
      </div>
      <!--end::Post job button-->

      <!--begin:: Contact Information -->
      <div class="mt-5 px-3 non-collapse-information">
        <div class="pt-4 border-top border-gray-400">
          <p class="fw-bolder my-0 text-uppercase">
            {{ translate("layout_contact") }}
          </p>
          <p class="my-0"><b>Hotline:</b> +8428 6656 7848</p>
          <p class="text-primary my-0">
            <b class="text-black">Email:</b> <EMAIL>
          </p>
        </div>
      </div>

      <div class="mt-5 d-none collapse-information">
        <div class="border-top border-gray-400">
          <div class="item-collapse-information cursor-pointer px-3 py-3 position-relative text-center">
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/aside/phone-2.svg" />
            </span>
            <div class="collapse-information-info position-absolute">
              <p class="fw-bolder my-0 text-uppercase">
                {{ translate("layout_contact") }}
              </p>
              <p class="my-0"><b>Hotline:</b> +8428 6656 7848</p>
            </div>
          </div>
          <div class="item-collapse-information cursor-pointer px-3 py-3 position-relative text-center">
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/aside/email-2.svg" />
            </span>
            <div class="collapse-information-info position-absolute">
              <p class="fw-bolder my-0 text-uppercase">
                {{ translate("layout_contact") }}
              </p>
              <p class="text-primary my-0">
                <b class="text-black">Email:</b> <EMAIL>
              </p>
            </div>
          </div>
        </div>
      </div>
      <!--End:: Contact Information -->
      <!--Begin:: Side banner-->
      <AppSideBanner />
      <!--End:: Side banner-->
    </div>
    <!--end::Menu-->
  </div>
  <!--end::Menu wrapper-->
</template>

<script lang="ts" setup>
import { useRoute } from "vue-router";
import AppButtonPostJob from "@/components/AppButtonPostJob.vue";
import AppSideBanner from "@/components/AppSideBanner.vue";

import { MainMenuConfig } from "@/config";
import { translate } from "@/helpers";

import { useAuthStore, useCandidatesStore } from "@/stores";
import { computed, ref, watch } from "vue";

const route = useRoute();
const authStore = useAuthStore();

const mainMenuConfig = computed(() => {
  return MainMenuConfig.filter((item) => {
    return true;
  });
});

const expanables = ref<number[]>([]);

const onClickExpandBtn = (menuIndex: number) => {
  if (menuIndex == -1) {
    expanables.value = [];
    return;
  }
  const findIndex = expanables.value.indexOf(menuIndex);
  if (findIndex > -1) {
    expanables.value.splice(findIndex);
  } else {
    expanables.value.push(menuIndex);
  }
  
  const candidatesStore = useCandidatesStore();
  candidatesStore.resetCandidatesParams();
}

watch(
  () => route.name,
  (name) => {
    expanables.value = [mainMenuConfig.value.findIndex((value) => value.activeRoutes && value.activeRoutes.length && value.activeRoutes.includes(name.toString()))];
  },
  { deep: true }
)

</script>
