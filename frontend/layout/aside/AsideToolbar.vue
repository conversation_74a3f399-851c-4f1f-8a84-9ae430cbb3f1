<template>
  <!--begin::User-->
  <div
    class="aside-user d-flex align-items-sm-center justify-content-center py-5"
  >
    <!--begin::Wrapper-->
    <div class="aside-user-info flex-row-fluid flex-wrap ms-5">
      <!--begin::Section-->
      <div class="d-flex">
        <!--begin::User menu-->
        <div class="me-n2">
          <!--begin::Action-->
          <UserMenu />
          <!--end::Action-->
        </div>
        <!--end::User menu-->
      </div>
      <!--end::Section-->
    </div>
    <!--end::Wrapper-->
  </div>
  <!--end::User-->
</template>

<script lang="ts">
import UserMenu from "@/layout/header/partials/UserMenu.vue";

export default {
  name: "kt--aside-toolbar",
  components: {
    UserMenu,
  },
};
</script>
