<template>
  <!--begin::Aside-->
  <aside
    id="kt_aside"
    class="aside aside-custom"
  >
    <!--begin::Aside menu-->
    <div class="aside-menu flex-column-fluid">
      <KTMenu />
    </div>
    <!--end::Aside menu-->
  </aside>
  <TheSideBannerModal />
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import { useLayoutStore } from "@/stores";
import KTMenu from "@/layout/aside/Menu.vue";
import TheSideBannerModal from "@/components/TheSideBannerModal.vue";

const layoutStore = useLayoutStore();

//Life cycle
onMounted(async () => {
  await layoutStore.getBannerModal("Employerdash_banner");
});
</script>
