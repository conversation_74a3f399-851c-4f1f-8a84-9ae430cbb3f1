export interface Contact {
  email?: string;
  phone?: string;
}
export interface Employer {
  id?: number;
  full_name?: string;
  position?: string;
  email?: string;
  phone?: string;
}
export interface Company {
  id: number;
  display_name: string;
  image_logo: Image;
  slug: string;
  detail_url: string;
  description: string;
  website: string;
  tagline: string;
  nationalities: Array<string>;
  products: Array<Products>;
  social_network: Social;
  addresses: Array<Addresses>;
  num_employees: number;
  image_cover: Image | null;
  image_galleries: Array<Image>;
  benefits: Array<Benefits>;
  skills_ids: Array<number>;
  industries_ids: Array<number>;
  faqs: Array<Faqs>;
  erc_file: ERCFile | null;
  status: number | null;
}

export interface Image {
  id: number | null;
  url: string;
}

export interface ERCFile {
  id: number | null;
  url: string;
  filename: string;
}

export interface Products {
  id?: number;
  name: string;
  description: string;
  image: Image;
  link: string;
}

export interface Social {
  facebook: string;
  youtube: string;
  linkedin: string;
  share_alt: string;
}

export interface Addresses {
  id: number;
  ward_id: string;
  province_id: string;
  district_id: string;
  full_address: string;
  street: string;
}

interface Benefits {
  value: string;
  icon: string;
}

interface Faqs {
  question: string;
  answer: string;
  active: number;
}
