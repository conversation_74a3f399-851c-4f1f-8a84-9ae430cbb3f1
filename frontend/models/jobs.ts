export interface Jobs {
  id: number;
  name: string;
  location: string;
  salary: string;
  package: string;
  published_date: string | null;
  expires_in: number | null;
  expires_at: number | null;
  created_by: string;
  status: string;
  is_48h: boolean;
  application_count: number;
  views_count: number;
  skills: Array<string>;
  detail_url: string;
  best_match_count: number;
}

export interface Meta {
  total: number;
  per_page: string;
  current_page: number;
  last_page: number;
  from: number;
  to: number;
  links: Array<{
    url: string;
    label: string;
    active: boolean;
  }>;
}

export interface JobsParams {
  page?: number;
  page_size?: number;
  location_id?: number | null;
  job_id?: number | null;
  skills_id?: string | [];
  status?: number | null;
  created_by?: number | null;
  query?: string;
}

export interface JobsFacetes {
  status: Array<{
    total: number;
    status_code: string;
  }>;
}

//Job form
export interface JobForm {
  id?: number;
  title: string;
  content: string;
  requirements: string;
  responsibilities: string;
  addresses_id: Array<number>;
  salary: Salary;
  benefits: Array<Benefits>;
  experiences_ids?: Array<number> | null;
  contract_type: Array<string>;
  job_levels: Array<number>;
  job_types: Array<number>;
  skills_ids: Array<number>;
  recruiment_process: Array<{
    name: string;
  }>;
  emails_cc: Array<string>;
  note?: string;
  status?: string;
  created_at?: string;
  created_by?: string;
  skills?: Array<string>;
  levels?: Array<string>;
  types?: Array<string>;
  experiences?: Array<string>;
  addresses?: Array<{
    id: number;
    ward_id?: string;
    province_id: string;
    district_id?: string;
    full_address: string;
    street?: string;
    order: number;
  }>;
}

export interface Salary {
  value: string;
  currency: string;
  unit: string;
  is_negotiable: number;
  max: number;
  min: number;
  min_estimate: number;
  max_estimate: number;
}

export interface MainContent {
  title: string;
  content: string;
  requirements: string;
  responsibilities: string;
}

export interface JobInformation {
  addresses_id: Array<number>;
  salary: Salary;
  experiences_ids: Array<number>;
  job_levels: Array<number>;
  job_types: Array<number>;
  skills_ids: Array<number>;
  recruiment_process: Array<object>;
}

export interface Benefits {
  name: string;
  value: string;
  icon: string;
}

export interface EmailAndNote {
  emails_cc: Array<string>;
  note: string;
}

export interface FilterOptions {
  province?: Array<string>;
  jobs?: Array<string>;
  employerName?: Array<string>;
  taxonomiesSkills?: Array<string>;
  status?: Array<string>;
}

export type JobCategory = {
  id: number;
  parent_id?: number|null;
  name: string;
  type: 'category' | 'role';
  sort_order: number;
}

export type JobRoles = {
  [key: number]: JobCategory[]
}

export type JobCategories = {
  categories: JobCategory[],
  roles: JobRoles
}