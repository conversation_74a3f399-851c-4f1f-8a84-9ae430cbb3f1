export interface ResumesParams {
  keyword: string[];
  page: number;
  page_size: number;
  filter: ResumesFilterParams;
  showResumesUnlocked: boolean;
  showWishList: boolean;
  selectedCandidateID: number;
}

export interface TimeRange {
  start: string;
  end: string;
}

export interface ResumesFilterParams {
  skill: Array<string>;
  experience: Array<string>;
  location: string;
  language: string;
  candidate_language: string;
  timeRange: TimeRange;
}

export interface ShoppingMe {
  resumes_wishlist: Set<number>;
  resumes_viewed: Set<number>;
  resumes_unlocked: Set<number>;
  resumes_noted: Set<number>;
  credits: number;
}

export interface Option {
  value: number | string;
  label: string;
}

export type SearchCandidate = {
  id: number;
  fullname: string;
  current_job: string;
  credit: number;
  skills: { skill_id: number; skill_name: string }[];
  address: string;
  educations: {
    from: number;
    to: number;
    school_name: string;
    is_studying_here: boolean;
    degree: string;
    description: string;
  }[];
  experiences: {
    from: number;
    to: number;
    company: string;
    description: string;
    is_working_here: boolean;
    position: string;
  }[];
  willing_to_work: boolean;
  years_of_exp: number;
  viewed_count: number;
  unlocked_count: number;
  last_updated_at: string;
  email: string;
  phone: string;
  is_unlocked: boolean;
  is_expired: boolean;
  is_saved: boolean;
  is_viewed: boolean;
  province: string | null;
  type?: string;
  summary: string;
  expected_salary?: string;
  avatar_url: string | null;
  force_show_willing_to_work_status_tip?: boolean;
};

export type SearchCandidateNoteItem = {
  id: number;
  user_name: string;
  user_id: number;
  content: string;
  created_at: string;
  updated_at: string;
};

export type NotesDataState = {
  editId: number | null;
  noteInputValue: string | "";
  listNotes: SearchCandidateNoteItem[];
  isActionLoading: boolean | false;
  isListNotesLoading: boolean | false;
};

export type CheckCreditBeforeUnlockCandidate = {
  available: number | null;
  credit: number | null;
  is_unlocked: boolean;
};

export type QueryParamsType =
  | keyof ResumesFilterParams
  | keyof ResumesParams
  | "timeRange_start"
  | "timeRange_end"
  | "keyword";
