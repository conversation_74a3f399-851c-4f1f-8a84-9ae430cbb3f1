export type SortType = {
  field: string,
  direction: 'asc' | 'desc' | null
}

export type JobPostingHistoryRequestType = {
  page_invoice: number;
  per_page_invoice: number;
  page_usage: number;
  per_page_usage: number;
  invoice_order_by?: "desc" | "asc";
  invoice_order_by_field?: string;
  from_date?: string;
  to_date?: string;
}

export type InvoiceSearchPackge = {
  id: number;
  name: string;
  qty: number;
  used: number;
  remain: number;
  free_package: boolean;
  expired_at: string;
}

export type Invoice = {
  invoice: string;
  total: number;
  paid_at: string;
  search_packages: InvoiceSearchPackge[];
}

export type InvoiceResponse = {
  current_page: number;
  data: Invoice[],
  last_page: number;
}

export type JobPackageSummary = {
  total: number;
  top_job: number;
  distinction: number;
  basic_plus: number;
  basic: number;
}

export type JobPackgeUsage = {
  created_at: string;
  job_title: string;
  package_name: string;
  id: number;
  company_id: number;
  job_id: number;
  job_detail: string;
}

export type JobPackageUsageResponse = {
  current_page: number;
  data: JobPackgeUsage[],
  last_page: number;
}

export type JobPostingResponse = {
  data: {
    invoices: InvoiceResponse;
    summary: JobPackageSummary;
    usages: JobPackageUsageResponse;
  }
}

export type JobPostingTimeRangeType = {
  start: string;
  end: string;
}

export type AvailablePackage = {
  value: string;
  is_free_package: boolean;
  label: string;
  expired_at: string;
}

export type CompanyRequestUpgradeType = {
  fullname: string;
  company_name: string;
  phone_number: string;
}
