export interface ChartDataItem {
    label: string;
    count: number;
    percent: number;
    color: string;
    bgColor: string;
    textColor: string;
}
export interface SeriesItem {
    name: string;
    data: number[];
    color: string;
}
export interface PackageItem {
    quantity: number;
    title: string;
    description: string;
    bgColor: string;
    textColor: string;
}
export interface JobStatuses {
    status: string;
    label: string;
    total: number;
    bgColor: string;
    textColor: string;
}
export interface ScoreCardStats {
    applications_today: number;
    not_viewed_application: number;
    total_application: number;
    view_job: number;
    viewed_application: number;
}