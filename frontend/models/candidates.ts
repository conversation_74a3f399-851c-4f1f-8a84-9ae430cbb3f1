export interface Candidate {
  applied_at: string;
  applied_status: string;
  cover_letter: string;
  download_cv_url: string;
  email: string;
  employer_note: string | null;
  full_name: string;
  has_cover: boolean;
  id: number;
  is_remove_cv: boolean;
  is_resume_exists: boolean;
  job_detail_url: string;
  job_title: string;
  location: string;
  phone: string | null;
  read_status: boolean;
  recalled_at: string;
  skills: Array<string>;
  view_cv_url: string | null;
  procedure_status: string | null;
  cv_profile_experiences: {
    from: number;
    to: number;
    company: string;
    description: string;
    is_working_here: boolean;
    position: string;
  }[];
  cv_profile_educations: {
    from: number;
    to: number;
    school_name: string;
    is_studying_here: boolean;
    degree: string;
    description: string;
  }[];
  avg_skill_match: number;
  matching_status: number;
  yoe: number;
}

interface Option {
  value: number;
  label: string;
}

interface OptionString {
  value: string;
  label: string;
}

export interface TimeRange {
  start: string;
  end: string;
}

export interface OptionTitle {
  value: number;
  title: string;
  label: string;
  published_at: string;
}
export interface JobList {
  value: number;
  title: string;
  label: string;
  published_at: string;
}

export interface CandidatesTaxonomies {
  candidatesProvince: Array<Option>;
  applicationStatus: Array<Option>;
  procedureStatus: Array<OptionString>;
  experiences: Array<Option>;
  jobTitles: Array<OptionTitle>;
  matchingStatus: Array<Option>;
}

export interface CandidatesParamsRequest {
  page?: number;
  page_size?: number;
  job_id: number | null;
  application_status: number | null;
  procedure_status: number | null;
  location_id: string;
  skills_id: string;
  experience_ids: string;
  applied_date_from: string | null;
  applied_date_to: string | null;
  query: string | null;
  matching_status: number | null;
}

export interface CandidatesFilterParams {
  job_id: number;
  application_status?: number;
  procedure_status?: number;
  location_id: string;
  skills_id: Array<string>;
  experience_ids: Array<number>;
  timeRange: TimeRange;
  matching_status?: number;
}

export interface CandidatesParams {
  query: string;
  page?: number;
  page_size?: number;
  filter: CandidatesFilterParams;
}

interface ValuesStatusCandidates {
  id: number;
  value: number;
}

export interface OptionStatusCandidates {
  values: Array<ValuesStatusCandidates> | null;
}

export interface OptionType {
  option: { label: string };
}

export interface ValuesType {
  values: {
    value: string;
    label: string;
    title?: string;
  }[];
}
