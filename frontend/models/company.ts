// Package
export interface PackageType {
  current_page: number;
  data: PackageDataType[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url: null;
  path: string;
  per_page: string;
  prev_page_url: null;
  to: number;
  total: number;
}

export interface PackageDataType {
  id: number;
  created_at: string | null;
  search_package_id: number;
  valid_at: string;
  expired_at: string;
  status: "Expired" | "Active" | "Inactive";
  search_package: SearchPackage;
}

export interface SearchPackage {
  id: number;
  name: string;
}

export interface Link {
  to: string;
  url: string;
  label: string;
  active: boolean;
}

// Usage
export interface UsageType {
  current_page: number;
  data: UsageDataType[];
  first_page_url: string;
  from: string | null;
  last_page: number;
  last_page_url: string;
  links: Link[];
  next_page_url: string | null;
  path: string;
  per_page: string;
  prev_page_url: string;
  to: number | null;
  total: number;
}

export interface UsageDataType {
  id: number;
  type: TransactionType;
  credit: number;
  candidate_name: string | null;
  created_by_name: string | null;
  created_at: string;
}

export type TransactionType = "In" | "Out" | "Refund" | "Expired";

export type PackageSortType =
  | "package_paid"
  | "status"
  | "purchased_date"
  | "activated_at"
  | "expired_at";

export interface CreditParamsType {
  page_package: number;
  per_page_package: number;
  page_usage: number;
  per_page_usage: number;
  package_order_by: "desc" | "asc";
  package_order_by_field: PackageSortType;
  from_date?: string;
  to_date?: string;
}
