

interface MenuItemsType {
  heading: string;
  route: string;
  activeRoutes?: string[];
  svgIcon?: string;
}

interface MainMenuType extends MenuItemsType {
  childItems?: MenuItemsType[];
}

const MainMenuConfig: MainMenuType[] = [
  {
    heading: "top_bar_dashboard",
    route: "/dashboard",
    svgIcon: "/assets/icons/aside/dashboard.svg",
  },
  {
    heading: "top_bar_company_profile",
    route: "/company",
    svgIcon: "/assets/icons/aside/company-profile.svg",
  },
  {
    heading: "layout_manage_jobs",
    route: "/jobs",
    svgIcon: "/assets/icons/aside/manage-job.svg",
  },
  {
    heading: "layout_manage_candidates",
    route: "/candidates",
    svgIcon: "/assets/icons/aside/manage-candidates.svg",   

  },
  {
    heading: "layout_search_candidates",
    route: "/search-candidates",
    svgIcon: "/assets/icons/aside/search-candidates.svg",
  },
  {
    heading: "layout_credit_management",
    route: "/unlock-candidate-management",
    svgIcon: "/assets/icons/aside/credit-management.svg",
  },
  {
    heading: "layout_my_product_management",
    route: null,
    activeRoutes: ['my-products-job-postings'],
    svgIcon: "/assets/icons/aside/my-products.svg",
    childItems: [
      {
        heading: "layout_job_posting_management",
        route: "/my-products/job-postings",
        activeRoutes: ['my-products-job-postings']
      }
    ]
  },
];

export default MainMenuConfig;

interface TabMenuConfigItem {
  name: string;
  title: string;
}
const CompanyProfileMenuConfig: TabMenuConfigItem[] = [
  {
    name: "company-profile",
    title: "top_bar_company_profile",
  },
  {
    name: "company-contact",
    title: "top_bar_contact_information",
  },
];

const ManageJobMenuConfig: TabMenuConfigItem[] = [
  {
    name: "jobs",
    title: "top_bar_all_jobs",
  },
  {
    name: "post-job",
    title: "top_bar_post_job",
  },
];

const MyProductMenuConfig: TabMenuConfigItem[] = [
  {
    name: "my-products-job-postings",
    title: "layout_job_posting_management",
  }
];

export { CompanyProfileMenuConfig, ManageJobMenuConfig, MyProductMenuConfig };
