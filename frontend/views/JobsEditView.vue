<template>
  <TheJobsLayout>
    <DebugPanel v-if="isDevMode" :data="formValue" />
    <form v-if="!isEditJobLoading" @submit="onSubmit">
      <!-- head  -->
      <div class="d-flex align-items-center mt-2 mb-1">
        <h2 class="my-0 me-3">{{ translate("job_form_edit_job") }}</h2>
        <button
          type="button"
          class="btn-topdev-filter"
          :class="[jobStatus === 'Open' ? 'open' : 'review']"
        >
          {{ jobStatus }}
        </button>
      </div>
      <p class="heading">
        {{ translate("job_form_job_updated_by") }}
        <b>{{ authStore.user?.email }}</b>
      </p>

      <!-- body  -->
      <div id="edit-job-form" class="row gap-4">
        <TheJobFormMainContent 
          :initial-package-id="initialPackageId"
          :is-create="false"
          :job-data="jobData"
        />
        <ThePopupPublicLoading
          v-if="showPublicLoading"
          :jobId="createdJobId"
          @done="() => {
          showPublicLoading = false;
          router.push({ name: 'jobs' });
        }"
        />

        <!-- Upgrade to premium banner -->
        <!-- <div class="col-12">
          <img src="/assets/images/upgrade-premium.svg" class="w-100" />
        </div> -->
        <!-- ./Upgrade to premium banner -->
      </div>

      <!-- action  -->
      <div class="row">
        <div class="col-12">
          <div
            class="d-flex justify-content-between align-items-center my-5 p-4 bg-white rounded-2"
          >
            <div>
              <p
                v-html="
                  translate('job_form_note_you_must_fill_in_the_required')
                "
              ></p>
            </div>

            <div class="d-flex justify-content-end align-items-center gap-4">
              <button
                class="btn btn-link btn-md"
                type="button"
                v-if="jobStatus === 'Draft'"
                @click="onSaveDraft"
              >
                Save Draft
              </button>

              <button
                v-if="formValue.level == 'paid'"
                class="btn btn-topdev-2 btn-md btn-outline"
                type="button"
                @click="onPublic"
              >
                <span>Publish</span>
              </button>

              <button class="btn btn-topdev-1 btn-md" type="submit">
                <span v-if="formValue.level == 'paid'">Review and Publish</span>
                <span v-else>Save</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- <div
        v-if="enve == 'local'"
        style="
          position: fixed;
          top: 0;
          left: 0;
          width: 300px;
          height: 300px;
          z-index: 99999999;
          overflow: scroll;
          background: #fff;
          padding: 10px;
        "
      >
        <pre>{{ errors }}</pre>
        <pre>{{ formValue }}</pre>
      </div> -->
    </form>
    <div v-else>
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </TheJobsLayout>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import DebugPanel from "@/components/DebugPanel.vue";
import { useRoute, useRouter } from "vue-router";
import { useForm } from "vee-validate";

import TheJobsLayout from "@/components/TheJobsLayout.vue";
import TheJobFormMainContent from "@/components/TheJobFormMainContent.vue";

import { useTaxonomiesStore, useAuthStore, useLayoutStore } from "@/stores";
import { updateJob, fetchJobDetail, createJob } from "@/api/job";
import * as yup from "yup";

import { jobFormSchemaObject } from "@/schemas/job-form";
import {
  confirmCancelForm,
  showSuccesToast,
  showWarningToast,
  scrollToFirstValidationError,
  translate,
} from "@/helpers";
import ThePopupPublicLoading from "@/components/ThePopupPublicLoading.vue";

// Define dev mode
const enve = process.env.MIX_APP_ENV;
const isDevMode = enve === 'local' || window.location.hostname === 'localhost';

// Define store
const taxonomiesStore = useTaxonomiesStore();
const authStore = useAuthStore();
const layoutStore = useLayoutStore();

//Route
const route = useRoute();
const router = useRouter();

//Define data
const isEditJobLoading = ref(true);
const jobId = ref();
const jobStatus = ref();
const initialPackageId = ref<string | null>(null);
const jobData = ref<any>(null);

// Define form
const {
  values: formValue,
  meta: formMeta,
  handleSubmit,
  resetForm,
  handleReset,
  isSubmitting,
  setFieldError,
} = useForm({
  validationSchema: computed(() =>
    yup.object({
      ...jobFormSchemaObject,
      package_id: yup
        .string()
        .typeError(translate("job_detail_package_select"))
        .when("job_status_id", {
          is: 2, // Review
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable(),
        })
        .label(translate("job_detail_package_select")),
    })
  ),
});

const showPublicLoading = ref(false);
const createdJobId = ref<number | null>(null);

//Life cycle
onMounted(async () => {
  const id: number = parseInt(route.params.id.toString());

  if (!id) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
    await router.push({ name: "jobs" });

    return;
  }

  layoutStore.blockPage();
  await taxonomiesStore.getTaxonomies();
  jobId.value = id;
  // //set default data
  try {
    const { data } = await fetchJobDetail(id);

    jobStatus.value = data.status;
    jobData.value = data;
    
    // Set the initial package ID based on the job's level and package_id
    initialPackageId.value = data.level === 'free' ? 'free' : (data.package_id?.toString() || null);
    
    resetForm({
      values: {
        level: data.level,
        title: data.title,
        content: data.content,
        requirements: data.requirements,
        responsibilities: data.responsibilities,
        addresses_id: data.addresses_id,
        salary: data.salary,
        experiences_ids: {
          from: data.experiences_ids[0],
          to: data.experiences_ids[1],
        },
        contract_type: data.contract_type_id,
        job_levels: data.job_levels,
        job_types: data.job_types,
        skills_ids: data.skills_ids,
        recruiment_process: data.recruiment_process,
        benefits: data.benefits,
        emails_cc: data.emails_cc,
        note: data.note,
        package_id: data.package_id,
        job_status_id: data.status_id,

        education_degree: data.education_degree_id,
        education_major: data.education_major_id,
        education_certificate: data.education_certificate,

        job_banner: data.job_banner_id,
        job_template: data.job_template_id,
        job_template_color: data.job_template_color_id,
        category_id: data.category_id,
        job_category_id: data.job_category_id,
      },
    });
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }
  layoutStore.unBlockPage();
  isEditJobLoading.value = false;
});

//Function
const onCancelButtonClick = async () => {
  if (formMeta.value.dirty) {
    const confirm = await confirmCancelForm();

    if (confirm.isConfirmed) {
      handleReset();

      await router.push({ name: "jobs" });
    }
    return;
  }
  await router.push({ name: "jobs" });
};

const onSubmit = handleSubmit(
  async (values) => {
    layoutStore.blockPage();

    try {
      let newExperiences = {
        experiences_ids: values.experiences_ids.to
          ? [values.experiences_ids.from, values.experiences_ids.to]
          : [values.experiences_ids.from],
      };
      if (
        Number(values.experiences_ids.from) ===
        Number(process.env.MIX_EXPERIENCE_FROM_ALL_ID)
      ) {
        newExperiences = {
          experiences_ids: [values.experiences_ids.from],
        };
      }

      // Preserve the original level for free jobs
      const isFreeJob = jobData.value?.level === 'free';
      const newValues = {
        ...values,
        ...newExperiences,
        // Force the level to 'free' if it was originally a free job
        level: isFreeJob ? 'free' : values.level,
      };

      // Update job
      const { data: job } = (await updateJob(jobId.value, newValues)) as any;

      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );

      // 🔥 Set jobId & show popup
      if (formValue.level == "paid") {
        createdJobId.value = job.id;
        showPublicLoading.value = true;
      } else {
        await router.push({ name: "jobs" });
      }
    } catch (e) {
      // If found error by validation, should show it in the revelation fields
      if (e?.response?.status === 422) {
        const displayMessageFields = ["package_id"];
        const errors = e.response.data.errors;
        for (const key in errors) {
          if (displayMessageFields.includes(key)) {
            setFieldError(
              key as any,
              errors[key].map((val: string) => translate(val))
            );
          }
        }
        scrollToFirstValidationError();
      }
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } finally {
      layoutStore.unBlockPage();
    }
  },
  (errors) => {
    scrollToFirstValidationError();
    showWarningToast(
      translate("toast_errors"),
      translate("toast_fill_all_information message")
    );
  }
);

const onPublic = async () => {
  layoutStore.blockPage();
  try {
    const newValues = {
      ...formValue,
      on_public: 1,
    };
    await updateJob(jobId.value, newValues);
    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );
    await router.push({ name: "jobs" });
  } catch (e) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    layoutStore.unBlockPage();
  }
};

const onSaveDraft = async () => {
  layoutStore.blockPage();
  try {
    const newValues = {
      ...formValue,
    };
    await updateJob(jobId.value, newValues);
    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );
    await router.push({ name: "jobs" });
  } catch (e) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    layoutStore.unBlockPage();
  }
};
</script>
