<template>
  <div id="credit-management" class="text-gray-600">
    <div class="container-fluid mt-8 mb-6">
      <div class="d-flex justify-content-between align-items-end mb-6">
        <h1 class="text-2xl m-0 p-0">
          {{ translate("layout_credit_management") }}
        </h1>
        <div>
          <v-date-picker
            is-range
            :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
            :masks="{ input: 'DD-MM-YYYY' }"
            v-model="timeRange"
          >
            <template v-slot="{ inputValue, inputEvents }">
              <div class="d-flex justify-center align-items-center">
                <input
                  :value="inputValue.start"
                  v-on="inputEvents.start"
                  class="form-control form-control-solid"
                  :placeholder="translate('candidate_list_form_date')"
                />
                <span class="svg-icon svg-icon-2x">
                  <inline-svg src="/assets/icons/arrow-right.svg" />
                </span>
                <input
                  :value="inputValue.end"
                  v-on="inputEvents.end"
                  class="form-control form-control-solid"
                  :placeholder="translate('candidate_list_to_date')"
                />
              </div>
            </template>
          </v-date-picker>
        </div>
      </div>
      <div class="border-top mb-6" id="credit-package">
        <div
          class="d-flex justify-content-between align-items-center p-4 credit-package-header bg-white"
        >
          <div class="d-flex align-items-center gap-1">
            <inline-svg src="/assets/icons/wallet.svg" />
            <h2 class="m-0 credit-title text-2xl">
              {{ translate("credit_management_credit_package") }}
            </h2>
          </div>
          <button
            @click="onOpenTopUp"
            type="button"
            class="btn btn-danger btn-md d-inline-flex gap-1 align-items-center"
          >
            <inline-svg
              src="/assets/icons/plus-circle.svg"
              width="20"
              height="20"
            />
            <span>
              {{ translate("credit_management_buy_more_credits") }}
            </span>
          </button>
        </div>
        <div class="credit-package-body p-4 pt-8 bg-white">
          <div class="d-flex gap-8">
            <div class="credit-available">
              <h3 class="credit-available-title">
                {{ translate("credit_management_credits_availables") }}
              </h3>
              <div
                class="d-flex align-items-end justify-content-center text-danger"
              >
                <span class="num-of-credit">{{ totalCredit }}</span>
                <span class="credit-icon">
                  <inline-svg
                    width="20"
                    height="20"
                    src="/assets/icons/aside/credit-management.svg"
                  />
                </span>
              </div>
            </div>
            <div class="flex-1">
              <table class="table table-sm package-table m-0">
                <thead>
                  <tr>
                    <th style="width: 40%">
                      {{ translate("credit_management_package_paid") }}
                    </th>
                    <th style="width: 12%">
                      {{ translate("credit_management_status") }}
                    </th>
                    <th style="width: 16%">
                      {{ translate("credit_management_purchased_date") }}
                    </th>
                    <th style="width: 16%">
                      {{ translate("credit_management_activated_date") }}
                    </th>
                    <th
                      style="width: 16%"
                      @click="handleFetchSortPackages('expired_at')"
                    >
                      <div
                        class="d-flex btn-sort gap-1 align-items-center justify-content-start"
                      >
                        <span>
                          {{ translate("credit_management_expired_date") }}
                        </span>
                        <div
                          class="d-flex flex-column align-items-center justify-content-center"
                        >
                          <span
                            class="sort-icon"
                            :class="
                              packageOrder.orderBy === 'asc' ? 'active' : ''
                            "
                          >
                            <inline-svg
                              src="/assets/icons/arrow-up.svg"
                              width="8"
                              height="8"
                            />
                          </span>
                          <span
                            class="sort-icon"
                            :class="
                              packageOrder.orderBy === 'desc' ? 'active' : ''
                            "
                          >
                            <inline-svg
                              src="/assets/icons/arrow-down.svg"
                              width="8"
                              height="8"
                            />
                          </span>
                        </div>
                      </div>
                    </th>
                    <th style="width: 12%">
                      {{ translate("credit_management_action") }}
                    </th>
                  </tr>
                </thead>
                <tbody ref="packageRef">
                  <tr v-for="item in packagesData.data" :key="item.id">
                    <td style="width: 40%" class="font-bold align-self-center">
                      {{ item.search_package.name }}
                    </td>
                    <td
                      style="width: 12%"
                      class="align-self-center"
                      :class="getPackageTypeClass(item.status)"
                    >
                      {{ item.status }}
                    </td>
                    <td style="width: 16%" class="align-self-center">
                      {{ convertDatetime(item.created_at) }}
                    </td>
                    <td style="width: 16%" class="align-self-center">
                      {{ convertDatetime(item.valid_at) }}
                    </td>
                    <td style="width: 16%" class="align-self-center">
                      {{ convertDatetime(item.expired_at) }}
                    </td>
                    <td style="width: 12%" class="align-self-center">
                      <ActiveUnlockPackagePopover
                        :item="{ id: item.id, status: item.status }"
                        @active-unlock-package="onHandleActivePackages"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div id="usage-log">
        <div class="usage-log-header">
          <h3 class="d-flex align-items-center gap-1 mb-4">
            <inline-svg
              src="/assets/icons/clock-nine.svg"
              width="20"
              height="20"
            />
            <span>
              {{ translate("credit_management_usage_log") }}
            </span>
          </h3>
        </div>
        <div class="usage-log-body" ref="usageLogRef">
          <table class="table table-sm table-hover">
            <tbody>
              <tr v-for="item in usagesData.data" :key="item.id">
                <td style="width: 25%">
                  <span class="text-black font-bold">
                    {{ convertDatetime(item.created_at, "DD-MM-YYYY") }}
                  </span>
                  {{ convertDatetime(item.created_at, "hh:mm:ss") }}
                </td>
                <td
                  style="width: 50%"
                  v-html="
                    getUsageDescription(
                      item.type,
                      item.created_by_name,
                      item.candidate_name
                    )
                  "
                ></td>
                <td
                  :class="getCreditFormatByType(item.type).class"
                  style="width: 25%"
                >
                  {{ getCreditFormatByType(item.type).text }}
                  {{ item.credit ?? "" }}
                  {{
                    item.credit
                      ? item.credit > 1
                        ? translate("credit_management_credits")
                        : translate("credit_management_credit")
                      : ""
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  activateUnlockPackage,
  fetchCompanyInfo,
  fetchCreditHistory,
} from "@/api/company";
import { showSuccesToast, showWarningToast, translate } from "@/helpers";
import { useLayoutStore } from "@/stores";
import {
  CreditParamsType,
  PackageSortType,
  PackageType,
  UsageType,
  TransactionType,
} from "@/models/company";
import { Modal } from "bootstrap";
import moment from "moment";
import { onMounted, ref, watch } from "vue";
import { useCompanyStore } from "@/stores";
import ActiveUnlockPackagePopover from "@/components/ActiveUnlockPackagePopover.vue";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import { useRouter } from "vue-router";

const timeRange = ref(null);
const layoutStore = useLayoutStore();
const packageRef = ref<HTMLDivElement>(null);
const totalCredit = ref<number>(0);
const packagesData = ref<PackageType>(null);
const usagesData = ref<UsageType>(null);
const isLoadingData = ref<boolean>(false);
const packageOrder = ref<{
  orderBy: "asc" | "desc";
  orderByField: PackageSortType;
}>({
  orderBy: "desc",
  orderByField: "expired_at",
});
const creditFilterParams = ref<Partial<CreditParamsType>>({
  page_package: 1,
  per_page_package: 5,
  package_order_by: packageOrder.value.orderBy,
  package_order_by_field: packageOrder.value.orderByField,
  page_usage: 1,
  per_page_usage: 10,
});
const usageLogRef = ref<HTMLDivElement>(null);
const companyStore = useCompanyStore();
// Fetch credit history
const router = useRouter();

onMounted(async () => {
  await fetchCompanyInfo().then(({ data }) => {
    if (data.status == COMPANY_STATUS_REVIEW) {
      showWarningToast(
        "",
        "You are not granted access to this feature. Please complete your company profile"
      );

      router.push("/company/profile");
    }
  });

  layoutStore.setPageTitle("layout_credit_management");
  await fetchCreditHistoryData(creditFilterParams.value).then(
    ({ packages, total_credit, usages }) => {
      packagesData.value = packages;
      totalCredit.value = total_credit;
      usagesData.value = usages;
      companyStore.updateTotalCredit(total_credit);
    }
  );
  useCompanyStore;
  handleLoadMoreUsageLog();
  handleLoadMorePackage();
});

const handleLoadMorePackage = () => {
  if (packageRef.value) {
    const element = packageRef.value;
    packageRef.value.addEventListener("scroll", async () => {
      if (
        element.scrollHeight - element.scrollTop >= element.clientHeight &&
        packagesData.value.current_page < packagesData.value.last_page &&
        !isLoadingData.value
      ) {
        // is scroll to bottom && not last page && not loading
        creditFilterParams.value = {
          ...creditFilterParams.value,
          page_package: packagesData.value.current_page + 1,
        };
        isLoadingData.value = true;
        const { packages } = await fetchCreditHistoryData(
          creditFilterParams.value
        );
        packagesData.value = {
          ...packages,
          data: [...packagesData.value.data, ...packages.data],
        };
        isLoadingData.value = false;
      }
    });
  }
};

const onHandleActivePackages = async (item) => {
  try {
    await activateUnlockPackage({ company_search_package_id: item.id }).then(
      ({ data }) => {
        // Success toast
        showSuccesToast(
          translate("toast_congrats"),
          translate(
            "credit_management_activate_inactive_unlock_package_success"
          )
        );
      }
    );
  } catch (error) {
    showWarningToast(
      translate("toast_something_went_wrong"),
      translate("toast_please_try_again_message")
    );
  } finally {
    // load data
    creditFilterParams.value = {
      page_package: 1,
      per_page_package: 5,
      package_order_by: packageOrder.value.orderBy,
      package_order_by_field: packageOrder.value.orderByField,
      page_usage: 1,
      per_page_usage: 10,
    };

    await fetchCreditHistoryData(creditFilterParams.value).then(
      ({ packages, total_credit, usages }) => {
        packagesData.value = packages;
        totalCredit.value = total_credit;
        usagesData.value = usages;
        companyStore.updateTotalCredit(total_credit);
      }
    );
  }
};

const handleLoadMoreUsageLog = () => {
  if (usageLogRef.value) {
    const element = usageLogRef.value;
    usageLogRef.value.addEventListener("scroll", async () => {
      if (
        element.scrollHeight - element.scrollTop >= element.clientHeight &&
        usagesData.value.current_page < usagesData.value.last_page &&
        !isLoadingData.value
      ) {
        // is scroll to bottom && not last page && not loading
        isLoadingData.value = true;
        creditFilterParams.value = {
          ...creditFilterParams.value,
          page_usage: usagesData.value.current_page + 1,
        };
        const { usages } = await fetchCreditHistoryData(
          creditFilterParams.value
        );
        usagesData.value = {
          ...usages,
          data: [...usagesData.value.data, ...usages.data],
        };
        isLoadingData.value = false;
      }
    });
  }
};

/**
 * Opens the top-up modal for credits usage.
 *
 * @return {void} No return value.
 */
const onOpenTopUp = () => {
  Modal.getOrCreateInstance("#modal-credits-usage-topup").show();
};

/**
 * Converts a given datetime string to the specified format using moment.js.
 *
 * @param {string} date - The datetime string to be converted.
 * @param {string} [format="DD/MM/YYYY"] - The format to which the datetime string should be converted. Defaults to "DD/MM/YYYY".
 * @return {string} The converted datetime string.
 */
const convertDatetime = (
  date?: string,
  format: string = "DD/MM/YYYY"
): string => {
  return date ? moment(date).format(format) : "Unknown";
};

/**
 * Returns the credit format based on the given type.
 *
 * @param {string} type - The type of credit (In, Out, Refund).
 * @returns {object} - An object with class and text properties representing the credit format.
 */
const getCreditFormatByType = (
  type: TransactionType
): { class: string; text: string } => {
  switch (type) {
    case "In":
    case "Refund":
      return {
        class: "credit-plus",
        text: "+",
      };
    case "Out":
      return {
        class: "credit-subtract",
        text: "-",
      };
    case "Expired": {
      return {
        class: "",
        text: "-",
      };
    }
    default:
      return {
        class: "",
        text: "",
      };
  }
};

/**
 * Returns the CSS class corresponding to the package type.
 *
 * @param {string} type - The type of the package ("Expired", "Active", or "Inactive").
 * @return {string} The CSS class corresponding to the package type.
 */
const getPackageTypeClass = (
  type: "Expired" | "Active" | "Inactive"
): string => {
  switch (type) {
    case "Expired": {
      return "package-expired";
    }
    case "Active": {
      return "package-active";
    }
    case "Inactive": {
      return "package-inactive";
    }
    default:
      return "";
  }
};

/**
 * Fetches credit history data.
 *
 * @param {Partial<CreditParamsType>} params - Optional parameters for credit history.
 * @return {Promise<void>} A promise that resolves to void.
 */
const fetchCreditHistoryData = async (params?: Partial<CreditParamsType>) => {
  const response = await fetchCreditHistory({ ...params });
  const { packages, total_credit, usages } = (await response.data) as {
    packages: PackageType;
    total_credit: number;
    usages: UsageType;
  };

  return {
    packages,
    total_credit,
    usages,
  };
};

/**
 * Handles the fetch and sorting of data by a specified column.
 *
 * @param {PackageSortType} type - The type of sorting to be applied.
 * @return {Promise<void>} - A promise that resolves when the fetch and sorting are completed.
 */
const handleFetchSortPackages = async (
  type: PackageSortType
): Promise<void> => {
  packageRef.value.scrollTop = 0;
  switch (type) {
    case "expired_at": {
      packageOrder.value = {
        ...packageOrder.value,
        orderBy: packageOrder.value.orderBy === "asc" ? "desc" : "asc",
        orderByField: "expired_at",
      };
      break;
    }
    default:
      break;
  }
  creditFilterParams.value = {
    ...creditFilterParams.value,
    page_package: 1,
    package_order_by: packageOrder.value.orderBy,
    package_order_by_field: packageOrder.value.orderByField,
  };
  const { packages } = await fetchCreditHistoryData(creditFilterParams.value);
  packagesData.value = packages;
};

const getUsageDescription = (
  type: TransactionType,
  createdByName: string = null,
  candidateName: string = null
) => {
  switch (type) {
    case "In": {
      return translate("credit_management_granted_credits_description");
    }
    case "Out": {
      return translate("credit_management_unlocked_condidate_description", [
        createdByName ?? "",
        candidateName ?? "",
      ]);
    }
    case "Refund": {
      return translate("credit_management_refunded_credits_description");
    }
    case "Expired": {
      return translate("credit_management_your_company_has_expired_unlock");
    }
    default:
      return null;
  }
};

watch(timeRange, async (newValue, oldValue) => {
  creditFilterParams.value = {
    ...creditFilterParams.value,
    page_package: 1,
    page_usage: 1,
    from_date: newValue ? newValue.start : null,
    to_date: newValue ? newValue.end : null,
  };
  await fetchCreditHistoryData({
    ...creditFilterParams.value,
  }).then(({ packages, usages }) => {
    packagesData.value = packages;
    usagesData.value = usages;
  });
});
</script>
