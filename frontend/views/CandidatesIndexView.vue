<template>
  <div class="container-fluid bg-white">
    <div v-if="isCandidatesLoading" class="py-4 pt-5">
      <TheCandidatesSearchWidget isAllCandidates :isDetail="true"/>

      <!-- Tabs -->
      <div class="box-nav line-tabs-candidates">
        <div class="d-flex pt-5 align-items-center justify-content-between">
          <div class="d-flex align-items-center nav-tabs flex-1">
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 1 }"
              type="button"
              @click="() => activeTab('bestMatch')"
              :title="translate('candidate_tab_best_match')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_best_match") }}
          </span>
            </button>
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 0 }"
              type="button"
              @click="() => activeTab('other')"
              :title="translate('candidate_tab_other')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_other") }}
          </span>
            </button>
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == null }"
              type="button"
              @click="() => activeTab('all')"
              :title="translate('candidate_tab_all')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_all") }}
          </span>
            </button>
          </div>
        </div>
      </div>

      <!-- List candidates  -->
      <div class="table-responsive">
        <table class="table position-relative">
          <thead>
          <tr class="fw-bold fs-6 text-gray-800 border-bottom border-top">
            <th>{{ translate("candidate_list_no") }}</th>
            <th>{{ translate("candidate_list_candidate_information") }}</th>
            <th class="min-w-550px">{{ translate("search_resumes_candidate_experience") }}</th>
            <th>{{ translate("candidate_list_job_title") }}</th>
            <th class="text-center min-w-200px">
              {{ translate("candidate_list_action") }}
            </th>
          </tr>
          </thead>
          <tbody v-if="candidates.length > 0" id="table-body">
          <AppLoader />
          <tr
            class="border-bottom"
            v-for="(candidate, index) in candidates"
            :key="index"
          >
            <td>
              {{ index + noPrefix }}
            </td>
            <!-- Candidate information  -->
            <td
              @click="toDetailCandidates(candidate.id)"
              class="flex-grow-1 mw-50 text-nowrap cursor-pointer"
            >
              <div v-if="candidate.is_resume_exists">
                <p class="fw-bolder text-blue-dark my-1">
                  {{ candidate.full_name }}
                </p>
                <div class="d-flex ">
                  <span class="badge custom-application-status not-match" v-if="candidate.recalled_at ||
                      candidate.is_remove_cv ||
                      !candidate.is_resume_exists || candidate.applied_status === 'Recall'"
                  >
                    {{
                      candidate.recalled_at ||
                      candidate.is_remove_cv ||
                      !candidate.is_resume_exists
                        ? "Recall"
                        : `${candidate.applied_status}*`
                    }}
                  </span>
                  <div
                    class="best-match"
                    :class="[
                      candidate.matching_status === 0 && 'd-none',
                    ]"
                  >
                    <div class="badge">
                      <span class="text-match">Best Match</span>
                      <i class="text-white">
                        <inline-svg
                          width="16"
                          height="16"
                          src="assets/icons/candidates/sparkles.svg"
                        />
                      </i>
                    </div>
                  </div>
                </div>
                <div class="mt-1">
                  <p class="m-0">{{ candidate.email }}</p>
                  <p class="m-0">{{ candidate.phone }}</p>
                  <p class="m-0">{{ candidate.location }}</p>
                </div>
                <div class="mt-3">
                  <p class="m-0 fw-boldest"> {{ translate("candidate_list_applied_date") }}</p>
                  <p class="m-0" v-html="(candidate.applied_at)"></p>
                </div>
                <div class="mt-3" :class="[
                    candidate.cover_letter
                      ? ''
                      : 'd-none'
                  ]">
                  <i>
                    <inline-svg
                      width="20"
                      height="20"
                      src="assets/icons/candidates/icon-check.svg"
                    />
                  </i>
                  <span class="m-lg-1">{{ translate("candidate_list_cover_letter") }}</span>
                </div>
              </div>
              <div v-else>
                <p class="fw-bold my-1">
                  {{ translate("candidate_detail_user_has_been_disabled") }}
                </p>
              </div>
            </td>
            <!-- Candidate experience  -->
            <td class="min-w-550px mw-550px space-y-2 candidate-experience-container">
              <TheCandidateExperienceInformation
                :candidate="candidate"
              />
            </td>
            <!-- Job title  -->
            <td>
              <p
                @click="
                    navigationToJobDetailPage(candidate.job_detail_url, $event)
                  "
                class="text-primary my-1 cursor"
              >
                {{ candidate.job_title }}
              </p>
            </td>
            <!-- Action  -->
            <td class="text-center text-nowrap">
              <div class="d-flex gap-2 justify-content-center">
                <!-- Seen action -->
                <span
                  @click="toDetailCandidates(candidate.id)"
                  class="btn btn-sm btn-action py-0 px-0"
                >
                  <inline-svg
                    v-if="!!candidate.read_status"
                    width="24"
                    height="24"
                    src="assets/icons/candidates/seen.svg"
                  />
                  <inline-svg
                    v-else
                    width="24"
                    height="24"
                    src="assets/icons/candidates/not-seen.svg"
                  />
                </span>
                <!-- note action -->
                <div>
                  <button
                    v-if="candidate.is_resume_exists"
                    type="button"
                    class="btn btn-sm p-0 btn-action"
                    :class="{ 'note-icon': !!candidate.employer_note }"
                    data-bs-toggle="tooltip"
                    data-bs-placement="left"
                    :title="candidate.employer_note"
                  >
                    <inline-svg src="assets/icons/candidates/note.svg" />
                  </button>
                  <button
                    v-else
                    type="button"
                    @click="(event) => event.stopPropagation()"
                    class="btn btn-sm p-0 btn-disabled btn-action"
                    :class="{ 'note-icon': !!candidate.employer_note }"
                    data-bs-toggle="tooltip"
                    data-bs-placement="left"
                    :title="candidate.employer_note"
                  >
                    <inline-svg
                      src="assets/icons/candidates/disabled-note.svg"
                    />
                  </button>
                </div>
                <!-- download action -->
                <div>
                  <button
                    v-if="
                        candidate.recalled_at ||
                        candidate.is_remove_cv ||
                        !candidate.is_resume_exists
                      "
                    type="button"
                    class="btn btn-sm btn-disabled btn-action p-0"
                    @click="
                        (event) => {
                          event.stopPropagation();
                        }
                      "
                  >
                    <inline-svg
                      src="assets/icons/candidates/disabled-download.svg"
                    />
                  </button>
                  <button
                    v-else
                    type="button"
                    class="btn btn-sm btn-action p-0"
                    @click="downloadCV(candidate.id, $event)"
                  >
                    <inline-svg src="assets/icons/candidates/download.svg" />
                  </button>
                </div>
              </div>
              <TheSelectBoxStatusCandidates
                :candidate="candidate"
                :idChange="idChange"
                :getDataStatus="getDataStatus"
                :isDetailCandidates="false"
                @openDropList="openDropList"
              />
            </td>
          </tr>
          </tbody>

          <!-- If jobs empty -->
          <tbody v-else id="table-body">
          <tr>
            <td colspan="6" class="text-center">
              <inline-svg
                src="/assets/icons/candidates/no-search-result.svg"
                class="empty-icon"
              ></inline-svg>
              <p style="font-size: 16px">
                {{
                  translate("candidate_list_you_currently_have_no_candidates")
                }}
              </p>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <!-- End list candidates  -->
    </div>
    <!-- Skeletor  -->
    <div v-else class="py-5">
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </div>

  <!-- Pagination  -->
  <div
    class="container-fluid bg-white"
    v-if="isCandidatesLoading && candidates.length"
  >
    <div class="pb-5 border-gray-500">
      <AppPagination :meta="candidatesMeta" @setPagination="setPagination" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import TheSelectBoxStatusCandidates from "@/components/TheSelectBoxStatusCandidates.vue";

import { fetchCandidateCV, fetchCandidates } from "@/api/candidate";
import { useCandidatesStore, useLayoutStore } from "@/stores";

import { fetchCompanyInfo } from "@/api/company";
import AppLoader from "@/components/AppLoader.vue";
import AppPagination from "@/components/AppPagination.vue";
import TheCandidateExperienceInformation from "@/components/TheCandidateExperienceInformation.vue";
import TheCandidatesSearchWidget from "@/components/TheCandidatesSearchWidget.vue";
import { scrollToTop, showWarningToast, translate } from "@/helpers";
import { Candidate } from "@/models/candidates";
import { Meta } from "@/models/jobs";
import { InlineSvg } from "@/plugins";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";

//Define store
const layoutStore = useLayoutStore();
const candidatesStore = useCandidatesStore();

//Define router
const router = useRouter();
const route = useRoute();

//Define data
const isCandidatesLoading = ref(false);
const candidates = ref<Candidate[]>([]);
const candidatesMeta = ref<Meta>();
const idChange = ref(0);
const getDataStatus = ref([]);

const breakLine = (text: string) => {
  if (!text) {
    return "";
  }

  return text.replace(" ", "<br>");
};
const toDetailCandidates = (id: number) => {
  const validTabs = ['bestMatch', 'other', 'all'];
  const tabParam = validTabs.includes(route.query.tab as string) ? route.query.tab : 'bestMatch';
  router.push(`/candidates/${id}?tab=${tabParam}`);
};

const downloadCV = (id: number, event) => {
  event.stopPropagation();
  fetchCandidateCV(id)
    .then((response) => {
      window.location.href = response.data.download_url;
    })
    .catch(() => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    });
};

const getAndSetSeeMore = async () => {
  const items = (await document.getElementsByClassName(
    "box_search_resumes_work_experience_education"
  )) as HTMLCollectionOf<HTMLElement>;
  for (let i = 0; i < items.length; i++) {
    let element = items[i];
    let height = element.clientHeight;
    if (height > 160) {
      element.classList.add("fix");
      element.lastElementChild.classList.add("d-inline-flex");
    } else {
      element.classList.add("no-fix");
      element.lastElementChild.classList.add("d-none");
    }
  }
};

const navigationToJobDetailPage = (detailJobUrl, event) => {
  event.stopPropagation();
  window.open(detailJobUrl);
};

const setPagination = (value) => {
  candidatesStore.setCandidatesParams(value);
};

const openDropList = (id: number, event: any) => {
  event.stopPropagation();
  idChange.value = id;
};
const closeDropList = () => {
  idChange.value = 0;
};
const initializeTabState = (tabActive: string = null) => {
  let tab = route.query.tab as string;
  let page = candidatesStore.candidatesParams.page;
  let page_size = candidatesStore.candidatesParams.page_size;
  if (tabActive) {
    tab = tabActive;
    page = 1;
    page_size = 30;
  }
  let matching_status: number | null; // Explicitly type matching_status

  switch (tab) {
    case 'bestMatch':
      matching_status = 1;
      break;
    case 'other':
      matching_status = 0;
      break;
    case 'all':
      matching_status = null;
      break;
    default:
      matching_status = 1;
  }

  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, matching_status},
    page: page,
    page_size: page_size
  });
}
const addQueryTab = (tabActive: string) => {
  const { path, query } = route;
  router.replace({
    path,
    query: { ...query, tab: tabActive }
  });
}
const activeTab = (tabActive: string) => {
  addQueryTab(tabActive)
  initializeTabState(tabActive);
}
//Life cycle
onMounted(async () => {
  layoutStore.setPageTitle("layout_manage_candidates");

  fetchCompanyInfo()
    .then(({data}) => {
      if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          router.push('/company/profile');
        }
      });

  //Fetch Candidates
  try {
    const { data, meta }: any = await fetchCandidates(
      candidatesStore.candidatesParamsRequest
    );
    candidates.value = data;
    candidatesMeta.value = meta;
    initializeTabState()
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isCandidatesLoading.value = true;
    await getAndSetSeeMore();
  }

  window.onclick = function() {
    if (idChange.value > 0) closeDropList();
  };
});
// Watch for changes to candidatesParams
watch(
  () => candidatesStore.candidatesParams,
  async () => {
    const tableBody = document.getElementById("table-body");
    tableBody.className = "page-loading";
    try {
      const { data, meta }: any = await fetchCandidates(
        candidatesStore.candidatesParamsRequest
      );
      candidates.value = data;
      candidatesMeta.value = meta;
    } catch (err) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
      throw err;
    } finally {
      tableBody.classList.remove("page-loading");
      await getAndSetSeeMore();
      scrollToTop();
    }
  },
  { deep: true }
);

const noPrefix = computed(() => {
  return (
    1 +
    (candidatesMeta.value.current_page - 1) *
    parseInt(candidatesMeta.value.per_page)
  );
});

watch(
  () => candidatesStore.getValueStatusCandidates,
  (data) => {
    if (
      JSON.stringify(getDataStatus.value) ===
      JSON.stringify(candidatesStore.getValueStatusCandidates)
    )
      return;
    getDataStatus.value = data;
  },
  { deep: true }
);
</script>
