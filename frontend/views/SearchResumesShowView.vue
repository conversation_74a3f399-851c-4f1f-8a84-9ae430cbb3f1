<template>
  <div class="container-fluid detail-search-resumes bg-gray" id="search-resumes-show">
    <div>
      <div class="row py-0 height-85vh">
        <!-- Left side -->
        <div
          class="col-3 border-end border-gray-400 pt-4 height-85vh overflow-auto"
        >
          <TheSearchCandidatesSearchWidget
            :isShowFull="false"
            :hasFilter="true"
            :hasSaveCandidate="false"
          />
          <!-- List candidates -->
          <div
            v-show="!isDataLoading"
            class="table-responsive mh-70vh pb-0"
            id="search-resumes-detail-table"
          >
            <table
              class="table position-relative table-detail-candidate bg-white"
            >
              <thead>
                <tr class="fw-bold fs-6 text-gray-800 border-bottom border-top">
                  <th class="ps-4">{{ translate("candidate_list_no") }}</th>
                  <th>
                    {{ translate("candidate_list_candidate_information") }}
                  </th>
                </tr>
              </thead>
              <tbody class="page-loading position-relative">
                <tr
                  v-for="(resume, index) in resumes"
                  :key="resume.id"
                  class="border-bottom cursor-pointer"
                  :class="{
                    'candidate-active': resume.id === resumeDetail?.id,
                  }"
                  :id="`candidate-${resume.id}`"
                  @click="changeResumeDetail(index, resume.id)"
                >
                  <!-- No -->
                  <td class="ps-4">
                    <p class="mb-1">{{ index + noPrefix }}</p>
                    <inline-svg
                      v-if="resume.is_saved"
                      class="saved-candidate-icon"
                      width="20"
                      height="20"
                      src="/assets/icons/bookmark-fill.svg"
                    />
                  </td>
                  <!-- Candidate information -->
                  <td>
                    <TheSearchResumeCandidateInformationContainer
                      :resume="resume"
                      :is-resume-detail-page="true"
                    />
                    <!-- <p
                        class="fw-bold mb-1"
                        :class="{
                          'text-primary': resume.is_unlocked,
                        }"
                      >
                        {{ resume.fullname }}
                      </p>
                      <p>{{ resume.province }}</p>
                      <p class="mb-6">
                        <b
                          >{{
                            `${translate("search_resumes_current_position")} `
                          }} </b
                        >{{ resume.current_job }}
                      </p>
                      <p v-if="resume.viewed_count > 0">
                        <b>{{ `${translate("search_resumes_views")} ` }}</b>
                        {{ resume.viewed_count }}
                      </p>
                      <p>
                        <b>{{ `${translate("search_resumes_unlocks")} ` }} </b
                        >{{ resume.unlocked_count }}
                      </p>
                      <p>
                        <b
                          >{{
                            `${translate("search_resumes_last_updated")} `
                          }} </b
                        >{{ resume.last_updated_at }}
                      </p> -->
                  </td>
                  <!-- Action -->
                  <!-- <td class="px-2">
                      <inline-svg
                        class="inline-svg-eye"
                        v-if="resume.is_viewed"
                        src="/assets/icons/candidates/seen.svg"
                      />
                    </td> -->
                </tr>
              </tbody>
            </table>
          </div>
          <div v-show="isDataLoading" class="py-5">
            <Skeletor />
            <Skeletor />
            <Skeletor />
          </div>
        </div>
        <!-- Right side -->
        <div
          class="col-9 p-0 height-85vh overflow-auto candidate-detail-container"
        >
          <div>
            <!-- Detail resume -->
            <div class="p-7 candidate-detail-content">
              <!-- Top -->
              <div class="d-flex justify-content-between align-items-start">
                <div>
                  <p class="mb-0 fw-bold">
                    {{ translate("search_resumes_candidate_from") }}
                    <b class="text-blue-dark font-bold">{{
                      translate("search_resumes_topdev_search_candidate")
                    }}</b>
                  </p>
                  <p class="fs-8 mb-1 text-gray-400">
                    {{ `${translate("search_resumes_last_updated")} ` }}
                    {{ resumeDetail ? resumeDetail.last_updated_at : "" }}
                  </p>
                </div>
                <div
                  class="d-flex align-items-center gap-4"
                  v-if="resumeDetail"
                >
                  <span
                    v-if="resumeDetail.is_unlocked"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    class="svg-icon svg-icon-1 cursor"
                    @click="requestRefundHandler"
                    :title="translate('search_resumes_request_refund')"
                  >
                    <inline-svg src="/assets/icons/refund-request.svg" />
                  </span>
                  <span
                    v-if="isUpdatingWishList"
                    class="spinner-border text-warning w-20px h-20px"
                    role="status"
                  ></span>
                  <span
                    v-else
                    class="svg-icon svg-icon-1 cursor"
                    @click="
                      onUpdateWishList(resumeDetail.id, resumeDetail.is_saved)
                    "
                  >
                    <inline-svg
                      :class="{
                        'saved-candidate-icon': resumeDetail.is_saved,
                      }"
                      class="w-6 h-6 text-black"
                      :src="
                        resumeDetail.is_saved
                          ? '/assets/icons/bookmark-fill.svg'
                          : '/assets/icons/bookmark.svg'
                      "
                    />
                  </span>
                  <button
                    type="button"
                    class="svg-icon cursor btn-redirect"
                    @click="moveToPrevResume"
                  >
                    <inline-svg
                      src="/assets/icons/candidates/pagination-prev.svg"
                    />
                  </button>
                  <button
                    type="button"
                    class="svg-icon cursor btn-redirect"
                    @click="moveToNextResume"
                  >
                    <inline-svg
                      src="/assets/icons/candidates/pagination-next.svg"
                    />
                  </button>
                  <router-link
                    :to="{ name: 'search-candidates' }"
                    class="svg-icon ms-3 close-icon"
                  >
                    <inline-svg src="/assets/icons/cancel.svg" />
                  </router-link>
                </div>
              </div>
              <div class="py-5" v-show="isResumeDetailLoading">
                <Skeletor />
                <Skeletor />
                <Skeletor />
              </div>
              <div v-if="!isResumeDetailLoading && resumeDetail">
                <hr class="my-5" />
                <!-- Candidate information -->
                <div class="mt-5 d-flex justify-content-between">
                  <!-- Left side -->
                  <div class="d-flex flex-column justify-content-between">
                    <!-- Full name -->
                    <h6
                      class="mb-1 fw-bold d-flex align-items-center gap-1 text-blue-dark"
                      v-if="resumeDetail.fullname"
                    >
                      {{ resumeDetail.fullname }}
                    </h6>
                    <!-- Current position -->
                    <p class="mb-1" v-if="resumeDetail.current_job">
                      <b>
                        {{ `${translate("search_resumes_current_position")} ` }}
                      </b>
                      {{ resumeDetail.current_job }}
                    </p>
                    <p class="mb" v-if="resumeDetail.expected_salary">
                      <b>Expected salary: </b>
                      {{ resumeDetail.expected_salary }}
                    </p>
                    <!-- Address -->
                    <div class="d-flex align-items-center mt-1">
                      <span class="svg-icon me-2">
                        <inline-svg
                          src="/assets/icons/candidates/location.svg"
                        />
                      </span>
                      <span>
                        {{ resumeDetail.province }}
                      </span>
                    </div>
                    <!-- Mail -->
                    <div class="d-flex align-items-center">
                      <span class="svg-icon me-2">
                        <inline-svg src="/assets/icons/candidates/mail.svg" />
                      </span>
                      <span v-if="resumeDetail.is_unlocked">
                        {{ resumeDetail.email }}
                      </span>
                      <span v-else>
                        {{ translate("candidate_detail_unlock_to_view") }}
                      </span>
                    </div>
                    <!-- Phone -->
                    <div class="d-flex align-items-center mt-1">
                      <span class="svg-icon me-2">
                        <inline-svg src="/assets/icons/candidates/phone.svg" />
                      </span>
                      <span v-if="resumeDetail.is_unlocked">
                        {{ resumeDetail.phone }}
                      </span>
                      <span v-else>
                        {{ translate("candidate_detail_unlock_to_view") }}
                      </span>
                    </div>
                  </div>
                  <!-- Right side -->
                  <div
                    class="position-relative z-10 flex-column d-flex justify-content-start align-items-center gap-2"
                  >
                    <!-- ! Chưa có các status này, sẽ cập nhật sau -->
                    <!-- <div>
                      <StatusActivelySeekingEmployment />
                      <StatusOpenToWork />
                    </div> -->
                    <div class="text-willing-to-work d-flex align-items-center gap-1 fw-bold"
                      v-if="!resumeDetail.willing_to_work && (resumeDetail.is_unlocked || resumeDetail.force_show_willing_to_work_status_tip)">
                      <span class="svg-icon mr-1"><inline-svg src="/assets/icons/yellow-dot.svg" /></span>
                      {{ translate("search_resumes_willing_to_work_status") }}
                    </div>
                    <TheSearchResumePopoverUnlock
                      v-if="!resumeDetail.is_unlocked"
                      :resume="{
                        id: resumeDetail.id,
                        credit: resumeDetail.credit,
                      }"
                      @unlock-resume="sendUnlockResumeRequest"
                      :btn-size="'md'"
                    />
                    <p
                      v-else
                      class="mb-0 btn-reset-custom btn-unlocked-info btn-sm"
                      :title="translate('search_resumes_paid')"
                    >
                      {{ translate("search_resumes_paid") }}
                    </p>
                    <div
                      v-if="resumeDetail.is_unlocked && !resumeDetail.is_expired"
                      class="d-flex align-items-start cursor"
                      @click="searchCandidateDownloadCV(resumeDetail.id, $event, resumeDetail.is_expired)"
                    >
                      <span class="svg-icon svg-icon-2">
                        <inline-svg src="/assets/icons/candidates/download.svg" />
                      </span>
                      <ins class="fw-bold text-success fs-6 ms-2">
                        {{ translate("candidate_detail_download_cv") }}
                      </ins>
                    </div>
                  </div>
                </div>
                <!-- Skills  -->
                <swiper
                  :spaceBetween="5"
                  :slidesPerView="'auto'"
                  class="tag-skill-container mt-5"
                  v-if="
                    resumeDetail.skills &&
                    resumeDetail.skills.length > 0 &&
                    checkTypeSkills(resumeDetail.skills)
                  "
                >
                  <swiper-slide
                    class="tag"
                    v-for="(skill, index) in resumeDetail.skills"
                    :key="index"
                  >
                    {{ skill.skill_name }}
                  </swiper-slide>
                </swiper>
                <div class="mt-4">
                  <!-- Add note -->
                  <TheSearchResumesAddNoteForm
                    :notesData="notesData"
                    :submitAddNote="onCreateMyResumeNotes"
                    :submit-edit-note="submitEditNoteHandler"
                    :isOpenForm="isOpenForm"
                    :open-form="openForm"
                    :cancel-handler="cancelHandler"
                  />
                  <!-- List Notes -->
                  <TheSearchResumesListNotes
                    :isUnlocked="isUnlocked"
                    :resumeDetail="resumeDetail"
                    :notesData="notesData"
                    :onDeleteMyResumeNote="onDeleteMyResumeNote"
                    :submitAddNote="onCreateMyResumeNotes"
                    :submitEditNote="onUpdateMyResumeNotes"
                    :onOpenEditNoteForm="onOpenEditNoteForm"
                    :isOpenForm="isOpenForm"
                    :open-form="openForm"
                    :cancel-handler="cancelHandler"
                  />
                </div>
                <div class="d-flex justify-content-center mt-5">
                  <div
                    class="d-inline-flex gap-4 justify-content-center p-2 align-items-center group-button-choose-type-of-cv"
                    role="group"
                  >
                    <button
                      type="button"
                      class="btn-choose-type-of-cv rounded"
                      :class="{ active: previewCV?.typeOfCV === 'topdev' }"
                      :hidden="resumeDetail?.type == null"
                      :disabled="previewCV.loading"
                      @click="updatePreviewCVType('topdev', resumeDetail?.id)"
                    >
                      {{ translate("search_resumes_topdev_format") }}
                    </button>
                    <button
                      type="button"
                      class="btn-choose-type-of-cv rounded"
                      :class="{ active: previewCV?.typeOfCV === 'original' }"
                      :hidden="
                        resumeDetail?.type == null ||
                        resumeDetail?.type == 'cv_builder' ||
                        resumeDetail?.type == 'topdev_cv'
                      "
                      :disabled="previewCV.loading"
                      @click="updatePreviewCVType('original', resumeDetail.id)"
                    >
                      {{ translate("search_resumes_original") }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <!-- View CV -->
            <div v-show="resumeDetail">
              <div
                v-if="!isErrorLoadingCV"
                class="py-4 bg-gray-400 px-7 relative"
              >
                <div class="preview-cv-container bg-white">
                  <div class="page-loading" v-show="previewCV.loading">
                    <AppLoader />
                  </div>
                  <div v-show="previewCV.original.url && !previewCV.loading">
                    <!-- Show pdf file -->
                    <vue-pdf-embed
                      v-if="cvType === 'pdf'"
                      :source="previewCV.original.url"
                      @loaded="onPreviewCVLoaded"
                      @loading-failed="onPreviewCVLoadingFailed"
                      @rendering-failed="onPreviewCVRenderingFailed"
                    />
                    <!-- Show pdf file -->
                    <!-- Show docs file -->
                    <div v-else class="ratio ratio-1x1">
                      <iframe
                        :src="`https://docs.google.com/gview?embedded=true&url=${previewCV.original.url}`"
                        :title="translate('candidate_detail_view_cv')"
                        allowfullscreen
                        @load="onPreviewCVLoaded"
                      ></iframe>
                    </div>
                    <!-- Show docs file -->
                  </div>
                </div>
              </div>
              <div v-if="!isDataLoading && resumes.length === 0" class="text-center py-5">
                <inline-svg
                  src="/assets/icons/candidates/no-search-result.svg"
                  class="empty-icon"
                />
                <p
                  class="fs-7 text-topdev-2"
                  v-html="translate('search_resumes_could_not_find_candidate')"
                ></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <TheSearchResumesRequestRefundForm
    :resume-detail="resumeDetail"
    :input-value="reasonRefund"
    :on-change-handler="onChangeReasonRefundHandler"
    :on-reset="onResetRequestRefund"
    :on-request-refund-handler="onSubmitRequestRefund"
  />
  <AppPagination
    v-if="resumes.length > 0"
    :meta="resumesMeta"
    @setPagination="onPagination"
  />
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, reactive, ref, watch } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { useRoute, useRouter } from "vue-router";
import router from "@/router";
import VuePdfEmbed from "vue-pdf-embed";

import AppPagination from "@/components/AppPagination.vue";
import AppLoader from "@/components/AppLoader.vue";
import TheSearchCandidatesSearchWidget from "@/components/TheSearchResumesSearchWidget.vue";
import TheSearchResumesAddNoteForm from "@/components/TheSearchResumesAddNoteForm.vue";

import { DEFAULT_PER_PAGE, useLayoutStore, useSearchResumesStore } from "@/stores";
import {
  createMyResumeNotes,
  deleteMyResumeNotes,
  fetchResumeDetail,
  fetchResumes,
  getMyResumeNotes,
  removeWishList,
  unlockResume,
  updateMyResumeNotes,
  updateWishList,
  fetchResumePreviewCV,
  requestRefundCredit,
  checkMaxAllowClickCandidate, fetchSearchCandidateUnlockCV
} from "@/api/search-resume";
import {
  getQueryParam,
  updateQueryParam,
  scrollToTop,
  showSuccesToast,
  showWarningToast,
  translate,
} from "@/helpers";
import { NotesDataState, SearchCandidate } from "@/models/search-resumes";
import TheSearchResumesListNotes from "@/components/TheSearchResumesListNotes.vue";
import { Modal } from "bootstrap";
import TheSearchResumesRequestRefundForm from "@/components/TheSearchResumesRequestRefundForm.vue";
import TheSearchResumeCandidateInformationContainer from "@/components/TheSearchResumeCandidateInformationContainer.vue";
import TheSearchResumePopoverUnlock from "@/components/TheSearchResumePopoverUnlock.vue";
// import StatusActivelySeekingEmployment from "@/components/StatusActivelySeekingEmployment.vue";
// import StatusOpenToWork from "@/components/StatusOpenToWork.vue";
import { updateQueryURL } from "@/helpers/searchCandidate";
import { Meta } from "@/models/jobs";
import { Skeletor } from "vue-skeletor";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";

interface PreviewCV {
  loading: boolean;
  typeOfCV: "original" | "topdev";
  original: {
    mime_type: string;
    url: any;
  };
}

const isDataLoading = ref(true);
const isResumeDetailLoading = ref(true);
const isFilterLoading = ref(false);
const isUpdatingWishList = ref(false);
const isResumesLoadingDownload = ref(false);

const resumes = ref<SearchCandidate[]>([]);
const resumesMeta = ref<Meta>({
  total: 0,
  current_page: 1,
  per_page: DEFAULT_PER_PAGE.toString(),
  from: 1,
  to: 1,
  last_page: 1,
  links: [{ active: true, label: "", url: "" }],
});
const resumeId = ref();
const resumeDetail = ref<SearchCandidate>(null);
const originalData = ref();
const objectDataFileDefine = ref(null);
const typeTabsCV = ref();
const isUnlocked = ref<boolean>(false);
const isReloadPage = ref<boolean>(false);
const reasonRefund = ref<string>("");
const isErrorLoadingCV = ref<boolean>(false);
const isExistResumeData = ref<boolean>(true);
const retryCount = ref<number>(0);

const previewCV = reactive<PreviewCV>({
  loading: true,
  typeOfCV: "original",
  original: {
    url: "",
    mime_type: "pdf",
  },
});
const notesData: NotesDataState = reactive({
  editId: null,
  noteInputValue: "",
  listNotes: [],
  isActionLoading: false,
  isListNotesLoading: false,
});

const route = useRoute();
const searchResumesStore = useSearchResumesStore();
const isEditingNote = ref<boolean>(false);
const isOpenForm = ref<boolean>(false);
const isAllowClick = ref<boolean>(false);
const activeResumeId = ref(<number | undefined>null);
const layoutStore = useLayoutStore();
resumeId.value = route.params?.id;

searchResumesStore.setResumesParams({
  filter: {
    skill: getQueryParam("skill") ? getQueryParam("skill").split(",") : [],
    experience: getQueryParam("experience") ? getQueryParam("experience").split(",") : [],
    location: getQueryParam("location") ?? "",
    language: getQueryParam("language") ?? "",
    candidate_language: getQueryParam("candidate_language") ?? "",
    timeRange: {
      start: getQueryParam("timeRange_start") ?? "",
      end: getQueryParam("timeRange_end") ?? "",
    },
  },
  keyword: getQueryParam("keyword") ? getQueryParam("keyword").split(",") : [],
  showResumesUnlocked: !!+getQueryParam("showResumesUnlocked"),
  showWishList: !!+getQueryParam("showWishList"),
  page: +getQueryParam("page") || 1,
  page_size: +getQueryParam("page_size") || DEFAULT_PER_PAGE,
});

const checkTypeSkills = (skills: Array<object>) => {
  if (!skills) return;
  return skills.filter((skill) => typeof skill === "object")?.length > 0;
};

const noPrefix = computed(() => {
  return (
    1 +
    (resumesMeta.value.current_page - 1) * parseInt(resumesMeta.value.per_page)
  );
});

const cvType = computed(() => {
  return previewCV.original.mime_type;
});

const onPagination = (value) => {
  searchResumesStore.setResumesParams(value);
};

const changeResumeDetail = async (index: number, id: number) => {
  try {
    isAllowClick.value = true;

    if (activeResumeId.value !== id) {
      let {
        data
      }: { data: { is_allow: boolean } } = await checkMaxAllowClickCandidate();

      isAllowClick.value = data.is_allow;

      if (!isAllowClick.value) {
        showWarningToast(
          translate("toast_something_went_wrong"),
          translate("search_resumes_max_allow_click_before")
        );
      }
    }

    resumeDetail.value = resumes.value[index];
    resumeId.value = resumeDetail.value.id;
    // Reset and update preview cv
    resetPreviewCv();

    activeResumeId.value = id;
    await router.push(`/search-candidates/${id}${document.location.search}`);
  } catch (error) {
    showWarningToast(
        "",
        translate("toast_something_went_wrong"),
      );
  }
};

const submitEditNoteHandler = () => {
  onUpdateMyResumeNotes();
  isEditingNote.value = false;
  isOpenForm.value = false;
};

const onOpenEditNoteForm = (id: number) => {
  notesData.editId = id;
  const noteById = notesData.listNotes.find((value) => value.id == id);
  notesData.noteInputValue = noteById.content;

  isEditingNote.value = true;
};

const moveToPrevResume = async () => {
  const index = resumes.value.findIndex(
    (resume) => resume.id === resumeDetail.value.id
  );
  const prevIndex = index - 1;

  //CHECK IF PREV INDEX LESS THAN 0
  if (prevIndex < 0) {
    // CHECK IF CURRENT PAGE EQUAL 0 -> SET CURRENT PAGE = LAST PAGE
    if (searchResumesStore.resumesParams.page === 1)
      searchResumesStore.setResumesParams({
        page: resumesMeta.value.last_page,
      });
    // ELSE CURRENT PAGE IS GOING TO MINUS 1
    else
      searchResumesStore.setResumesParams({
        page: searchResumesStore.resumesParams.page - 1,
      });
  } else {
    // router.push(`/search-candidates/${resumes.value[prevIndex].id}`);
    resumeDetail.value = resumes.value[prevIndex];
  }
  resetPreviewCv();
  scrollToTrActiveElement("prev");
};

const onSubmitRequestRefund = async () => {
  try {
    let resData: any = requestRefundCredit(
      resumeDetail.value.id,
      reasonRefund.value
    );

    if (resData.error) {
      showWarningToast(
        translate("toast_something_went_wrong"),
        translate("toast_request_refund_failed_message")
      );
    } else {
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_request_refund_success_message")
      );
    }
  } catch (error) {
    showWarningToast(
      translate("toast_something_went_wrong"),
      translate("toast_request_refund_failed_message")
    );
  }

  onResetRequestRefund();
};

const onResetRequestRefund = () => {
  reasonRefund.value = "";
  Modal.getOrCreateInstance("#modal-view-request-refund-confirm").hide();
};

const onChangeReasonRefundHandler = (value) => {
  reasonRefund.value = value;
};

const requestRefundHandler = () => {
  reasonRefund.value = "";
  // Manually open
  Modal.getOrCreateInstance("#modal-view-request-refund-confirm").show();
};

const moveToNextResume = async () => {
  const index = resumes.value.findIndex(
    (candidate) => candidate.id === resumeDetail.value.id
  );
  const nextIndex = index + 1;

  // CHECK IF NEXT INDEX GREATER THAN CANDIDATES LENGTH
  if (nextIndex >= resumes.value.length) {
    // CHECK IF CURRENT PAGE EQUAL LAST PAGE -> SET CURRENT PAGE = 1
    if (searchResumesStore.resumesParams.page === resumesMeta.value.last_page)
      searchResumesStore.setResumesParams({ page: 1 });
    // ELSE CURRENT PAGE IS GOING TO PLUS 1
    else
      searchResumesStore.setResumesParams({
        page: searchResumesStore.resumesParams.page + 1,
      });
  } else {
    // router.push(`/search-candidates/${resumes.value[nextIndex].id}`);
    resumeDetail.value = resumes.value[nextIndex];
  }
  resetPreviewCv();
  scrollToTrActiveElement("next");
};

// When u move to next or prev resume -> this function will follow to your current resume
const scrollToTrActiveElement = (type: string) => {
  const container = document.getElementById("search-resumes-detail-table");
  if (container) {
    const activePos = container.getElementsByClassName(
      "candidate-active"
    ) as HTMLCollectionOf<HTMLElement>;
    if (activePos.length > 0) {
      if (type === "next") container.scrollTop = activePos[0].offsetTop;
      if (type === "prev") container.scrollTop = activePos[0].offsetTop - 150;
    }
  }
};

const onPreviewCVRenderingFailed = () => {
  previewCV.loading = false;
}

const onPreviewCVLoadingFailed = () => {
  if (retryCount.value == 0) {
    // retry load pdf file
    retryCount.value = 1;

    setTimeout(() => {
      onUpdatePreviewCv(resumeDetail.value.id);
    }, 1000);
  } else {
    retryCount.value = 0;
    previewCV.loading = false;
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );

    if (previewCV.typeOfCV === 'original') {
      updatePreviewCVType('topdev', resumeDetail.value.id);
    }
  }
}

const onPreviewCVLoaded = () => {
  previewCV.loading = false;
};

const updatePreviewCVType = (type: "original" | "topdev", resumeId) => {
  if (previewCV.loading === true) {
    return;
  }

  previewCV.typeOfCV = type;
  // Check type preview cv = topdev => update cvType = pdf
  if (previewCV.typeOfCV === "topdev") {
    previewCV.original.mime_type = "pdf";
  }
  onUpdatePreviewCv(resumeId);
};

// Reset and update preview cv
const resetPreviewCv = () => {
  try {
    previewCV.original.mime_type = "pdf";
    switch (resumeDetail.value.type) {
      case "upload_cv":
        previewCV.typeOfCV = "original";
        break;
      case "cv_builder":
      case "topdev_cv":
        previewCV.typeOfCV = "topdev";
    }
    isErrorLoadingCV.value = false;
  } catch (error) {
    isErrorLoadingCV.value = true;
  }
};

const updatePreviewCV = (original) => {
  previewCV.original = original;
};

const searchCandidateDownloadCV = (
  resumeId: number,
  event: Event,
  isExpired: boolean = false
) => {
  if (isExpired) {
    event.preventDefault();
    return;
  }

  event.stopPropagation();
  fetchSearchCandidateUnlockCV(resumeId)
    .then((res) => {
      window.location.href = res.data.download_url;
    })
    .catch(() => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    });
};

const onCreateMyResumeNotes = async () => {
  notesData.isActionLoading = true;
  try {
    if (!!notesData.noteInputValue) {
      const noteParam = { content: notesData.noteInputValue };
      const { data } = await createMyResumeNotes(
        resumeDetail.value.id,
        noteParam
      );
      notesData.listNotes.splice(0, 0, data);
      notesData.noteInputValue = "";

      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );

      if (!searchResumesStore.resumesNoted.has(resumeDetail.value.id)) {
        searchResumesStore.addNewResumesNoted(resumeDetail.value.id);
      }
    }
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
    throw err;
  } finally {
    notesData.isActionLoading = false;
  }

  isOpenForm.value = false;
};

const onUpdateMyResumeNotes = async () => {
  notesData.isActionLoading = true;
  try {
    const noteParam = { content: notesData.noteInputValue };
    const result: any = await updateMyResumeNotes(
      resumeDetail.value.id,
      notesData.editId,
      noteParam
    );

    if (!result.error) {
      const index = notesData.listNotes.findIndex(
        (note) => note.id === notesData.editId
      );
      notesData.listNotes[index].content = notesData.noteInputValue;
    }

    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );

    notesData.noteInputValue = "";
    notesData.editId = null;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
    throw err;
  } finally {
    notesData.isActionLoading = false;
  }

  isOpenForm.value = false;
};

const onDeleteMyResumeNote = async (noteId: number) => {
  notesData.isActionLoading = true;
  try {
    await deleteMyResumeNotes(resumeDetail.value.id, noteId);

    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );
    notesData.listNotes = notesData.listNotes.filter(
      (note) => note.id !== noteId
    );
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
    throw err;
  } finally {
    notesData.isActionLoading = false;
  }
};

// const onDownloadResume = async () => {
//   try {
//     const result: any = searchResumesStore.blobResume;
//     // Download
//     const url = window.URL.createObjectURL(result);
//     const link = document.createElement("a");
//     link.href = url;

//     if (result.type === "application/pdf")
//       link.setAttribute("download", `${resumeDetail.value.fullname}.pdf`);
//     if (result.type === "application/msword")
//       link.setAttribute("download", `${resumeDetail.value.fullname}.doc`);
//     document.body.appendChild(link);
//     link.click();
//   } catch (err) {
//     throw err;
//   }
// };

// const onUnlockResume = async (id: number, isUnlocked: boolean = false) => {
//   if (isUnlocked) {
//     return;
//   }

//   let { data } = await checkCreditBeforeUnlockCandidate(id);

//   if (data.is_unlocked) {
//     showWarningToast(
//       translate("toast_something_went_wrong"),
//       translate("search_resumes_resume_unlock_before")
//     );

//     // reload list candidate here
//     isReloadPage.value = true;
//   } else if (data.available && data.available >= resumeDetail.value.credit) {
//     let key = await onCreateUnlockResumeDraft(id, resumeDetail.value.credit);

//     if (key) {
//       const confirm = await confirmUnlockResume(
//         resumeDetail.value.credit,
//         data.available
//       );
//       if (confirm.isConfirmed) {
//         await sendUnlockResumeRequest(id, key);
//       }
//     }

//     isReloadPage.value = true;
//   } else {
//     const confirmSendNoti = await notificationNotEnoughCredits(
//       resumeDetail.value.credit,
//       data.available
//     );
//     if (confirmSendNoti.isConfirmed) {
//       // missing request to send notification
//       let resData: any = fetchSendMeNotification(id);

//       if (resData.error) {
//         showWarningToast(
//           translate("toast_something_went_wrong"),
//           translate("toast_save_failed_message")
//         );
//       } else {
//         showSuccesToast(
//           translate("toast_congrats"),
//           translate("toast_send_me_in_formation")
//         );
//       }
//     }
//     isReloadPage.value = true;
//   }
// };

//If code = 1 -> success, code = 2 -> failed, code =3 -> resume unlocked before

const sendUnlockResumeRequest = async (id: number, key: string) => {
  try {
    const { data } = await unlockResume(id, key);
    // if (data) {
    //   resumeDetail.value = {
    //     ...resumeDetail.value,
    //     email: data.email,
    //     phone: data.phone,
    //   };
    //   searchResumesStore.addNewResumesUnlocked(data.resume_id);
    //   searchResumesStore.updateCredit(data.remaining_credit);
    // }
    // isReloadPage.value = true;
    await loadData(false);
  } catch (error) {
    const codes = error.response?.data?.errors?.code;
    let errorTitle = "toast_something_went_wrong",
      errorMessage = "toast_save_failed_message";

    if (codes.includes(3)) {
      errorMessage = "search_resumes_resume_unlock_before";
    } else if (codes.includes(4)) {
      errorMessage = "search_resumes_toast_not_enough_credit";
    }

    showWarningToast(translate(errorTitle), translate(errorMessage));
    throw error;
  }
};

const onUpdateWishList = async (id: number, isDeleteAction = false) => {
  isUpdatingWishList.value = true;
  try {
    let res: any;
    if (isDeleteAction) {
      res = await removeWishList(id);
    } else {
      res = await updateWishList(id);
    }

    if (res.errors) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } else {
      resumeDetail.value.is_saved = !isDeleteAction;
      let indexTarget = resumes.value.findIndex((value) => value.id === id);
      resumes.value = resumes.value.map((value, index) => {
        if (index == indexTarget) {
          return { ...value, is_saved: !isDeleteAction };
        }
        return value;
      });
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );
    }
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isUpdatingWishList.value = false;
  }
};

const setDataFileConverFromThemeAPI = (
  stringFile: any,
  originalFileUrl: any,
  typeMedia: "media" | "cvbuilder"
) => {
  originalData.value = {
    mime_type: originalFileUrl.mime_type ?? "pdf",
    url: stringFile,
  };

  if (typeMedia === "media") {
    originalData.value = originalFileUrl;
  }
};

onBeforeMount(async () => {
  layoutStore.toggleActiveSidebar(false);
  window.addEventListener("resize", handleWindowResize);
  window.addEventListener("keydown", (e) => {
    if (e.which == 123) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 73) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 75) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 67) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 74) {
      e.preventDefault();
    }
  });
  if (window.innerWidth < 992) {
    layoutStore.toggleActiveSidebar(false);
  }

  await loadData();
  scrollToTrActiveElement("next");

  //Block right click
  window.addEventListener("contextmenu", (event) => event.preventDefault());
});

const handleWindowResize = () => {
  if (window.innerWidth < 992) {
    layoutStore.toggleActiveSidebar(false);
  }
};

const loadFirst = ref<boolean>(true);

const loadData = async (isScrollToTop: boolean = true) => {
  try {
    if (loadFirst.value) {
      searchResumesStore.resumesParamsRequest.selectedCandidateID = resumeId.value;
    } else {
      searchResumesStore.resumesParamsRequest.selectedCandidateID = 0;
    }
    const { data, meta }: any = await fetchResumes(
      searchResumesStore.resumesParamsRequest
    );

    if (loadFirst.value) {
      loadFirst.value = false;
      updateQueryParam('page',meta.current_page);
    }

    resumes.value = data;
    resumesMeta.value = meta;

    if (data.length === 0) {
      isDataLoading.value = false;
      isExistResumeData.value = false;
      isResumeDetailLoading.value = false;
      isFilterLoading.value = false;
      return;
    }
    isDataLoading.value = false;
    isExistResumeData.value = true;
    isFilterLoading.value = false;

    if (!!resumeId) {
      try {
        if (!!resumeId.value) {
          const { data } = await fetchResumeDetail(resumeId.value);
          resumeDetail.value = data;
          typeTabsCV.value = resumeDetail.value?.type;
        }
      } catch (err) {
        showWarningToast(
          translate("toast_sorry"),
          translate("toast_save_failed_message")
        );
      } finally {
        isResumeDetailLoading.value = false;
      }
    }

    // assign resumeDetail value when pagination
    let indexTarget = resumes.value.findIndex(
      (value) => value.id == resumeDetail.value.id
    );
    if (indexTarget === -1) {
      resumeDetail.value = resumes.value[0];
      resumeId.value = resumeDetail.value.id;
    } else {
      resumeDetail.value = resumes.value[indexTarget];
    }
    // Check and update type preview cv
    resetPreviewCv();

    // Update router browser
    await router.push(
      `/search-candidates/${resumeDetail.value.id}${document.location.search}`
    );
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isFilterLoading.value = false;
    isDataLoading.value = false;
    if (isScrollToTop) {
      scrollToTop();
    }
  }
};

// Watch filters params change -> call api update list resumes
watch(
  () => searchResumesStore.resumesParams,
  async () => {
    isDataLoading.value = true;
    await loadData();
    updateQueryURL(searchResumesStore.resumesParams);
    isDataLoading.value = false;
  },
  { deep: true }
);

watch(
  () => isReloadPage.value,
  async () => {
    if (isReloadPage.value) {
      searchResumesStore.setResumesParams(searchResumesStore.resumesParams);
      isReloadPage.value = false;
    }
  },
  { deep: true }
);

// Watch detail resume id -> update notes, update preview cv url, fetch detail resume to update view status.
watch(
  () => resumeDetail.value.id,
  async (resumeId) => {
    // update is unlocked
    isUnlocked.value = resumeDetail.value.is_unlocked;

    //Update notes
    notesData.isListNotesLoading = true;
    notesData.isActionLoading = true;

    try {
      const { data } = await getMyResumeNotes(resumeId);
      notesData.listNotes = data;
    } catch (err) {
      throw err;
    } finally {
      notesData.isListNotesLoading = false;
      notesData.isActionLoading = false;
    }
    // Fetch detail resume to update view status
    if (!resumeDetail.value.is_viewed) {
      searchResumesStore.addNewResumesViewed(resumeId);
      await fetchResumeDetail(resumeId);
    }

    previewCV.loading = true;
    isResumesLoadingDownload.value = true;
    typeTabsCV.value = resumeDetail.value?.type;

    try {
      // await searchResumesStore.getJsonResumeFormStore(resumeId);
      // await searchResumesStore.getThemeResume();
      // await searchResumesStore.getBlobDataResume(typeTabsCV.value, resumeId);
    } catch (err) {
      throw err;
    } finally {
      isResumesLoadingDownload.value = false;
    }

    objectDataFileDefine.value = searchResumesStore.objectDataFile;
    objectDataFileDefine.value = await objectDataFileDefine.value.arrayBuffer();
    //Update data convert form API CV
    setDataFileConverFromThemeAPI(
      objectDataFileDefine.value,
      "",
      typeTabsCV.value
    );

    // Update preview cv
    updatePreviewCV(originalData.value);

    // Update tab change detail
    resetPreviewCv();

    updatePreviewCVType(previewCV.typeOfCV, resumeId);
  }
);

watch(
  () => previewCV.typeOfCV,
  async (typeOfCV) => {
    previewCV.loading = true;
    objectDataFileDefine.value = searchResumesStore.objectDataFile;
    objectDataFileDefine.value = await objectDataFileDefine.value.arrayBuffer();

    typeTabsCV.value = resumeDetail.value?.type;
    if (typeOfCV == "topdev") typeTabsCV.value = "cvbuilder";

    setDataFileConverFromThemeAPI(
      objectDataFileDefine.value,
      "",
      typeTabsCV.value
    );

    //Update preview cv
    updatePreviewCV(originalData.value);
  }
);

const onUpdatePreviewCv = async (resumeId) => {
  //Fetch search candidate preview CV
  previewCV.loading = true;
  previewCV.original.url = '';
  previewCV.original.mime_type = "pdf";

  try {
    // Call api return preview_url cv
    const result = await fetchResumePreviewCV(resumeId, {
      type: previewCV.typeOfCV,
    });

    setTimeout(() => {
      previewCV.original.url = result.data.preview_url;
      previewCV.original.mime_type = result.data.preview_cv_type ?? "pdf";
    }, 1000);
  } catch (err) {
    previewCV.loading = false;
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }
};

watch(
  () => resumeDetail.value.id,
  async (resumeId) => {
    // Check search candidate type (cv_builder || topdev_cv) => topdev
    switch (resumeDetail.value.type) {
      case "cv_builder":
      case "topdev_cv":
        previewCV.typeOfCV = "topdev";
    }

    await onUpdatePreviewCv(resumeId);
  },
  { deep: true }
);

watch(
  () => notesData,
  () => {
    if (notesData.editId != null) {
      openForm();
    }
  },
  { deep: true }
);

const openForm = () => {
  isOpenForm.value = true;
};

const cancelHandler = () => {
  isOpenForm.value = false;
  notesData.editId = null;
  notesData.noteInputValue = "";
};

onMounted(async () => {
  await fetchCompanyInfo().then(({ data }) => {
    if (data.status == COMPANY_STATUS_REVIEW) {
      showWarningToast(
        "",
        "You are not granted access to this feature. Please complete your company profile"
      );

      router.push("/company/profile");
    }
  });
});
</script>

<style>
.text-willing-to-work {
  color: #ce8800;
  border: 1px solid #ce8800;
  border-radius: 10rem;
  padding: 0.2rem 0.4rem;
}
</style>
