<template>
  <TheMyProductLayout>
    <div class="d-flex justify-content-between align-items-center credit-package-header">
      <div class="d-flex flex-column heading">
        <div class="d-flex align-items-center gap-1">
          <inline-svg src="/assets/icons/wallet.svg" />
          <h2 class="m-0 credit-title text-2xl">
            {{ translate("layout_job_posting_management") }}
          </h2>
        </div>
        <p>{{ translate('usage_history_my_product_description') }}</p>
      </div>
      <div>
        <v-date-picker is-range :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }" :masks="{ input: 'DD-MM-YYYY' }"
          v-model="timeRange">
          <template v-slot="{ inputValue, inputEvents }">
            <div class="d-flex justify-center align-items-center">
              <input :value="inputValue.start" v-on="inputEvents.start" class="form-control form-control-solid"
                :placeholder="translate('candidate_list_form_date')" />
              <span class="svg-icon svg-icon-2x">
                <inline-svg src="/assets/icons/arrow-right.svg" />
              </span>
              <input :value="inputValue.end" v-on="inputEvents.end" class="form-control form-control-solid"
                :placeholder="translate('candidate_list_to_date')" />
            </div>
          </template>
        </v-date-picker>
      </div>
    </div>
    <div id="job-posting-management" v-if="!isLoadingJobPosting" class="d-flex flex-column gap-4 position-relative">
      <AppLoader />
      <TheJobPostingPackageSummary :summary="summary" />
      <TheJobPostingInvoicePackageList :invoices="invoices" @load-more="handleLoadMoreInvoices"
        @on-sort="handleSortValue" />
      <TheJobPostingPackageUsageHistory :usages="usages" @load-more="handleLoadMoreUsageHistory" />
    </div>
    <div v-else>
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </TheMyProductLayout>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { showWarningToast, translate } from "@/helpers";
import TheMyProductLayout from "@/components/TheMyProductLayout.vue";
import TheJobPostingPackageSummary from "@/components/TheJobPostingPackageSummary.vue";
import TheJobPostingInvoicePackageList from "@/components/TheJobPostingInvoicePackageList.vue";
import TheJobPostingPackageUsageHistory from "@/components/TheJobPostingPackageUsageHistory.vue";
import { JobPostingHistoryRequestType, InvoiceResponse, JobPackageSummary, JobPackageUsageResponse, SortType, JobPostingTimeRangeType } from "@/models/usage-history";
import { fetchCompanyInfo, fetchJobPostingHistory } from "@/api/company";
import AppLoader from "@/components/AppLoader.vue";
import { JOB_PACKAGE_SUMMARY_DEFAULT_VALUE, JOB_POSTING_HISTORY_REQUEST_DEFAULT_PARAMS } from "@/constants/usage-history";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import { useRouter } from "vue-router";

const timeRange = ref<JobPostingTimeRangeType>(null);
const invoices = ref<InvoiceResponse>({
  current_page: 1,
  last_page: 1,
  data: []
});
const summary = ref<JobPackageSummary>(JOB_PACKAGE_SUMMARY_DEFAULT_VALUE);
const usages = ref<JobPackageUsageResponse>({
  current_page: 1,
  last_page: 1,
  data: []
});
const jobPostingParams = ref<JobPostingHistoryRequestType>(JOB_POSTING_HISTORY_REQUEST_DEFAULT_PARAMS);
const isLoadingJobPosting = ref(false);

onMounted(() => {
  fetchCompanyInfo()
    .then(({data}) => {
      if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          const router = useRouter();
          router.push('/company/profile');
        }
      });

  initData(true);
});

watch(timeRange, async (newValue) => {
  const jobpostingElement = document.getElementById("job-posting-management");
  jobpostingElement.classList.add('page-loading');
  jobPostingParams.value = {
    ...JOB_POSTING_HISTORY_REQUEST_DEFAULT_PARAMS,
    from_date: newValue ? newValue.start : undefined,
    to_date: newValue ? newValue.end : undefined,
  };
  await initData();
  jobpostingElement.classList.remove('page-loading');
});

const initData = async (isFirstTime = false) => {
  isFirstTime && (isLoadingJobPosting.value = true);
  const { data } = await fetchJobPostingHistory(jobPostingParams.value);
  invoices.value = data.invoices;
  summary.value = data.summary;
  usages.value = data.usages;
  isLoadingJobPosting.value = false;
}

const handleLoadMoreUsageHistory = async (page: number) => {
  jobPostingParams.value.page_usage = page;
  const { data } = await fetchJobPostingHistory(jobPostingParams.value);
  usages.value = {
    ...data.usages,
    data: [...usages.value.data, ...data.usages.data]
  }
}

const handleLoadMoreInvoices = async (page: number) => {
  jobPostingParams.value.page_invoice = page;
  const { data } = await fetchJobPostingHistory(jobPostingParams.value);
  invoices.value = {
    ...data.invoices,
    data: [...invoices.value.data, ...data.invoices.data]
  }
}

const handleSortValue = async (sort: SortType) => {
  jobPostingParams.value = {
    ...jobPostingParams.value,
    page_invoice: 1,
    per_page_invoice: 5,
    invoice_order_by_field: sort.field,
    invoice_order_by: sort.direction,
  }
  const { data } = await fetchJobPostingHistory(jobPostingParams.value);
  invoices.value = data.invoices;
}
</script>
