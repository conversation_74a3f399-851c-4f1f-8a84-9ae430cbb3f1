<template>
  <TheCompanyLayout>
    <template v-slot:topbar>
      <div
        class="d-flex flex-column align-self-center"
        v-if="!isLoadingCompanyInformation"
      >
        <p
          class="text-end mb-2"
          v-html="translate('about_company_note_required')"
        ></p>

        <div class="d-flex gap-4 justify-content-end align-items-center mb-2">
          <a
            :href="detailUrl"
            target="_blank"
            class="btn py-1 pe-0 fw-bolder text-primary"
            >{{ translate("about_company_view_company_site") }}</a
          >
          <div class="d-flex gap-4" v-if="meta.dirty">
            <button
              type="button"
              class="btn btn-sm btn-secondary"
              @click="onCancelButtonClick"
            >
              {{ translate("about_company_cancel") }}
            </button>
            <button
              class="btn btn-sm btn-primary"
              form="company-profile-form"
              type="submit"
              :disabled="
                !meta.dirty || isSubmitting || authStore.user.is_denied
              "
            >
              {{ translate("about_company_save") }}
            </button>
          </div>
        </div>
      </div>
    </template>

    <div v-if="!isLoadingCompanyInformation">
      <form id="company-profile-form" @submit="onSubmit">
        <!--begin::Company Information-->
        <TheCompanyProfileFormInformation />
        <!--end::Company Information-->

        <hr />

        <!--begin::Company addresses-->
        <TheCompanyProfileFormAddresses />
        <!--end::Company addresses-->
        <hr />

        <!--begin::Company benefits-->
        <TheCompanyProfileFormBenefits />
        <!--end::Company benetis-->

        <hr />

        <!--begin::Company image-->
        <TheCompanyProfileFormImages />
        <!--end::Company image-->

        <hr />

        <!--begin::Company top concerns-->
        <TheCompanyProfileFormFaqs />
        <!--end::Company top converns-->

        <hr />

        <!--begin::Company products-->
        <TheCompanyProfileFormProducts />
        <!--end::Company products-->

        <!--begin::Company erc-->
        <TheCompanyProfileFormERC />
        <!--end::Company erc-->

        <!--begin::About footer-->
        <div class="d-flex justify-content-between about-company-footer">
          <div v-html="translate('about_company_note_required')"></div>

          <div class="d-flex gap-4">
            <button
              class="btn btn-sm btn-secondary"
              @click="() => onCancelButtonClick()"
              :disabled="!meta.dirty || isSubmitting"
              type="button"
            >
              {{ translate("about_company_cancel") }}
            </button>
            <button
              class="btn btn-sm btn-primary"
              form="company-profile-form"
              type="submit"
              @click="onSaveViewCompanyButtonClick"
              :disabled="
                !meta.dirty || isSubmitting || authStore.user.is_denied
              "
            >
              {{ translate("about_company_save_and_view_site") }}
            </button>
          </div>
        </div>
        <!--end::About footer-->
      </form>
    </div>
    <div v-else>
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </TheCompanyLayout>
</template>

<script lang="ts" setup>
import { ref, onMounted, toRaw, watch } from "vue";
import { useForm } from "vee-validate";

import TheCompanyProfileFormInformation from "@/components/TheCompanyProfileFormInformation.vue";
import TheCompanyProfileFormAddresses from "@/components/TheCompanyProfileFormAddresses.vue";
import TheCompanyProfileFormBenefits from "@/components/TheCompanyProfileFormBenefits.vue";
import TheCompanyProfileFormImages from "@/components/TheCompanyProfileFormImages.vue";
import TheCompanyProfileFormFaqs from "@/components/TheCompanyProfileFormFaqs.vue";
import TheCompanyProfileFormProducts from "@/components/TheCompanyProfileFormProducts.vue";
import TheCompanyLayout from "@/components/TheCompanyLayout.vue";
import TheCompanyProfileFormERC from "@/components/TheCompanyProfileFormERC.vue";

import { fetchCompanyInfo, updateCompany } from "@/api/company";
import { useLayoutStore, useTaxonomiesStore, useAuthStore } from "@/stores";
import {
  confirmCancelForm,
  showSuccesToast,
  showWarningToast,
  scrollToFirstValidationError,
  translate,
} from "@/helpers";

import { companyProfileSchema } from "@/schemas/company-profile-form";
import { Company } from "@/models/employer";

import { unleash } from "@/plugins";
import { FREE_POST } from "@/plugins/unleash";

const { meta, handleSubmit, resetForm, handleReset, isSubmitting } = useForm({
  validationSchema: companyProfileSchema,
});

/**
 * Define data
 */
const isLoadingCompanyInformation = ref(true);
const isNavigateToCompanySide = ref(false);
const detailUrl = ref("");

/**
 * Init store
 */
const layoutStore = useLayoutStore();
const taxonomiesStore = useTaxonomiesStore();
const authStore = useAuthStore();

/**
 * Functions
 */

//confirm unload page but form have a change
window.onbeforeunload = (e) => {
  if (layoutStore.isConfirmNavigation) {
    e.returnValue = translate("swal_confirm_changes_not_saved");
  }
};

const setCompanyFormValues = (data) => {
  const company: Company = data;

  // Set detail url for later
  detailUrl.value = company.detail_url;

  let companyFormValue: any = {
    display_name: company.display_name,
    image_logo: company.image_logo?.url,
    tagline: company.tagline,
    nationalities: company.nationalities,
    num_employees: company.num_employees,
    description: company.description,
    industries_ids: company.industries_ids,
    skills_ids: company.skills_ids,
    website: company.website,
    social_network: company.social_network,
    addresses: company.addresses,
    benefits: company.benefits,
    image_cover: company.image_cover?.url,
    image_galleries: company.image_galleries,
    faqs: company.faqs,
    products: company.products,
  };

  companyFormValue = {
    ...companyFormValue,
    erc_file: company.erc_file?.url,
    erc_filename: company.erc_file?.filename,
    status: company.status,
  };

  // Reset form
  resetForm({
    values: companyFormValue,
  });
};
const fetchCompany = () => {
  if (authStore.user.is_denied) {
    isLoadingCompanyInformation.value = false;
    return;
  }

  //Fetch company data
  fetchCompanyInfo()
    .then(({ data }) => setCompanyFormValues(data))
    .catch((error) => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    })
    .finally(() => {
      isLoadingCompanyInformation.value = false;
      layoutStore.resetConfirmNavigation();
    });
};

const onSubmit = handleSubmit(
  async (values) => {
    try {
      // Block page
      layoutStore.blockPage();

      values.benefits = values.benefits ? [{ value: values.benefits }] : [];

      const companyInfo = toRaw(values);
      const { data } = await updateCompany(companyInfo);
      if (data && isNavigateToCompanySide.value) {
        window.open(data.detail_url);
      }

      // Refresh company form
      setCompanyFormValues(data);

      // Do stuff on UI
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );
      layoutStore.resetConfirmNavigation();

      isNavigateToCompanySide.value = false;
    } catch (err) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } finally {
      layoutStore.unBlockPage();
    }
  },
  (errors) => onError(errors)
);
const onError = (errors) => {
  scrollToFirstValidationError();
  showWarningToast(
    translate("toast_errors"),
    translate("toast_finish_form_message")
  );
};
const onCancelButtonClick = async () => {
  const confirm = await confirmCancelForm();

  if (confirm.isConfirmed) {
    isNavigateToCompanySide.value = false;
    handleReset();
  }
};
const onSaveViewCompanyButtonClick = () => {
  isNavigateToCompanySide.value = true;
};

/**
 * Get all data need for component on load
 */
onMounted(async () => {
  // Get taxonomies
  await taxonomiesStore.getTaxonomies();

  // Get company information
  fetchCompany();

  // Start unleash
  unleash.start();
});

/**
 * Watch the form changes for confirm before navigation
 */
watch(
  () => meta.value.dirty,
  (isDirty) => {
    isDirty
      ? layoutStore.needConfirmNavigation()
      : layoutStore.resetConfirmNavigation();
  }
);
</script>
