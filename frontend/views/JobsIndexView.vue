<template>
  <TheJobsLayout>
    <!-- Topbar -->
    <template v-slot:topbar>
      <div class="text-end h-100 d-flex align-items-center w-75">
        <p class="mb-0" v-html="translate('top_bar_note_post_job')"></p>
      </div>
    </template>
    <!-- End topbar -->

    <div v-if="!isLoadingAllJobs">
      <!-- Topbar -->
      <div
        class="row flex-wrap d-flex justify-content-between align-items-center mb-5"
      >
        <div class="col d-flex align-items-center mw-50">
          <h2 class="text-nowrap me-3 my-0">
            {{ translate("job_list_list_jobs") }}
          </h2>
          <TheJobsFilterWidget @update="setStatus" :jobsFacets="jobsFacets" />
        </div>
        <TheJobsSearchWidget
          :isChangeJob="isChangeJob"
          :filterOptions="filterOptions"
          :jobsParams="jobsParams"
          @search="setSearch"
          @setJobsFilter="setJobsFilter"
        />
      </div>

      <!--List jobs -->
      <TheJobsList :jobs="jobs" />

      <!-- Pagination  -->
      <div v-if="jobs.length > 0" class="mb-5">
        <AppPagination :meta="jobsMeta" @setPagination="setPagination" />
      </div>

      <!-- Filter Modal  -->
      <TheJobsModalFilter
        :filterOptions="filterOptions"
        :jobsParams="jobsParams"
        @setJobsFilter="setJobsFilter"
      />
      <!-- End  -->
    </div>
    <div v-else>
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </TheJobsLayout>
</template>
<script lang="ts" setup>
import { ref, Ref, onMounted, reactive } from "vue";

import AppPagination from "@/components/AppPagination.vue";
import TheJobsFilterWidget from "@/components/TheJobsFilterWidget.vue";
import TheJobsSearchWidget from "@/components/TheJobsSearchWidget.vue";
import TheJobsList from "@/components/TheJobsList.vue";
import TheJobsModalFilter from "@/components/TheJobsModalFilter.vue";

import { fetchJobs } from "@/api/job";
import {
  fetchAllJobTitle,
  fetchAllEmployerName,
  fetchAllCompanyProvince,
} from "@/api/ams";

import { Jobs, Meta, JobsParams } from "@/models/jobs";
import { scrollToTop, showWarningToast, translate } from "@/helpers";
import TheJobsLayout from "@/components/TheJobsLayout.vue";

import { useTaxonomiesStore } from "@/stores";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import { useRouter } from "vue-router";

//Define store
const taxonomiesStore = useTaxonomiesStore();

interface SetPagination {
  page: number;
  page_size: number;
}

// Define data jobs Params
const isLoadingAllJobs = ref(true);
const jobsParams: JobsParams = reactive({
  page: 1,
  page_size: 5,
  location_id: null,
  job_id: null,
  skills_id: [],
  status: null,
  created_by: null,
  query: "",
});

const jobParamsDefault: JobsParams = reactive({
  location_id: null,
  job_id: null,
  skills_id: [],
  status: null,
  created_by: null,
});

const jobs: Ref<Jobs[] | undefined> = ref([]);
const jobsMeta: Ref<Meta | undefined> = ref();
const jobsFacets = ref();
const isChangeJob = ref(false);

// Define data filter Options
const filterOptions = reactive({
  province: null,
  jobs: null,
  employerName: null,
  taxonomiesSkills: null,
  status: [
    { value: 1, label: "Close" },
    { value: 2, label: "Review" },
    { value: 3, label: "Open" },
    { value: 0, label: "Draft" },
  ],
});

const router = useRouter();

//Life cycle
onMounted(async () => {

  await   fetchCompanyInfo()
    .then(({data}) => {
        if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          router.push('/company/profile');
        }
      });

  try {
    //fetch jobs by params
    const { data, meta, facets }: any = await fetchJobs(jobsParams);
    jobs.value = data;
    jobsMeta.value = meta;
    jobsFacets.value = facets;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    //Finish loading;
    isLoadingAllJobs.value = false;
  }

  //Fetch Province
  const { data: provinceResult } = await fetchAllCompanyProvince();
  filterOptions.province = provinceResult;

  //Fetch all job title
  const { data: jobTitlesResult } = await fetchAllJobTitle();
  filterOptions.jobs = jobTitlesResult;

  //Fetch all employer name
  const { data: employerNamesResult } = await fetchAllEmployerName();
  filterOptions.employerName = employerNamesResult;

  //Get taxonomies
  await taxonomiesStore.getTaxonomies();
  filterOptions.taxonomiesSkills = taxonomiesStore.skills;
});

//Function
const fetchJobsWithTableLoading = async () => {
  const listJobsElement = document.getElementById("list-jobs");
  listJobsElement.className = "page-loading";
  try {
    const fetchJobsParams = {
      location_id: jobsParams.location_id,
      job_id: jobsParams.job_id,
      skills_id: jobsParams.skills_id?.toString(),
      status: jobsParams.status,
      created_by: jobsParams.created_by,
      page: jobsParams.page,
      page_size: jobsParams.page_size,
      query: jobsParams.query,
    };

    const { data, meta }: any = await fetchJobs(fetchJobsParams);
    jobs.value = data;
    jobsMeta.value = meta;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    listJobsElement.classList.remove("page-loading");

    scrollToTop();
  }
};

const setStatus = (value: number) => {
  jobsParams.status = value;
  jobsParams.page = 1;

  fetchJobsWithTableLoading();
};

const setPagination = (value: SetPagination) => {
  jobsParams.page_size = value.page_size;
  jobsParams.page = value.page;

  fetchJobsWithTableLoading();
};

const setJobsFilter = (value: JobsParams, isChange: boolean) => {
  jobsParams.location_id = value.location_id;
  jobsParams.job_id = value.job_id;
  jobsParams.skills_id = value.skills_id;
  jobsParams.status = value.status;
  jobsParams.created_by = value.created_by;
  fetchJobsWithTableLoading();

  if (JSON.stringify(jobParamsDefault) === JSON.stringify(value)) {
    isChangeJob.value = false;
  } else isChangeJob.value = isChange;
};

const setSearch = (value: string) => {
  jobsParams.query = value;
  fetchJobsWithTableLoading();
};
</script>
