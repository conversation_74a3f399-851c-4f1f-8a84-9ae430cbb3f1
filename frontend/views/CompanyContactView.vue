<template>
  <TheCompanyLayout>
    <template v-slot:topbar>
      <div class="d-flex align-items-center h-100">
        <p
          class="text-end mb-0"
          v-html="translate('top_bar_note_contact_info')"
        ></p>
      </div>
    </template>

    <TheCompanyContactInformationForm />
    <TheCompanyContactEmployerList />
  </TheCompanyLayout>
</template>
<script lang="ts" setup>
import TheCompanyContactInformationForm from "@/components/TheCompanyContactInformationForm.vue";
import TheCompanyContactEmployerList from "@/components/TheCompanyContactEmployerList.vue";
import TheCompanyLayout from "@/components/TheCompanyLayout.vue";
import { showWarningToast, translate } from "@/helpers";
import { onMounted } from "vue";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import { useRouter } from "vue-router";

onMounted(() => {
  fetchCompanyInfo()
    .then(({data}) => {
      if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          const router = useRouter();
          router.push('/company/profile');
        }
      });
})
</script>
