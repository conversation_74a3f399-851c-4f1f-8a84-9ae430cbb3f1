<template>
  <TheJobsLayout>
    <DebugPanel v-if="isDevMode" :data="formValue" />
    <TheModalSelectJobPackage />
    <AppModalPendingBuyPackage />
    <ThePopupFreeCustomerPlanPostJob />

    <!-- Topbar -->
    <template v-slot:topbar>
      <div class="text-end h-100 d-flex align-items-center">
        <div>
          <p
            class="mb-1"
            v-html="translate('job_form_note_you_must_fill_in_the_required')"
          ></p>
          <a
            href="https://topdev.vn/ManualEmployerDashboard.pdf"
            target="_blank"
            class="text-primary"
            >{{ translate("top_bar_dont_know_how_to_post_job") }}</a
          >
        </div>
      </div>
    </template>
    <!-- End topbar -->

    <form v-if="!isPostJobLoading" @submit="onSubmit">
      <ThePopupPublicLoading
        v-if="showPublicLoading"
        :jobId="createdJobId"
        @done="() => {
          showPublicLoading = false;
          router.push({ name: 'jobs' });
        }"
      />

      <!-- body  -->
      <div id="create-job-form" class="row gap-4">
        <div class="col-12">
          <h2 class="mb-0 mt-2">{{ translate("top_bar_post_job") }}</h2>
          <p class="heading">
            {{ translate("job_form_job_creates_by") }}
            <b>{{ authStore.user?.email }}</b>
          </p>
        </div>

        <TheJobFormMainContent :isCreate="true" />

        <!-- Upgrade to premium banner -->
        <!--        <div class="col-12">
          <img src="/assets/images/upgrade-premium.svg" class="w-100" />
        </div>-->
        <!-- ./Upgrade to premium banner -->
      </div>

      <!-- action  -->
      <div class="row">
        <div class="col-12">
          <div
            class="d-flex justify-content-between align-items-center my-5 p-4 bg-white rounded-2"
          >
            <div>
              <p
                v-html="
                  translate('job_form_note_you_must_fill_in_the_required')
                "
              ></p>
            </div>

            <div class="d-flex justify-content-end align-items-center gap-4">
              <button
                class="btn btn-link btn-md"
                type="button"
                @click="onSaveDraft"
              >
                Save Draft
              </button>

              <button
                v-if="formValue.level == 'paid'"
                class="btn btn-topdev-2 btn-md btn-outline"
                type="button"
                @click="onPublic"
              >
                <span>Publish</span>
              </button>

              <button class="btn btn-topdev-1 btn-md" type="submit">
                <span v-if="formValue.level == 'paid'">Review and Publish</span>
                <span v-else>Save</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div v-else>
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </TheJobsLayout>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import DebugPanel from "@/components/DebugPanel.vue";
import { useForm } from "vee-validate";
import { useRouter } from "vue-router";

import TheJobsLayout from "@/components/TheJobsLayout.vue";

import { createJob, fetchJobDetail, previewUrl, requestDesign } from "@/api/job";
import {
  useAuthStore,
  useJobStore,
  useLayoutStore,
  useTaxonomiesStore,
} from "@/stores";

import { defaultJobValues, jobFormSchema } from "@/schemas/job-form";
import {
  scrollToFirstValidationError,
  showSuccesToast,
  showWarningToast,
  translate,
} from "@/helpers";

import TheJobFormMainContent from "@/components/TheJobFormMainContent.vue";
import TheModalSelectJobPackage from "@/components/TheModalSelectJobPackage.vue";
import ThePopupFreeCustomerPlanPostJob from "@/components/ThePopupFreeCustomerPlanPostJob.vue";
import AppModalPendingBuyPackage from "@/components/AppModalPendingBuyPackage.vue";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import ThePopupPublicLoading from "@/components/ThePopupPublicLoading.vue";

//Define store
const taxonomiesStore = useTaxonomiesStore();
const authStore = useAuthStore();
const jobStore = useJobStore();
const layoutStore = useLayoutStore();

//Define router
const router = useRouter();

// Define data
const isPostJobLoading = ref(true);
const enve = process.env.MIX_APP_ENV;
const isDevMode = enve === 'local' || window.location.hostname === 'localhost';

// Define form
const {
  values: formValue,
  meta,
  errors,
  resetForm,
  handleSubmit,
  handleReset,
  setFieldError,
} = useForm({
  validationSchema: jobFormSchema,
  initialValues: defaultJobValues,
  validateOnMount: false,
});

const showPublicLoading = ref(false);
const createdJobId = ref<number | null>(null);

// Life cycle
onMounted(async () => {
  await fetchCompanyInfo().then(({ data }) => {
    if (data.status == COMPANY_STATUS_REVIEW) {
      showWarningToast(
        "",
        "You are not granted access to this feature. Please complete your company profile"
      );

      router.push("/company/profile");
    }
  });

  layoutStore.blockPage();

  await taxonomiesStore.getTaxonomies();

  // Set default data
  if (jobStore.status === "duplicate" && !!jobStore.id) {
    try {
      const { data } = await fetchJobDetail(jobStore.id);

      resetForm({
        values: {
          title: data.title,
          content: data.content,
          requirements: data.requirements,
          responsibilities: data.responsibilities,
          addresses_id: data.addresses_id || defaultJobValues.addresses_id,
          salary: data.salary || defaultJobValues.salary,
          experiences_ids: {
            from: data.experiences_ids[0],
            to: data.experiences_ids[1],
          },
          contract_type: data.contract_type_id,
          job_levels: data.job_levels,
          job_types: data.job_types,
          skills_ids: data.skills_ids,
          recruiment_process:
            data.recruiment_process || defaultJobValues.recruiment_process,
          benefits: data.benefits,
          emails_cc: data.emails_cc,
          note: data.note,
          package_id: null,
          job_status_id: 2,
          company_tagline:
            data.company_tagline || defaultJobValues.company_tagline,
          company_logo: data.company_logo || defaultJobValues.company_logo,
          level: data.level,
          category_id: data.category_id,
          job_category_id: data.job_category_id,
          education_degree: data.education_degree_id,
          education_major: data.education_major_id,
          education_certificate: data.education_certificate,

          job_banner: data.job_banner_id,
          job_template: data.job_template_id,
          job_template_color: data.job_template_color_id,
        },
      });
    } catch (err) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    }
  }

  layoutStore.unBlockPage();
  isPostJobLoading.value = false;
});

// Function
const onSubmit = handleSubmit(
  async (values) => {
    layoutStore.blockPage();

    try {
      // Create the job
      const { data: job } = (await createJob(values)) as any;
      // Send to designer
      if (formValue.level == "paid") {
        // await requestDesign(job.id);
      }

      // Done
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );

      // 🔥 Set jobId & show popup
      if (formValue.level == "paid") {
        createdJobId.value = job.id;
        showPublicLoading.value = true;
      } else {
        await router.push({ name: "jobs" });
      }
    } catch (e) {
      // If found error by validation, should show it in the revelation fields
      if (e?.response?.status === 422) {
        const displayMessageFields = ["package_id"];
        const errors = e.response.data.errors;
        for (const key in errors) {
          if (displayMessageFields.includes(key)) {
            setFieldError(
              key as any,
              errors[key].map((val: string) => translate(val))
            );
          }

          // Show toast error if user post free job but already have open job
          if ("free_job_open_exceeded" === key) {
            showWarningToast(
              translate("toast_sorry"),
              translate(errors[key][0])
            );
          }

          if ("free_job_open_quota_exceeded" === key) {
            showWarningToast(
              translate("toast_sorry"),
              translate(errors[key][0])
            );
          }
        }
        scrollToFirstValidationError();
      }

      // Haha user will confuse
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } finally {
      layoutStore.unBlockPage();
    }
  },
  (errors) => {
    scrollToFirstValidationError();
    showWarningToast(
      translate("toast_errors"),
      translate("toast_fill_all_information message")
    );
  }
);

const onSaveDraft = async (isPublic = 0) => {
  try {
    layoutStore.blockPage();

    const jobData = {
      ...formValue,
      save_draft: isPublic === 0,
      on_public: isPublic,
    };

    await createJob(jobData);

    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );

    if (isPublic === 1) {
      await router.push({ name: "jobs" });
    }
  } catch (e) {
    if (e?.response?.status === 422) {
      const displayMessageFields = ["package_id", "title"];
      const errors = e.response.data.errors;
      for (const key in errors) {
        if (displayMessageFields.includes(key)) {
          setFieldError(
            key as any,
            errors[key].map((val: string) => translate(val))
          );
        }
      }
      scrollToFirstValidationError();
    }
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    layoutStore.unBlockPage();
  }
};

const onPublic = () => {
  onSaveDraft(1);
};
</script>
