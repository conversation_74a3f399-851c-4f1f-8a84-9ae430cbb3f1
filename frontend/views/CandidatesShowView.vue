<template>
  <div class="container bg-white">
    <div class="row py-0 height-85vh" v-if="!isCandidatesLoading">
      <!-- Left side  -->
      <div class="col-4 border-end border-gray-400 pt-4 height-85vh">
        <TheCandidatesSearchWidget :isAllCandidates="false" :isDetail="false" />
        <!-- Tabs -->
        <div class="box-nav line-tabs-candidates">
          <div class="d-flex pt-5 align-items-center justify-content-between">
            <div class="d-flex align-items-center nav-tabs flex-1">
              <button
                class="pb-5 btn-tab mw-125px"
                :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 1 }"
                type="button"
                @click="() => activeTab('bestMatch')"
                :title="translate('candidate_tab_best_match')"
              >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_best_match") }}
          </span>
              </button>
              <button
                class="pb-5 btn-tab mw-125px"
                :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 0 }"
                type="button"
                @click="() => activeTab('other')"
                :title="translate('candidate_tab_other')"
              >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_other") }}
          </span>
              </button>
              <button
                class="pb-5 btn-tab mw-125px"
                :class="{ active: candidatesStore.candidatesParams.filter.matching_status == null }"
                type="button"
                @click="() => activeTab('all')"
                :title="translate('candidate_tab_all')"
              >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_all") }}
          </span>
              </button>
            </div>
          </div>
        </div>
        <!-- List candidates  -->
        <div
          class="table-responsive mt-3 mh-70vh pb-0"
          id="candidate-detail-table"
        >
          <table class="table position-relative">
            <thead id="table-body">
              <tr class="fw-bold fs-6 text-gray-800 border-bottom">
                <th class="ps-4">{{ translate("candidate_list_no") }}</th>
                <th>
                  {{ translate("candidate_list_candidate_information") }}
                </th>
              </tr>
            </thead>
            <tbody v-if="candidates.length > 0" class="page-loading">
              <AppLoader v-if="isCandidatesTableLoading" />
              <tr
                v-for="(candidate, index) in candidates"
                :key="index"
                class="border-bottom cursor-pointer"
                :class="{
                  'tr-active': candidate.id === candidateDetail.id,
                }"
                @click="changeDetailCandidate(index)"
              >
                <td class="ps-4">
                  {{
                    index +
                    1 +
                    (candidatesMeta.current_page - 1) *
                      Number(candidatesMeta.per_page)
                  }}
                </td>
                <!-- Candidate information  -->
                <td class="flex-grow-1 text-nowrap mw-300px">
                  <div v-if="candidate.is_resume_exists">
                    <p
                      class="fw-bold text-primary my-1"
                      v-if="candidate.full_name"
                    >
                      {{ candidate.full_name }}
                    </p>
                    <div class="d-flex ">
                    <span class="badge custom-application-status not-match" v-if="candidate.recalled_at ||
                        candidate.is_remove_cv ||
                        !candidate.is_resume_exists || candidate.applied_status === 'Recall'"
                    >
                      {{
                        candidate.recalled_at ||
                        candidate.is_remove_cv ||
                        !candidate.is_resume_exists
                          ? "Recall"
                          : `${candidate.applied_status}*`
                      }}
                    </span>
                      <div
                        class="best-match detail"
                        :class="[
                      candidate.matching_status === 0 && 'd-none',
                    ]"
                      >
                        <div class="badge">
                          <span class="text-match">Best Match</span>
                          <i class="text-white">
                            <inline-svg
                              width="16"
                              height="16"
                              src="/assets/icons/candidates/sparkles.svg"
                            />
                          </i>
                        </div>
                      </div>
                    </div>
                    <p class="my-1" v-if="candidate.email">
                      {{ candidate.email }}
                    </p>
                    <p class="my-1" v-if="candidate.phone">
                      {{ candidate.phone }}
                    </p>
                    <p class="my-1" v-if="candidate.location">
                      {{ candidate.location }}
                    </p>
                  </div>
                  <p v-else class="fw-bold text-primary my-1">
                    {{ translate("candidate_detail_user_has_been_disabled") }}
                  </p>
                  <swiper
                    :spaceBetween="5"
                    :slidesPerView="'auto'"
                    class="tag-skill-container"
                  >
                    <swiper-slide
                      class="tag"
                      v-for="(skill, index) in candidate.skills"
                      :key="index"
                    >
                      {{ skill }}
                    </swiper-slide>
                  </swiper>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Right side  -->
      <div class="col-8 p-7 height-85vh overflow-auto">
        <div v-if="!isCandidateDetailLoading">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <p class="py-1 mb-0 fs-4 lh-1 fw-bold fs-7">
                {{ translate("candidate_detail_job_applied") }}
                <b class="text-primary">{{ candidateDetail.job_title }}</b>
              </p>
              <p class="fs-8 lh-1 py-1">
                {{
                  `${translate("candidate_detail_applied_date")} ${
                    candidateDetail.applied_at
                  }`
                }}
              </p>
            </div>
            <div>
              <span
                class="svg-icon svg-icon-1"
                @click="moveToPrevCandidate()"
              >
                <inline-svg
                  src="/assets/icons/candidates/pagination-prev.svg"
                />
              </span>
              <span
                class="svg-icon svg-icon-1 ms-2"
                @click="moveToNextCandidate()"
              >
                <inline-svg
                  src="/assets/icons/candidates/pagination-next.svg"
                />
              </span>
              <router-link
                :to="{ name: 'candidates' }"
                class="svg-icon ms-3 close-icon"
              >
                <inline-svg src="/assets/icons/cancel.svg" />
              </router-link>
            </div>
          </div>
          <div class="mt-5 d-flex justify-content-between">
            <div
              v-if="candidateDetail.is_resume_exists"
              class="d-flex flex-column justify-content-between"
            >
              <h4 class="mb-1 fs-6">{{ candidateDetail.full_name }}</h4>
              <!-- Mail  -->
              <div
                v-if="!!candidateDetail.email"
                class="d-flex align-items-center"
              >
                <span class="svg-icon">
                  <inline-svg src="/assets/icons/candidates/mail.svg" />
                </span>
                <span class="ms-2 fs-7">{{ candidateDetail.email }}</span>
              </div>
              <!-- Phone  -->
              <div
                v-if="!!candidateDetail.phone"
                class="d-flex align-items-center mt-1"
              >
                <span class="svg-icon">
                  <inline-svg src="/assets/icons/candidates/phone.svg" />
                </span>
                <span class="ms-2 fs-7">{{ candidateDetail.phone }}</span>
              </div>
              <!-- Location   -->
              <div
                v-if="!!candidateDetail.location"
                class="d-flex align-items-center mt-1"
              >
                <span class="svg-icon">
                  <inline-svg src="/assets/icons/candidates/location.svg" />
                </span>
                <span class="ms-2 fs-7">{{ candidateDetail.location }}</span>
              </div>
            </div>
            <p v-else class="fw-bold">
              {{ translate("candidate_detail_user_has_been_disabled") }}
            </p>
            <div
              class="d-flex flex-column justify-content-start align-items-center"
            >
              <div
                class="py-1 px-4 mb-2 ms-3 best-match detail best-match detail"
                :class="[
                      candidateDetail.matching_status === 0 && 'd-none',
                    ]"
              >
                <div class="badge p-1">
                  <span class="text-match fs-6">Best Match</span>
                  <i class="text-white">
                    <inline-svg
                      width="16"
                      height="16"
                      src="/assets/icons/candidates/sparkles.svg"
                    />
                  </i>
                </div>
              </div>
              <div
                v-if="
                  candidateDetail.recalled_at ||
                  candidateDetail.is_remove_cv ||
                  !candidateDetail.is_resume_exists
                "
                class="d-flex align-items-start cursor-not-allowed"
              >
                <span class="svg-icon svg-icon-2">
                  <inline-svg
                    src="/assets/icons/candidates/disabled-download.svg"
                  />
                </span>
                <span class="fw-bold text-disabled fs-6 ms-2">
                  {{ translate("candidate_detail_download_cv") }}
                </span>
              </div>
              <div
                v-else
                class="d-flex align-items-start cursor"
                @click="downloadCV(candidateDetail.id)"
              >
                <span class="svg-icon svg-icon-2">
                  <inline-svg src="/assets/icons/candidates/download.svg" />
                </span>
                <ins class="fw-bold text-success fs-6 ms-2">
                  {{ translate("candidate_detail_download_cv") }}
                </ins>
              </div>
            </div>
          </div>

          <!-- Skills  -->
          <swiper
            :spaceBetween="5"
            :slidesPerView="'auto'"
            class="tag-skill-container mt-1"
            v-if="candidateDetail.skills.length > 0"
          >
            <swiper-slide
              class="tag"
              v-for="(skill, index) in candidateDetail.skills"
              :key="index"
              >{{ skill }}</swiper-slide
            >
          </swiper>
          <TheSelectBoxStatusCandidates
            :candidate="candidateDetail"
            :idChange="idChange"
            :getDataStatus="getDataStatus"
            :isDetailCandidates="true"
            @openDropList="openDropList"
            @changeCandidateProcedureStatus="changeCandidateProcedureStatus"
          />
          <!-- Add note  -->
          <div class="position-relative mt-5">
            <textarea
              v-if="!candidateDetail.is_resume_exists"
              disabled
              type="text"
              name="search"
              class="form-control form-icon form-control-solid"
              :placeholder="translate('candidate_detail_add_note')"
            >
            </textarea>
            <textarea
              v-else
              type="text"
              name="search"
              class="form-control form-icon form-control-solid"
              :placeholder="translate('candidate_detail_add_note')"
              v-model.lazy="note"
            ></textarea>
            <div
              class="position-absolute translate-middle-y top-30 start-0 mx-3"
            >
              <span class="svg-icon svg-icon-2 text-topdev-3">
                <inline-svg src="/assets/icons/candidates/note.svg" />
              </span>
            </div>
          </div>

          <!-- Cover Letter and view cv  -->
          <div
            v-if="
              candidateDetail.is_remove_cv ||
              !candidateDetail.is_resume_exists
            "
            class="border-top border-gray-600 mt-5 overflow-auto mb-3 py-3"
          >
            <p v-if="!candidateDetail.is_resume_exists">
              {{
                translate("candidate_detail_cv_deleted_with_user_be_deleted")
              }}
            </p>
            <p v-else="candidateDetail.is_remove_cv">
              {{
                translate("candidate_detail_cv_deleted_with_user_withdrawal")
              }}
            </p>
          </div>
          <div
            v-else
            class="border-top border-gray-600 mt-5 overflow-auto mb-3"
          >
            <div v-if="candidateDetail.has_cover">
              <!-- Cover letter -->
              <p class="fw-bold py-1 fs-6 mt-2 mb-0 lh-1">
                {{ translate("candidate_detail_cover_letter") }}
              </p>
              <p class="py-1 fs-7" v-html="candidateDetail.cover_letter"></p>
            </div>
            <!-- View CV -->
            <p class="fw-bold mt-3 mb-0 py-1 fs-6 lh-1">
              {{ translate("candidate_detail_view_cv") }}
            </p>
            <div>
              <div
                v-if="isPreviewCVLoading"
                class="loading-cv-text"
                v-html="translate('candidate_detail_loading_text')"
              ></div>
            </div>
            <div>
              <div class="preview-cv-container">
                <div class="page-loading" v-if="isPreviewCVLoading">
                  <AppLoader />
                </div>
                <!-- Show pdf file -->
                <vue-pdf-embed
                  v-if="previewCvData.type === 'pdf'"
                  :source="previewCvData.url"
                  @rendered="onPreviewCVLoaded"
                />
                <!-- Show docs file -->
                <div v-else class="ratio ratio-1x1">
                  <iframe
                    :src="previewCvData.url"
                    title="View CV"
                    allowfullscreen
                    @load="onPreviewCVLoaded"
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else>
          <Skeletor />
          <Skeletor />
          <Skeletor />
        </div>
      </div>
      <!-- End Right side  -->
    </div>
    <!-- Skeletor  -->
    <div v-else class="py-5">
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </div>

  <!-- Pagination  -->
  <div class="bg-white relative-index" v-if="candidates.length > 0 && !isCandidatesLoading">
    <div class="container pb-5 border-bottom border-gray-500">
      <AppPagination :meta="candidatesMeta" @setPagination="setPagination" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Swiper, SwiperSlide } from "swiper/vue";

import VuePdfEmbed from "vue-pdf-embed";

import AppPagination from "@/components/AppPagination.vue";
import AppLoader from "@/components/AppLoader.vue";
import TheCandidatesSearchWidget from "@/components/TheCandidatesSearchWidget.vue";
import TheSelectBoxStatusCandidates from "@/components/TheSelectBoxStatusCandidates.vue";

import { useLayoutStore, useCandidatesStore } from "@/stores";
import {
  fetchCandidates,
  fetchCandidateDetail,
  updateCandidate,
  fetchCandidatePreviewCV,
  fetchCandidateCV,
} from "@/api/candidate";

import { Candidate } from "@/models/candidates";
import { showWarningToast, translate } from "@/helpers";
import { Meta } from "@/models/jobs";
import { InlineSvg } from "@/plugins";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";

//Define store
const layoutStore = useLayoutStore();
const candidatesStore = useCandidatesStore();

//Define route
const router = useRouter();
const route = useRoute();

//Define data
const isCandidatesLoading = ref(false);
const isCandidateDetailLoading = ref(false);
const isCandidatesTableLoading = ref(false);

const candidates = ref([]);
const candidatesMeta = ref<Meta>();
const id = ref();
const candidateDetail = ref<Candidate>();
const previewCvData = reactive({
  url: "",
  type: "",
});
const isPreviewCVLoading = ref(false);

const note = ref("");

const idChange = ref(0);
const getDataStatus = ref([]);

//Function
const openDropList = (id: number, event: any) => {
  event.stopPropagation();
  idChange.value = id;
};

// Change value of the candidates whenerver update it in detail page
const changeCandidateProcedureStatus = (id: number, status: string|null) => {
  const index = candidates.value.findIndex(
    (candidate) => candidate.id === id
  );
  candidates.value[index].procedure_status = status;
};

const closeDropList = () => {
  idChange.value = 0;
};

const changeDetailCandidate = (index: number) => {
  candidateDetail.value = candidates.value[index];
  note.value = candidateDetail.value.employer_note;
};

const downloadCV = (id) => {
  fetchCandidateCV(id)
    .then((response) => {
      window.location.href = response.data.download_url;
    })
    .catch(() => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    });
};

const moveToPrevCandidate = async () => {
  const index = candidates.value.findIndex(
    (candidate) => candidate.id === candidateDetail.value.id
  );
  const prevIndex = index - 1;

  //CHECK IF PREV INDEX LESS THAN 0
  if (prevIndex < 0) {
    // CHECK IF CURRENT PAGE EQUAL 0 -> SET CURRENT PAGE = LAST PAGE
    if (candidatesStore.candidatesParams.page === 1)
      candidatesStore.setCandidatesParams({
        page: candidatesMeta.value.last_page,
      });
    // ELSE CURRENT PAGE IS GOING TO MINUS 1
    else
      candidatesStore.setCandidatesParams({
        page: candidatesStore.candidatesParams.page - 1,
      });

    // GET NEW CANDIDATES
    const { data } = await fetchCandidates(
      candidatesStore.candidatesParamsRequest
    );
    candidates.value = data;

    candidateDetail.value = candidates.value[candidates.value.length - 1];
    note.value = candidateDetail.value.employer_note;
  } else {
    candidateDetail.value = candidates.value[prevIndex];
    note.value = candidateDetail.value.employer_note;
  }
  scrollToTrActiveElement("prev");
};

const moveToNextCandidate = async () => {
  const index = candidates.value.findIndex(
    (candidate) => candidate.id === candidateDetail.value.id
  );
  const nextIndex = index + 1;

  // CHECK IF NEXT INDEX GREATER THAN CANDIDATES LENGTH
  if (nextIndex >= candidates.value.length) {
    // CHECK IF CURRENT PAGE EQUAL LAST PAGE -> SET CURRENT PAGE = 1
    if (
      candidatesStore.candidatesParams.page === candidatesMeta.value.last_page
    )
      candidatesStore.setCandidatesParams({ page: 1 });
    // ELSE CURRENT PAGE IS GOING TO PLUS 1
    else
      candidatesStore.setCandidatesParams({
        page: candidatesStore.candidatesParams.page + 1,
      });

    // GET NEW CANDIDATES
    const { data } = await fetchCandidates(
      candidatesStore.candidatesParamsRequest
    );
    candidates.value = data;

    candidateDetail.value = candidates.value[0];
    note.value = candidateDetail.value.employer_note;
  } else {
    candidateDetail.value = candidates.value[nextIndex];
    note.value = candidateDetail.value.employer_note;
  }
  scrollToTrActiveElement("next");
};

const onPreviewCVLoaded = () => {
  isPreviewCVLoading.value = false;
};

const setPagination = (value) => {
  candidatesStore.setCandidatesParams(value);
};

const scrollToTrActiveElement = (type: string) => {
  const container = document.getElementById("candidate-detail-table");
  const activePos = document.getElementsByClassName(
    "tr-active"
  ) as HTMLCollectionOf<HTMLElement>;

  if ((type = "next")) container.scrollTop = activePos[0].offsetTop;
  if ((type = "prev")) container.scrollTop = activePos[0].offsetTop - 150;
};
const initializeTabState = (tabActive: string = null) => {
  let tab = route.query.tab as string;
  let page = candidatesStore.candidatesParams.page;
  let page_size = candidatesStore.candidatesParams.page_size;
  if (tabActive) {
    tab = tabActive;
    page = 1;
    page_size = 30;
  }
  let matching_status: number | null; // Explicitly type matching_status

  switch (tab) {
    case 'bestMatch':
      matching_status = 1;
      break;
    case 'other':
      matching_status = 0;
      break;
    case 'all':
      matching_status = null;
      break;
    default:
      matching_status = 1;
  }

  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, matching_status},
    page: page,
    page_size: page_size
  });
}
const addQueryTab = (tabActive: string) => {
  const { path, query } = route;
    router.replace({
      path,
      query: { ...query, tab: tabActive }
    });
}
const activeTab = (tabActive: string) => {
  addQueryTab(tabActive)
  initializeTabState(tabActive);
}

//Life cycle
onMounted(async () => {
  layoutStore.setPageTitle("layout_manage_candidates");

  fetchCompanyInfo()
    .then(({data}) => {
      if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          router.push('/company/profile');
        }
      });

  window.onclick = function () {
    if (idChange.value > 0) closeDropList();
  };

  //Get id by params
  id.value = route.params?.id;

  //start loading
  isCandidatesLoading.value = true;
  isCandidateDetailLoading.value = true;

  //Fetch Candidates
  try {
    const { data, meta }: any = await fetchCandidates(
      candidatesStore.candidatesParamsRequest
    );
    candidates.value = data;
    candidatesMeta.value = meta;
    initializeTabState()
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }

  //Fetch candidate detail
  try {
    if (!!id.value) {
      const { data } = await fetchCandidateDetail(
        id.value as unknown as number
      );
      candidateDetail.value = data;
      note.value = candidateDetail.value.employer_note;
    }
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }

  //Finish loading
  isCandidatesLoading.value = false;
  isCandidateDetailLoading.value = false;
});

//watch candidates params
watch(
  () => candidatesStore.candidatesParams,
  async () => {
    isCandidatesTableLoading.value = true;
    try {
      const { data, meta }: any = await fetchCandidates(
        candidatesStore.candidatesParamsRequest
      );
      candidates.value = data;
      candidatesMeta.value = meta;
    } catch (err) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } finally {
      isCandidatesTableLoading.value = false;
    }
  },
  { deep: true }
);

//update note value
watch(
  () => note.value,
  async (value) => {
    if (
      candidateDetail.value.id &&
      value !== candidateDetail.value.employer_note
    ) {
      try {
        await updateCandidate(candidateDetail.value.id, {
          note: value,
        });
        //set note value to list candidates
        const index = candidates.value.findIndex(
          (candidate) => candidate.id === candidateDetail.value.id
        );
        candidates.value[index].employer_note = value;
      } catch (err) {
        showWarningToast(
          translate("toast_sorry"),
          translate("toast_save_failed_message")
        );
      }
    }
  }
);

//watch candidate detail for fetch preview CV & update seen status
watch(
  () => candidateDetail.value,
  async (value) => {
    //Fetch candidate preview CV
    isPreviewCVLoading.value = true;
    try {
      const result = await fetchCandidatePreviewCV(value.id);
      previewCvData.type = result.data.type;
      previewCvData.url =
        result.data.type === "pdf"
          ? result.data.preview_url
          : "https://docs.google.com/gview?embedded=true&url=" +
            result.data.preview_url;
    } catch (err) {
      isPreviewCVLoading.value = false;
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    }
    // update read status
    if (!value.read_status) {
      await fetchCandidateDetail(value.id);
    }
  }
);

watch(
  () => candidatesStore.getValueStatusCandidates,
  (data) => {
    if (
      JSON.stringify(getDataStatus.value) ===
      JSON.stringify(candidatesStore.getValueStatusCandidates)
    )
      return;
    getDataStatus.value = data;
  },
  { deep: true }
);
</script>
