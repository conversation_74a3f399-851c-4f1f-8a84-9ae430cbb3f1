<template>
  <div id="search-resumes-index">
    <div class="container-fluid">
      <div class="pt-4 pt-5">
        <TheSearchCandidatesSearchWidget
          :isShowFull="true"
          :totalResumes="resumesMeta.total"
          :hasFilter="false"
          :hasSaveCandidate="true"
        />
      </div>
    </div>
    <div class="container-fluid my-5">
      <div class="bg-white position-relative">
        <div v-show="!isDataLoading">
          <!-- List Candidates  -->
          <div class="table-responsive table-candidates">
            <table class="table table-row-bordered position-relative">
              <thead>
                <tr
                  class="fw-bold fs-6 text-gray-black border-bottom border-top"
                >
                  <th class="text-center">
                    {{ translate("candidate_list_no") }}
                  </th>
                  <th>
                    {{ translate("candidate_list_candidate_information") }}
                  </th>
                  <th>
                    {{ translate("search_resumes_candidate_experience") }}
                  </th>
                  <th>
                    {{ translate("candidate_list_action") }}
                  </th>
                </tr>
              </thead>

              <tbody v-if="resumes.length > 0" class="page-loading">
                <tr v-for="(resume, index) in resumes" :key="index">
                  <!-- No -->
                  <td class="text-center">
                    <span class="d-block font-semibold">
                      {{ index + noPrefix }}
                    </span>
                  </td>

                  <!-- Candidate information -->
                  <td class="mw-200px">
                    <TheSearchResumeCandidateInformationContainer
                      :resume="resume"
                    />
                  </td>

                  <!-- Candidate experience -->
                  <td class="mw-400px space-y-2 candidate-experience-container">
                    <!-- Candidate summary -->
                    <div
                      class="line-clamp-2 text-gray-600"
                      v-show="resume.summary"
                    >
                      {{ convertHTMLToText(resume.summary) }}
                    </div>

                    <TheSearchCandidateExperienceInformation :resume="resume" />
                  </td>

                  <!-- Action -->
                  <td>
                    <div class="d-flex flex-column align-items-center gap-4">
                      <div class="d-flex align-items-center gap-4">
                        <!-- View detail candidate -->
                        <TheSearchResumePopoverUnlock
                          v-if="!resume.is_unlocked"
                          :resume="{
                            id: resume.id,
                            credit: resume.credit,
                          }"
                          @unlock-resume="sendUnlockResumeRequest"
                        />
                        <div v-else class="text-center">
                          <p
                            class="btn-reset-custom btn-unlocked-info"
                            :title="translate('search_resumes_paid')"
                          >
                            {{ translate("search_resumes_paid") }}
                          </p>
                        </div>
                        <button
                          class="btn btn-sm btn-action-candidate"
                          :class="resume.is_saved ? 'text-danger' : ''"
                          @click="onUpdateWishList(resume.id, resume.is_saved)"
                        >
                          <inline-svg
                            v-if="resume.is_saved"
                            src="/assets/icons/bookmark-fill.svg"
                          />
                          <inline-svg v-else src="/assets/icons/bookmark.svg" />
                        </button>
                        <button
                          v-if="resume.is_unlocked"
                          class="btn btn-sm btn-action-candidate"
                          @click="
                            searchCandidateDownloadCV(
                              resume.id,
                              $event,
                              resume.is_expired
                            )
                          "
                        >
                          <inline-svg
                            v-if="!resume.is_expired"
                            src="/assets/icons/candidates/download.svg"
                          />
                          <inline-svg
                            v-else
                            src="/assets/icons/candidates/disabled-download.svg"
                            class="disabled-download"
                            data-toggle="tooltip"
                            data-placement="top"
                            :title="
                              translate('search_resumes_expired_download_cv')
                            "
                          />
                        </button>
                      </div>
                      <div class="d-flex flex-row justity-content-center">
                        <div
                          class="text-willing-to-work d-flex align-items-center gap-1 fw-bold"
                          v-if="
                            !resume.willing_to_work &&
                            (resume.is_unlocked ||
                              resume.force_show_willing_to_work_status_tip)
                          "
                        >
                          <span class="svg-icon mr-1"
                            ><inline-svg src="/assets/icons/yellow-dot.svg"
                          /></span>
                          {{
                            translate("search_resumes_willing_to_work_status")
                          }}
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>

              <tbody v-else class="page-loading">
                <tr>
                  <td colspan="6" class="text-center">
                    <inline-svg
                      src="/assets/icons/candidates/no-search-result.svg"
                      class="empty-icon"
                    />
                    <p
                      class="fs-7 text-topdev-2"
                      v-html="
                        translate('search_resumes_could_not_find_candidate')
                      "
                    ></p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- End List Candidates  -->

          <AppPagination
            v-if="resumes.length > 0"
            :meta="resumesMeta"
            @setPagination="onPagination"
          />
        </div>
        <div v-show="isDataLoading" class="container py-5">
          <Skeletor />
          <Skeletor />
          <Skeletor />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onBeforeMount,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from "vue";
import {
  fetchResumes,
  fetchSearchCandidateUnlockCV,
  removeWishList,
  unlockResume,
  updateWishList,
} from "@/api/search-resume";
import AppPagination from "@/components/AppPagination.vue";
import TheSearchCandidatesSearchWidget from "@/components/TheSearchResumesSearchWidget.vue";
import {
  getQueryParam,
  scrollToTop,
  showSuccesToast,
  showWarningToast,
  translate,
} from "@/helpers";
import { SearchCandidate } from "@/models/search-resumes";
import {
  DEFAULT_PER_PAGE,
  useLayoutStore,
  useSearchResumesStore,
} from "@/stores";
import TheSearchResumeCandidateInformationContainer from "@/components/TheSearchResumeCandidateInformationContainer.vue";
import TheSearchResumePopoverUnlock from "@/components/TheSearchResumePopoverUnlock.vue";
import { updateQueryURL } from "@/helpers/searchCandidate";
import { Meta } from "@/models/jobs";
import TheSearchCandidateExperienceInformation from "@/components/TheSearchCandidateExperienceInformation.vue";
import { fetchCompanyInfo } from "@/api/company";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";
import { useRouter } from "vue-router";
// Define data
const isDataLoading = ref(true);
const resumes = ref<SearchCandidate[]>([]);
const resumesMeta = ref<Meta>({
  total: 0,
  current_page: 1,
  per_page: DEFAULT_PER_PAGE.toString(),
  from: 1,
  to: 1,
  last_page: 1,
  links: [{ active: true, label: "", url: "" }],
});

const searchResumesStore = useSearchResumesStore();
const layoutStore = useLayoutStore();

const noPrefix = computed(() => {
  return (
    1 +
    (resumesMeta.value.current_page - 1) * parseInt(resumesMeta.value.per_page)
  );
});

searchResumesStore.setResumesParams({
  filter: {
    skill: getQueryParam("skill") ? getQueryParam("skill").split(",") : [],
    experience: getQueryParam("experience")
      ? getQueryParam("experience").split(",")
      : [],
    location: getQueryParam("location") ?? "",
    language: getQueryParam("language") ?? "",
    candidate_language: getQueryParam("candidate_language") ?? "",
    timeRange: {
      start: getQueryParam("timeRange_start") ?? "",
      end: getQueryParam("timeRange_end") ?? "",
    },
  },
  keyword: getQueryParam("keyword") ? getQueryParam("keyword").split(",") : [],
  showResumesUnlocked: !!+getQueryParam("showResumesUnlocked"),
  showWishList: !!+getQueryParam("showWishList"),
  page: +getQueryParam("page") || 1,
  page_size: +getQueryParam("page_size") || DEFAULT_PER_PAGE,
});

const onPagination = (value: { page_size: number; page: number }) => {
  searchResumesStore.setResumesParams(value);
};

const onUpdateWishList = async (
  id: number,
  isDeleteAction: boolean = false
) => {
  try {
    let res: any;
    if (isDeleteAction) {
      res = await removeWishList(id);
    } else {
      res = await updateWishList(id);
    }

    if (res.errors) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } else {
      let indexTarget = resumes.value.findIndex((value) => value.id === id);
      resumes.value = resumes.value.map((value, index) => {
        if (index == indexTarget) {
          return { ...value, is_saved: !isDeleteAction };
        }
        return value;
      });
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );
    }
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }
};

const searchCandidateDownloadCV = (
  resumeId: number,
  event: Event,
  isExpired: boolean = false
) => {
  if (isExpired) {
    event.preventDefault();
    return;
  }

  event.stopPropagation();
  fetchSearchCandidateUnlockCV(resumeId)
    .then((res) => {
      window.location.href = res.data.download_url;
    })
    .catch(() => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    });
};

//If code = 1 -> success, code = 2 -> failed, code =3 -> resume unlocked before
const sendUnlockResumeRequest = async (
  id: number,
  // index: number,
  key: string
) => {
  try {
    const { data } = await unlockResume(id, key);

    await loadData(false);
    showSuccesToast(
      translate("toast_congrats"),
      translate("search_resumes_resume_unlock_success")
    );
    // isReloadPage.value = true;
  } catch (error) {
    const codes = error.response?.data?.errors?.code;
    let errorTitle = "toast_something_went_wrong",
      errorMessage = "toast_save_failed_message";

    if (codes.includes(3)) {
      errorMessage = "search_resumes_resume_unlock_before";
    } else if (codes.includes(4)) {
      errorMessage = "search_resumes_toast_not_enough_credit";
    }

    showWarningToast(translate(errorTitle), translate(errorMessage));
    throw error;
  }
};

const getAndSetSeeMore = async () => {
  const items = (await document.getElementsByClassName(
    "box_search_resumes_work_experience_education"
  )) as HTMLCollectionOf<HTMLElement>;
  for (let i = 0; i < items.length; i++) {
    let element = items[i];
    let height = element.clientHeight;
    if (height > 160) {
      element.classList.add("fix");
      element.lastElementChild.classList.add("d-inline-flex");
    } else {
      element.classList.add("no-fix");
      element.lastElementChild.classList.add("d-none");
    }
  }
};

const loadData = async (isScrollToTop: boolean = true) => {
  try {
    const { data, meta }: any = await fetchResumes(
      searchResumesStore.resumesParamsRequest
    );
    resumes.value = data;
    resumesMeta.value = meta;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isDataLoading.value = false;
    await getAndSetSeeMore();
    if (isScrollToTop) {
      scrollToTop();
    }
  }
};

onBeforeMount(async () => {
  layoutStore.toggleActiveSidebar(false);
  window.addEventListener("resize", handleWindowResize);
  window.addEventListener("keydown", (e) => {
    if (e.which == 123) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 73) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 75) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 67) {
      e.preventDefault();
    }
    if (e.ctrlKey && e.shiftKey && e.which == 74) {
      e.preventDefault();
    }
  });
  if (window.innerWidth < 992) {
    layoutStore.toggleActiveSidebar(false);
  }
  await loadData();

  //Block right click
  window.addEventListener("contextmenu", (event) => event.preventDefault());
});

onUnmounted(() => {
  window.removeEventListener("resize", handleWindowResize);
});

const handleWindowResize = () => {
  if (window.innerWidth < 992) {
    layoutStore.toggleActiveSidebar(false);
  }
};

const convertHTMLToText = (str: string) => {
  const div = document.createElement("div");
  div.innerHTML = str;
  return div.textContent || div.innerText || "";
};

// Watch params change -> call api update list resumes
watch(
  () => searchResumesStore.resumesParams,
  async () => {
    isDataLoading.value = true;
    await loadData();
    updateQueryURL(searchResumesStore.resumesParams);
    isDataLoading.value = false;
  },
  { deep: true }
);

const router = useRouter();

onMounted(async () => {
  await fetchCompanyInfo().then(({ data }) => {
    if (data.status == COMPANY_STATUS_REVIEW) {
      showWarningToast(
        "",
        "You are not granted access to this feature. Please complete your company profile"
      );

      router.push("/company/profile");
    }
  });
});
</script>

<style>
.text-willing-to-work {
  color: #ce8800;
  border: 1px solid #ce8800;
  border-radius: 10rem;
  padding: 0.2rem 0.4rem;
  white-space: nowrap;
}
</style>
