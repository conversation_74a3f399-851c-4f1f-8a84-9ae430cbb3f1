import {
  <PERSON>uComponent,
  <PERSON><PERSON>Component,
  Sticky<PERSON>omponent,
  ToggleComponent,
  DrawerComponent,
  SwapperComponent,
} from "@/assets/ts/components";

/**
 * @description Initialize KeenThemes custom components
 */
const initializeComponents = () => {
  setTimeout(() => {
    ToggleComponent.bootstrap();
    StickyComponent.bootstrap();
    MenuComponent.bootstrap();
    ScrollComponent.bootstrap();
    DrawerComponent.bootstrap();
    SwapperComponent.bootstrap();
  }, 0);
};

export { initializeComponents };
