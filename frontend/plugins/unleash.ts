import { UnleashClient } from "unleash-proxy-client";
import { ref } from "vue";

const unleash = new UnleashClient({
  url: process.env.MIX_UNLEASH_URL || "https://api.topdev.asia/feature",
  clientKey: process.env.MIX_UNLEASH_CLIENT_KEY || "some-secret",
  appName: process.env.MIX_APP_ENV || "development",
});

const unleashReady = ref(false);

const FREE_POST = 'dev-2062-free-post';

export default unleash;
export { unleashReady, FREE_POST };
