import { formatDate } from "@/helpers";
import { defineStore } from "pinia";
import { subDays } from "date-fns";

interface StateType {
  job_id: string[] | null;
}
const today = new Date();
const tenDaysAgo = subDays(today, 10);
export const useDashBoardStore = defineStore("dashboard", {
  state: () => ({
    job_id: [],
    from_date: formatDate(new Date()),
    to_date: formatDate(new Date()),
    from_to_raw: { start: tenDaysAgo, end: new Date() },
    type:'today'
  }),

  getters: {
    getJobId: (state: StateType): string[] => state.job_id,
  },

  actions: {
    updateJobId(job_ids: string[] | null) {
      if (job_ids === null)
        this.job_id = []
      else {
        this.job_id = job_ids;
      }
    },
    updateRangeDate(from_date: string, to_date: string) {
      this.from_date = from_date;
      this.to_date = to_date;
    },
    updateTypeFilter(type: string) {
      this.type = type;
    },
  },
});
