import { defineS<PERSON> } from "pinia";
import { getInfo } from "@/api/auth";

/**
 * Interface define for state
 */
interface State {
  user: User;
  isLogged: boolean;
}

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  phone: string;
  position: string;
  roles: Array<string>;
  company_id: number;
  is_denied: boolean;
  is_unlocked: boolean;
  available_credit: number;
  approved: boolean;
}
/**
 * Define state
 */
export const useAuthStore = defineStore("auth", {
  state: (): State => ({
    user: {
      id: null,
      username: "",
      email: "",
      full_name: "",
      phone: "",
      position: "",
      roles: [],
      company_id: null,
      is_denied: false,
      is_unlocked: false,
      available_credit: 0,
      approved: false
    },
    isLogged: false,
  }),

  getters: {
    hasRoles: (state: State): boolean =>
      state.user.roles &&
      state.user.roles.length > 0 &&
      state.user.roles.includes("employer"),
    roles: (state: State): string[] => state.user.roles,
  },

  actions: {
    async getUserInfo(): Promise<User> {
      const { data } = await getInfo();
      if (!data) {
        throw new Error("Can't get user");
      }

      const {
        id,
        username,
        email,
        full_name,
        phone,
        position,
        roles,
        company_id,
        is_denied,
        is_unlocked,
        available_credit,
        approved
      } = data;

      if (!roles || roles.length == 0 || !roles.includes("employer")) {
        throw new Error("User do not have permission");
      }

      // Set current user
      this.user = {
        id,
        username,
        email,
        full_name,
        phone,
        position,
        roles,
        company_id,
        is_denied,
        is_unlocked,
        available_credit,
        approved
      };

      // Set logged
      this.isLogged = true;

      return this.user;
    },
    resetLogged() {
      this.isLogged = false;
    },
  },
});
