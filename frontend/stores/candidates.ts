import { defineStore } from "pinia";
import {
  CandidatesParams,
  CandidatesTaxonomies,
  OptionStatusCandidates
} from "@/models/candidates";
import { fetchAllJobTitle, fetchAllCandidatesProvince } from "@/api/ams";

interface State {
  candidatesParams: CandidatesParams;
  isChange: boolean;
  candidatesTaxonomies: CandidatesTaxonomies;
  statusCandidates: OptionStatusCandidates;
}

export const candidatesParamsDefine = {
  query: "",
  page: 1,
  page_size: 30,
  filter: {
    job_id: null,
    application_status: null,
    procedure_status: null,
    location_id: "",
    skills_id: [],
    experience_ids: [],
    timeRange: {
      start: "",
      end: ""
    },
    matching_status: 1
  }
};

const candidatesTaxonomiesDefine = {
  applicationStatus: [
    { value: 1, label: "Ready" },
    { value: 5, label: "Not matching" }
  ],
  procedureStatus: [
    { value: "not_started", label: "candidate_list_not_started" },
    { value: "not_matching", label: "candidate_list_not_matching" },
    { value: "matched", label: "candidate_list_matched" },
    // { value: "interviewed_passed", label: "candidate_list_interviewed_passed_option" },
    // { value: "interviewed_failed", label: "candidate_list_interviewed_failed_option" },
    { value: "interview_appointment", label: "hr_dashboard_interview_appointment" },
    { value: "offer", label: "candidate_list_offer" },
    { value: "hired", label: "candidate_list_hired" },
    { value: "failed", label: "candidate_list_failed" }
  ],
  experiences: [
    { value: -1, label: "candidate_list_under_one_year" },
    { value: 1, label: "candidate_list_one_year" },
    { value: 2, label: "candidate_list_two_year" },
    { value: 3, label: "candidate_list_three_year" },
    { value: 4, label: "candidate_list_four_year" },
    { value: 5, label: "candidate_list_five_year" },
    { value: 6, label: "candidate_list_six_year" },
    { value: 7, label: "candidate_list_seven_year" },
    { value: 8, label: "candidate_list_eight_year" },
    { value: 9, label: "candidate_list_nine_year" },
    { value: 10, label: "candidate_list_ten_year" },
    { value: 11, label: "candidate_list_above_ten_year" }
  ],
  candidatesProvince: [],
  jobTitles: [],
  matchingStatus: [
    { value: 1, label: "candidate_list_best_match" },
    { value: 2, label: "candidate_list_other" }
  ]
};

const optionStatusCandidatesDefine = {
  values: []
};

export const useCandidatesStore = defineStore("candidates", {
  state: (): State => ({
    candidatesParams: candidatesParamsDefine,
    candidatesTaxonomies: candidatesTaxonomiesDefine,
    isChange: false,
    statusCandidates: optionStatusCandidatesDefine
  }),
  getters: {
    candidatesParamsRequest: (state) => {
      return {
        page: state.candidatesParams.page,
        page_size: state.candidatesParams.page_size,
        query: state.candidatesParams.query,
        application_status: state.candidatesParams.filter.application_status,
        procedure_status: state.candidatesParams.filter.procedure_status,
        experience_ids: state.candidatesParams.filter.experience_ids.toString(),
        job_id: state.candidatesParams.filter.job_id,
        location_id: state.candidatesParams.filter.location_id,
        skills_id: state.candidatesParams.filter.skills_id.toString(),
        applied_date_from: state.candidatesParams.filter.timeRange?.start,
        applied_date_to: state.candidatesParams.filter.timeRange?.end,
        matching_status: state.candidatesParams.filter.matching_status
      };
    },
    getValueStatusCandidates: (state: State) => state.statusCandidates.values
  },
  actions: {
    setCandidatesParams(params) {
      this.candidatesParams = { ...this.candidatesParams, ...params };
      this.isChange = true;
    },
    resetCandidatesParams() {
      this.candidatesParams = candidatesParamsDefine;
      this.isChange = false;
    },
    setValueStatusCandidates(params) {
      this.statusCandidates.values = {
        ...this.statusCandidates.values,
        ...params
      };
    },
    async getCandidatesTaxonomies() {
      try {
        const { data: candidatesProvince } = await fetchAllCandidatesProvince();
        const { data: jobTitles } = await fetchAllJobTitle(true);
        this.candidatesTaxonomies = {
          ...this.candidatesTaxonomies,
          candidatesProvince,
          jobTitles
        };
      } catch (error) {
        throw error;
      }
    }
  }
});
