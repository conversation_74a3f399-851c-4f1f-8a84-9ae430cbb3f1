import { defineStore } from "pinia";
import { getBannerPromoteApp } from "@/api/ams";

interface BannerModal {
  title: string;
  description: string;
  image: string;
}
interface State {
  pageTitle: string;
  isBlock: boolean;
  isConfirmNavigation: boolean;
  isShowAnnouncementBar: boolean;
  dataBanner: BannerModal;
  isActiveSidebar: boolean;
}

const dataBannerDefine = {
  title: "",
  description: "",
  image: "",
};

export const useLayoutStore = defineStore("layout", {
  state: (): State => ({
    pageTitle: "",
    isBlock: false,
    isConfirmNavigation: false,
    isShowAnnouncementBar: false,
    dataBanner: dataBannerDefine,
    isActiveSidebar: true,
  }),

  getters: {},

  actions: {
    setPageTitle(pageTitle: string) {
      this.pageTitle = pageTitle;
    },
    blockPage() {
      this.isBlock = true;
    },
    unBlockPage() {
      this.isBlock = false;
    },
    needConfirmNavigation() {
      this.isConfirmNavigation = true;
    },
    resetConfirmNavigation() {
      this.isConfirmNavigation = false;
    },
    hideAnnouncementBar() {
      this.isShowAnnouncementBar = false;
    },
    async getBannerModal(type: string) {
      try {
        const { data } = await getBannerPromoteApp(type);
        this.dataBanner = { ...this.dataBanner, ...data?.[0] };
      } catch (err) {
        throw err;
      }
    },
    toggleActiveSidebar(value?: boolean) {
      if (typeof value !== "undefined") {
        this.isActiveSidebar = value;
      } else {
        this.isActiveSidebar = !this.isActiveSidebar;
      }
    },
  },
});
