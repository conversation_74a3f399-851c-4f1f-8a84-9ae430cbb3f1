import { defineStore } from "pinia";
import i18n from "@/plugins/i18n";

import { fetchTaxonomies } from "@/api/ams";

interface ResponseData {
  id: number;
  text_vi: string;
  text_en: string;
  description: string;
  thumbnail_url: string;
}

interface MultiselectOption {
  value: number;
  label: string;
  description: string;
}
interface State {
  taxonomies: {
    skills: Array<ResponseData>;
    num_employees: Array<ResponseData>;
    nationalities: Array<ResponseData>;
    industries: Array<ResponseData>;
    job_levels: Array<ResponseData>;
    languages: Array<ResponseData>;
    job_types: Array<ResponseData>;
    experiences: Array<ResponseData>;
    contract_types: Array<ResponseData>;
    role: Array<ResponseData>;
    responsibilities: Array<ResponseData>;
    requirements: Array<ResponseData>;
    benefits: Array<ResponseData>;
    education: Array<ResponseData>;
    education_major: Array<ResponseData>;
    recruitment_processes: Array<ResponseData>;
    job_banner: Array<ResponseData>;
    job_template: Array<ResponseData>;
    job_template_color: Array<ResponseData>;
  };
  multiSelectOptions: {
    skills: Array<MultiselectOption>;
    numEmployee: Array<MultiselectOption>;
    nationalities: Array<MultiselectOption>;
    industries: Array<MultiselectOption>;
    jobLevels: Array<MultiselectOption>;
    jobTypes: Array<MultiselectOption>;
    experiences: Array<MultiselectOption>;
    contractTypes: Array<MultiselectOption>;
    role: Array<MultiselectOption>;
    responsibilities: Array<MultiselectOption>;
    requirements: Array<MultiselectOption>;
    benefits: Array<MultiselectOption>;
    education: Array<MultiselectOption>;
    educationMajor: Array<MultiselectOption>;
    recruitmentProcesses: Array<MultiselectOption>;
  };
  isTaxonomiesLoaded: boolean;
}

export type TaxonomiesKey = keyof State["taxonomies"];

// constant contain list ID except experiences
const EXPERIENCE_THREE_MONTHS = 8667;
const EXPERIENCE_SIX_MONTHS = 1641;
const ALL_EXPERIENCES = 4851;
const EXCEPT_EXPERIENCES = [
  EXPERIENCE_THREE_MONTHS,
  EXPERIENCE_SIX_MONTHS,
  ALL_EXPERIENCES,
];

export const useTaxonomiesStore = defineStore("taxonomies", {
  state: (): State => ({
    taxonomies: {
      skills: [],
      num_employees: [],
      nationalities: [],
      industries: [],
      job_levels: [],
      job_types: [],
      languages: [],
      experiences: [],
      contract_types: [],
      role: [],
      responsibilities: [],
      requirements: [],
      benefits: [],
      education: [],
      education_major: [],
      recruitment_processes: [],
      job_banner: [],
      job_template: [],
      job_template_color: []
    },
    multiSelectOptions: {
      skills: [],
      numEmployee: [],
      nationalities: [],
      industries: [],
      jobLevels: [],
      jobTypes: [],
      experiences: [],
      contractTypes: [],
      role: [],
      responsibilities: [],
      requirements: [],
      benefits: [],
      education: [],
      educationMajor: [],
      recruitmentProcesses: []
    },
    isTaxonomiesLoaded: false,
  }),

  getters: {
    skills: (state) => {
      return state.multiSelectOptions.skills;
    },
    numEmployee: (state) => {
      return state.multiSelectOptions.numEmployee;
    },
    nationalities: (state) => {
      return state.multiSelectOptions.nationalities;
    },
    industries: (state) => {
      return state.multiSelectOptions.industries;
    },
    jobLevels: (state) => {
      return state.multiSelectOptions.jobLevels;
    },
    jobTypes: (state) => {
      return state.multiSelectOptions.jobTypes;
    },
    experiences: (state) => {
      return state.multiSelectOptions.experiences;
    },
    contractTypes: (state) => {
      return state.multiSelectOptions.contractTypes;
    },
    role: (state) => {
      return state.multiSelectOptions.role;
    },
    benefits: (state) => {
      return state.multiSelectOptions.benefits;
    },
    responsibilities: (state) => {
      return state.multiSelectOptions.responsibilities;
    },
    requirements: (state) => {
      return state.multiSelectOptions.requirements;
    },
    education: (state) => {
      return state.multiSelectOptions.education;
    },
    educationMajor: (state) => {
      return state.multiSelectOptions.educationMajor;
    },
    recruitmentProcesses: (state) => {
      return state.multiSelectOptions.recruitmentProcesses;
    },
  },

  actions: {
    async getTaxonomies() {
      const lang = i18n.global.locale.value;

      this.isTaxonomiesLoaded = false;

      const result = await fetchTaxonomies(
        [
          "skills",
          "num_employees",
          "nationalities",
          "industries",
          "job_levels",
          "job_types",
          "experiences",
          "contract_types",
          "salary_range",
          "languages",
          "role",
          "responsibilities",
          "benefits",
          "education",
          "education_major",
          "requirements",
          "recruitment_processes",
          "job_banner",
          "job_template",
          "job_template_color",
        ].join(",")
      );

      if (!result.data) {
        // TODO: log error
        return;
      }

      result.data.experiences = result.data.experiences.filter(
        (item) => !EXCEPT_EXPERIENCES.includes(item.id)
      );

      this.taxonomies = result.data;

      this.setOptionsByLang(lang);

      this.isTaxonomiesLoaded = true;
    },

    setOptionsByLang(lang: string) {
      this.multiSelectOptions.skills = this.taxonomies.skills.map(
        (skill: ResponseData) => ({
          value: skill.id,
          label: lang === "vi" ? skill.text_vi : skill.text_en,
        })
      );

      this.multiSelectOptions.numEmployee = this.taxonomies.num_employees.map(
        (employee: ResponseData) => ({
          value: employee.id,
          label: lang === "vi" ? employee.text_vi : employee.text_en,
        })
      );

      this.multiSelectOptions.nationalities = this.taxonomies.nationalities.map(
        (nationality: ResponseData) => ({
          value: nationality.id,
          label: lang === "vi" ? nationality.text_vi : nationality.text_en,
        })
      );

      this.multiSelectOptions.industries = this.taxonomies.industries.map(
        (industry: ResponseData) => ({
          value: industry.id,
          label: lang === "vi" ? industry.text_vi : industry.text_en,
        })
      );

      this.multiSelectOptions.jobLevels = this.taxonomies.job_levels
        .filter((item: any) => {
          return item.id != 1618 && item.id != 8459;
        })
        .map((jobLevel: ResponseData) => ({
          value: jobLevel.id,
          label: lang === "vi" ? jobLevel.text_vi : jobLevel.text_en,
        }));

      this.multiSelectOptions.jobTypes = this.taxonomies.job_types.map(
        (jobType: ResponseData) => ({
          value: jobType.id,
          label: lang === "vi" ? jobType.text_vi : jobType.text_en,
        })
      );

      this.multiSelectOptions.experiences = this.taxonomies.experiences.map(
        (experience: ResponseData) => ({
          value: experience.id,
          label: lang === "vi" ? experience.text_vi : experience.text_en,
        })
      );

      this.multiSelectOptions.contractTypes =
        this.taxonomies.contract_types.map((contractType: ResponseData) => ({
          value: contractType.id,
          label: lang === "vi" ? contractType.text_vi : contractType.text_en,
        }));

      this.multiSelectOptions.role = this.taxonomies.role.map(
        (role: ResponseData) => ({
          value: role.id,
          label: lang === "vi" ? role.text_vi : role.text_en,
        })
      );

      this.multiSelectOptions.benefits = this.taxonomies.benefits.map(
        (benefit: ResponseData) => ({
          value: benefit.id,
          label: lang === "vi" ? benefit.text_vi : benefit.text_en,
        })
      );

      this.multiSelectOptions.responsibilities =
        this.taxonomies.responsibilities.map(
          (responsibility: ResponseData) => ({
            value: responsibility.id,
            label:
              lang === "vi" ? responsibility.text_vi : responsibility.text_en,
            description: responsibility.description,
          })
        );

      this.multiSelectOptions.education = this.taxonomies.education.map(
        (education: ResponseData) => ({
          value: education.id,
          label: lang === "vi" ? education.text_vi : education.text_en,
        })
      );

      this.multiSelectOptions.requirements = this.taxonomies.requirements.map(
        (requirement: ResponseData) => ({
          value: requirement.id,
          label: lang === "vi" ? requirement.text_vi : requirement.text_en,
        })
      );

      this.multiSelectOptions.educationMajor = this.taxonomies.education_major.map(
        (major: ResponseData) => ({
          value: major.id,
          label: lang === "vi" ? major.text_vi : major.text_en,
        })
      );

      this.multiSelectOptions.recruitmentProcesses = this.taxonomies.recruitment_processes.map(
        (process: ResponseData) => ({
          value: process.id,
          label: lang === "vi" ? process.text_vi : process.text_en,
        })
      );
    },

    setSkillsData(data: Array<MultiselectOption>) {
      this.taxonomies.skills = data;
    },

    setExperiencesData(data: Array<MultiselectOption>) {
      this.taxonomies.experiences = data;
    },
  },
});
