import { defineStore } from "pinia";
import _ from "lodash";

import { ResumesParams, ShoppingMe, TimeRange } from "@/models/search-resumes";
import {
  exportThemeResume,
  fetchShoppingMe,
  getJsonResume,
  downloadResume,
} from "@/api/search-resume";
import { getQueryParam } from "@/helpers";

interface State {
  resumesParams: ResumesParams;
  isChange: boolean;
  isChangeSearch: boolean;
  isChangeSearchCredits: boolean;
  shoppingMe: ShoppingMe;
  objectDataFile: object;
  jsonResume: object;
  blobResume: object;
  timeRangeCreditsUsage: TimeRange;
  isFirstRender: boolean;
}

export const timeRangeCreditsUsageDefine: TimeRange = {
  start: "",
  end: "",
};

export const DEFAULT_PER_PAGE = 30;

const resumesParamsDefine: ResumesParams = {
  keyword: [],
  page: 1,
  page_size: DEFAULT_PER_PAGE,
  filter: {
    skill: [],
    experience: [],
    location: "",
    language: "",
    candidate_language: "",
    timeRange: {
      start: "",
      end: "",
    },
  },
  showResumesUnlocked: false,
  showWishList: false,
  selectedCandidateID: 0,
};

export const initialParams = resumesParamsDefine;

const shoppingMeDefine: ShoppingMe = {
  resumes_wishlist: new Set(),
  resumes_viewed: new Set(),
  resumes_unlocked: new Set(),
  resumes_noted: new Set(),
  credits: 0,
};

export const useSearchResumesStore = defineStore("search-resumes", {
  state: (): State => ({
    resumesParams: resumesParamsDefine,
    isChange: false,
    isChangeSearch: false,
    isChangeSearchCredits: false,
    shoppingMe: shoppingMeDefine,
    objectDataFile: null,
    jsonResume: null,
    blobResume: null,
    isFirstRender: true,
    timeRangeCreditsUsage: timeRangeCreditsUsageDefine,
  }),
  getters: {
    resumesParamsRequest: (state) => ({
      page: state.resumesParams.page,
      page_size: state.resumesParams.page_size,
      keyword: state.resumesParams.keyword,
      selectedCandidateID: state.resumesParams.selectedCandidateID,
      "filters[skill]": state.resumesParams.filter.skill ?? [],
      "filters[experience]": state.resumesParams.filter.experience,
      "filters[location]": state.resumesParams.filter.location,
      "filters[language]": state.resumesParams.filter.candidate_language,
      "filters[updated_date_from]": state.resumesParams.filter.timeRange.start,
      "filters[updated_date_to]": state.resumesParams.filter.timeRange.end,
      "filters[saved_only]": state.resumesParams.showWishList,
      "filters[unlocked_only]": state.resumesParams.showResumesUnlocked,
    }),
    resumesUnlocked: (state) => state.shoppingMe.resumes_unlocked,
    resumesWishList: (state) => state.shoppingMe.resumes_wishlist,
    resumesViewed: (state) => state.shoppingMe.resumes_viewed,
    resumesNoted: (state) => state.shoppingMe.resumes_noted,
    credits: (state) => state.shoppingMe.credits,
    isFilterByResumeUnlocked: (state) =>
      state.resumesParams.showResumesUnlocked,
    isFilterByWishList: (state) => state.resumesParams.showWishList,
  },
  actions: {
    setResumesParams(params: Partial<ResumesParams>) {
      this.resumesParams = { ...this.resumesParams, ...params };
      if (
        _.isEqual(this.resumesParams.filter, resumesParamsDefine.filter) &&
        this.resumesParams.showWishList === false
      ) {
        this.isChange = false;
      } else {
        this.isChange = true;
      }
      // if (_.isEqual(this.resumesParams.keyword, resumesParamsDefine.keyword)) {
      //   this.isChangeSearch = false;
      // } else {
      this.isChangeSearch = true;
      // }
    },
    resetTimeRangeCreditsUsage() {
      this.timeRangeCreditsUsage = timeRangeCreditsUsageDefine;
      this.isChangeSearchCredits = false;
    },
    async getJsonResumeFormStore(resumeId: number) {
      const { data } = await getJsonResume(resumeId);
      this.jsonResume = data;
    },
    async getThemeResume() {
      const urlFile = await exportThemeResume(this.jsonResume?.template_name, {
        resume: this.jsonResume,
      });
      this.objectDataFile = urlFile;
    },
    async getBlobDataResume(
      typeMedia: "media" | "cvbuilder",
      resumeId: number
    ) {
      let data: any = this.objectDataFile;
      if (typeMedia === "media") data = await downloadResume(resumeId);
      this.blobResume = data;
    },

    resetResumesParams() {
      this.resumesParams = resumesParamsDefine;
      this.isChange = false;
    },

    addNewResumeWishList(id: number) {
      this.shoppingMe.resumes_wishlist.add(id);
    },

    deleteResumeWishList(id: number) {
      this.shoppingMe.resumes_wishlist.delete(id);
    },

    addNewResumesUnlocked(id: number) {
      this.shoppingMe.resumes_unlocked.add(id);
    },

    addNewResumesViewed(id: number) {
      this.shoppingMe.resumes_viewed.add(id);
    },

    addNewResumesNoted(id: number) {
      this.shoppingMe.resumes_noted.add(id);
    },

    updateCredit(newCredit: number) {
      this.shoppingMe.credits = newCredit;
    },

    async getShoppingMe() {
      try {
        const { data } = await fetchShoppingMe();

        this.shoppingMe.resumes_wishlist = new Set(data.resumes_wishlist);
        this.shoppingMe.resumes_viewed = new Set(data.resumes_viewed);
        this.shoppingMe.resumes_unlocked = new Set(data.resumes_unlocked);
        this.shoppingMe.resumes_noted = new Set(data.resumes_noted);
        this.shoppingMe.credits = data.credits;
      } catch (err) {
        throw err;
      }
    },
  },
});
