/**
 * Topdev's Employer Dashboard
 *
 * @version 1.0.0
 */
import { createApp } from "vue";
import { createPinia } from "pinia";
import * as Sentry from "@sentry/vue";
import "bootstrap";
import draggable from "vuedraggable";

import App from "./App.vue";

import { InlineSvg, i18n, Skeletor, VCalendar } from "@/plugins";
import router from "@/router";
import VueApexCharts from "vue3-apexcharts";

const app = createApp(App);
const pinia = createPinia();

Sentry.init({
  app,
  dsn: process.env.MIX_SENTRY_DSN,
});

/**
 * Apply plugin
 */
app.use(pinia);
app.use(router);
app.use(i18n);
app.use(VCalendar);
app.use(VueApexCharts);

/**
 * Apply component
 */
app.component("inline-svg", InlineSvg);
app.component(Skeletor.name, Skeletor);
app.component("draggable", draggable);

/**
 * Finally mount the app
 */
app.mount("#app");
