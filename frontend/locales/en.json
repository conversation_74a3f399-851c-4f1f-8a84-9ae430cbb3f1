{"about_company_dashboard": "Dashboard", "layout_manage_jobs": "Manage Jobs", "layout_manage_candidates": "Manage Candidates", "layout_search_candidates": "Search Candidates", "layout_contact": "Contact for supporting?", "layout_company_profile": "Company Profile", "layout_signout": "Sign Out", "layout_account_settings": "Account <PERSON><PERSON>", "layout_credits_usage": "Unlock(s) usage", "layout_credits_close": "CLOSE", "layout_new_badge": "NEW", "layout_credits_number": "{0} Unlock(s)", "layout_account_top_up_credits": "Top-up Unlock(s)", "layout_account_current_balance": "Your current balance", "layout_credits_usage_content_plus": "Your company has been granted <b>{0} Unlocks</b>", "layout_credits_usage_content_minus": "<b>{0}</b> has unlocked candidate <a target='_blank' href='/search-candidates/{3}' class='text-primary' title='{1}'><b>{1}</b></a> with <b>{2} Unlock(s)</b> in Search Candidate", "layout_credits_usage_topup_cancel": "THANKS, MAYBE LATER!", "layout_credits_usage_topup_send": "SEND INFORMATION", "layout_credits_usage_want_to_topup": "Want to Top-up <PERSON>lock(s)?", "layout_account_your_current_balance": "Your Current balance <span class='color-credits'>{0} Unlock(s)</span>", "layout_account_credits_usage_information_topup": "Or get support with the Unlock(s) top up process from TopDev team by pressing <b>“Send information”</b> button.", "layout_hi": "Hi", "layout_copyright": "Copyright © APPLANCER JOINT STOCK COMPANY", "layout_post_job": "POST JOB", "layout_thanks_maybe_later": "THANKS, MAYBE LATER!", "layout_popup_new_tag": "New Features", "layout_popup_header_description": "Quickly and effectively discover potential candidates (*)", "layout_popup_body_benefit_first": "Access a diverse database of potential candidates.", "layout_popup_body_benefit_second": "Simple and fast search tools support.", "layout_popup_body_benefit_third": "Proactively search and contact suitable candidates.", "layout_popup_body_button": "Explore now", "layout_popup_footer_first": "(*) For more detailed information, visit the Product page of TopDev", "layout_popup_footer_second": "Or contact us directly for immediate support", "layout_notification_bar_title": "Discovering Future Talent - Smart Recruitment with TopDev's <a href='https://topdev.vn/products' class='text-black'><strong><u>CV Search</u></strong></a> Tool", "layout_notification_bar_description": "Receive quick information and consultation about the tool, contact TopDev now", "layout_send_me_information": "SEND ME INFORMATION", "top_bar_company_profile": "Company Profile", "top_bar_dashboard": "Dashboard", "top_bar_contact_information": "Contact Information", "top_bar_note_contact_info": "<b>Note</b>: If there is any information that you would like to change but cannot edit on this page, please <br /> contact us via <a href='mailto:customercare{'@'}topdev.vn'>customercare{'@'}topdev.vn</a> or hotline +************** to request update", "top_bar_all_jobs": "All Jobs", "top_bar_post_job": "Post Job", "top_bar_note_post_job": "<b>Note: </b> If you edit a job with status Open, changes will be displayed after saving and job status will be marked (&lowast;) in 48 hours from the time of change. During that time, TopDev will contact you when we need to check and confirm the information you’ve made", "top_bar_dont_know_how_to_post_job": "View job posting guide for Employer", "pagination_total": "Total {0} items", "pagination_perpage": "/page", "pagination_page": "Page", "pagination_goto": "Go to", "about_company_company_information": "Company Information", "about_company_note_required": "<b>Note:</b> You must fill in the required fields <b class='text-danger'>(&lowast;)</b> to save", "about_company_view_company_site": "VIEW COMPANY SITE", "about_company_cancel": "CANCEL", "about_company_save": "SAVE", "about_company_save_and_view_site": "SAVE & VIEW COMPANY SITE", "about_company_add": "ADD ", "about_company_add_link": "ADD LINK", "about_company_done": "DONE", "about_company_logo": "Logo", "about_company_company_name": "Company Name", "about_company_nationality": "Nationality", "about_company_company_tagline": "Company Tagline", "about_company_company_size": "Company Size", "about_company_introduction": "Introduction", "about_company_industry": "Industry", "about_company_tech_stack": "Tech Stack", "about_company_website": "Website", "about_company_social_media": "Social Media", "about_company_addresses": "Addresses", "about_company_company_benefits": "Company Benefits", "about_company_company_image": "Company Image", "about_company_cover_photo": "Cover photo", "about_company_galleries": "Galleries", "about_company_top_concerns": "Top Concerns", "about_company_company_product": "Company Product", "about_company_product_photo": "Product photo", "about_company_product_name": "Product Name", "about_company_link": "Link", "about_company_description": "Description", "about_company_city_province": "City/Province", "about_company_district": "District", "about_company_ward": "Ward", "about_company_street": "Street", "about_company_types_image": "Types: png, jpg, jpeg. <5MB", "about_company_tell_job_seekers_about_your_company": "Tell job seekers about your company. Your description will appear in the About company tab", "about_company_add_addresses": "Add your company addresses", "about_company_add_company_benefits": "Add company benefits", "about_company_add_the_frequently_ask_questions": "Add the frequently ask questions (FAQs) to help job seekers know more about company & other processes ", "about_company_add_company_product": "Add company product information", "about_company_enter_shortened_and_recognizable_name": "Enter a shortened & recognizable name (Eg. <PERSON>ev)", "about_company_enter_your_company_tag_line": "Enter your Company Tagline (Eg. Top IT Jobs For Developers in Vietnam)", "about_company_add_industry": "Add industry (maximum 10 industries)", "about_company_add_tech_stack": "Add tech stack (maximum 10 tech stacks)", "about_company_add_question": "Add question", "about_company_add_answer": "Add answer", "about_company_pick_icon": "Pick an icon", "about_company_add_benefit_description": "Add benefit description", "contact_information_save_change": "SAVE CHANGE", "contact_information_edit": "EDIT", "contact_information_account_settings": "Account settings", "contact_information_done": "DONE", "contact_information_cancel": "CANCEL", "contact_information_account": "Account", "contact_information_change_password": "Change password", "contact_information_contact_information": "Contact Information", "contact_information_employer_account": "Employer Account", "contact_information_email_address": "Email address", "contact_information_phone": "Phone", "contact_information_no": "No", "contact_information_display_name": "Display name", "contact_information_position": "Position", "contact_information_current_password": "Current password", "contact_information_new_password": "New password", "contact_information_confirm_new_password": "Confirm new password", "contact_information_confirmation_password_does_not_match": "Confirmation password does not match", "contact_information_email_is_required": "Email is a required field", "contact_information_phone_is_required": "Phone is a required field", "contact_information_invalid_phone_number": "Invalid Phone Number", "contact_information_email_must_be_valid": "email must be a valid email", "contact_information_manage_your_account_information": "Manage your account information", "contact_information_total": "Total:", "contact_information_you_havent_post_job_yet": "You haven’t posted any job yet. Start to", "contact_information_now": "now!", "job_list_post_job": "Post Job", "job_list_job_post": "Job post", "job_list_publised_date": "Published date", "job_list_status": "Status", "job_list_applications": "Applications", "job_best_match": "Best Match", "job_list_action": "Action", "job_list_all": "All ", "job_list_review": "Review", "job_list_open": "Open", "job_list_closed": "Closed", "job_list_filter": "Filter", "job_list_search": "Search", "job_list_download": "Download", "job_list_edit": "Edit", "job_list_view": "View", "job_list_dupplicate job post": "Duplicate Job Post", "job_list_apply": "Apply", "job_list_reset": "Reset", "job_list_location": "Location:", "job_list_salary": "Salary:", "job_list_created_by": "Created by", "job_list_views": "View(s):", "job_list_expired_in": "Expired in ..", "job_list_your post in review": "Your post is in review", "job_list_expired": "Expired", "job_list_job_title": "Job Title", "job_list_job_location": "Job Location", "job_list_skills": "Skill(s)", "job_list_you_currently_have_no_jobs_match_your_search": "You currently have no jobs that match your search criteria!<br />Start to Post Job now", "job_list_all_jobs": "All Jobs", "job_list_province_city": "Province/City", "job_list_draft": "Draft", "job_form_post_job": "Post Job", "job_form_edit_job": "Edit Job", "layout_popup_loading_title": "Generating your job", "layout_popup_loading_description": "Please wait a moment while we process your request.", "review_popup_process_done_title": "Your job is created successfully", "review_popup_process_done_description": "Please click the button below to review before publishing.", "review_popup_process_done_body_button": "See job post", "job_form_note_you_must_fill_in_the_required": "<b>Note: </b> You must fill in the required fields <b class='text-danger'>(&lowast;)</b> to save", "job_form_cancel": "CANCEL", "job_form_save_change": "SAVE CHANGE", "job_form_add": "ADD", "job_form_save": "SAVE", "job_form_submit_for_review": "SUBMIT FOR REVIEW", "job_form_negotiable": "NEGOTIABLE", "job_form_this_salary_will_be": "This salary will be displayed with the status \"Negotiable\" on the website topdev.vn", "job_form_title": "Title", "job_form_location": "Location", "job_form_description": "Description", "job_form_salary": "Salary", "job_form_your_role": "Your role & responsibilities", "job_form_your_skills": "Your skills & qualifications", "job_form_year_of_exp": "Year of experience", "job_form_level": "Level", "job_form_job_type": "Job Type", "job_form_skills": "Skills", "job_form_recruitment_process": "Recruitment process", "job_form_job_benefits": "Job Benefits", "job_form_contract_type": "Contract type", "job_form_select_contract_type": "Select contract type", "job_form_input_year_experience_requirement": "Input year of experience requirement. Only the minimum year of experience will be displayed on TopDev website", "job_form_experience_from": "Experience from", "job_form_minimum_year_experience": "Minimum year of experience", "job_form_maximum_year_experience": "Maximum year of experience", "job_form_email_for_applications": "Email for Applications", "job_form_note_for_topdev": "Note for TopDev", "job_form_attract_quality_candidates_by_adding_recruitment_process": "Attract quality candidates by adding a transparent recruitment process", "job_form_attract_more_candidates_by_adding_job_benefits": "Attract more candidates by adding job benefits. If you don’t add benefits for this job, we will show Company benefits", "job_form_job_updated_by": "Job updated by", "job_form_job_creates_by": "Job creates by ", "job_form_add_job_tilte": "Add job title", "job_form_select_the_address_that_has_been_added": "Select the address that has been added to Company profile", "job_form_enter_job_description": "Enter job description", "job_form_competitive_salary_helps": "Competitive salary helps attracting more candidates", "job_form_optional_add_an_approximate_salary_range": "<b>Optional: </b>Add an approximate salary range to help us target suitable candidates. This information won’t be displayed", "job_form_from": "From", "job_form_to": "To", "job_form_enter_salary_range_for_this_position": "Enter salary range for this position to help us target suitable candidates.", "job_form_select_year_of_exp": "Select year of experience", "job_form_select_job_level": "Select job level", "job_form_select_job_type": "Select job type", "job_form_select_job_skills": "Select job skills (maximum 3 skills)", "job_form_maxium_100_characters": "Maximum 100 characters, DO NOT use ALL CAPITAL letter", "job_form_attrct_more_candidates_by_adding_job_benefits": "Attract more candidates by adding job benefits. If you don’t add benefits for this job, we will show Company benefits", "job_form_you_can_add_other_email_addresses_here": "You can add other email addresses here in addition to the employer account(s) that will automatically receive candidates information of this job posting.", "job_form_this_information_wont_be_displayed": "(This information won’t be displayed)", "job_form_let_us_know_your_addition_requirements": "Let’s us know your addition requirements for this job post or candidates", "job_form_enter_your_job_title": "Enter your job title", "job_form_select_your_recruitment_process": "Select your recruitment process", "job_form_enter_your_recruitment_process": "Enter your recruitment process", "job_form_select_your_requirements": "Select your requirements", "job_form_input_your_requirements": "Input your requirements", "job_form_enter_your_requirements": "Enter your requirements", "job_form_enter_your_responsibilities": "Enter your responsibilities", "job_form_select_your_responsibilities": "Select your responsibilities", "job_form_input_your_responsibilities": "Input your responsibilities", "job_form_input_suggest_tag_input": "Suggested", "job_form_select_location": "Select location", "job_form_select_job_lelect": "Select job level", "job_form_select_job_skills_placeholder": "Select job skills (maximum 3 skills)", "job_form_interview_with_line_manager": "Eg. Interview with Line Manager", "job_form_add_description": "Add description", "job_form_lets_job_seeker_know_what_role": "Let’s job seeker know what role or responsibilities they will take for this job s requirements", "job_form_let_job_seeker_know_what_skills": "Let’s job seeker know what skills or qualifications they must have to meet your jobs requirements", "job_form_add_note": "Add note", "job_form_add_multiple_email": "Add multiple email addresses and separate them by pressing enter or using comma", "candidate_list_list_candidates": "List Candidates", "candidate_list_search": "Search", "candidate_list_download": "Download", "candidate_list_reset": "RESET", "candidate_list_apply": "APPLY", "candidate_list_candidate_information": "Candidate information", "candidate_list_job_title": "Job title", "candidate_list_applied_date": "Applied date", "candidate_list_application_status": "Application status", "candidate_list_action": "Action", "candidate_list_skills": "Skill(s)", "candidate_list_filter": "Filter", "candidate_list_you_currently_have_no_candidates": "You currently have no candidates that match your search criteria!", "candidate_list_note": "Note", "candidate_list_province_city": "Province/City", "candidate_list_all_jobs": "All Jobs", "candidate_list_received_cv": "Received CV", "candidate_list_not_matching": "Not matching", "candidate_list_proceed_interview": "Proceed interview", "candidate_list_interviewed": "Interview", "candidate_list_interviewed_passed": "Passed", "candidate_list_interviewed_failed": "Failed", "candidate_list_matched": "Matched", "candidate_list_offer": "Offer", "candidate_list_hired": "<PERSON><PERSON>", "candidate_list_failed": "Failed", "candidate_list_procedure_status": "Procedure status", "candidate_list_select_procedure": "Select procedure", "candidate_detail_download_cv": "Download CV", "candidate_detail_job_applied": "Job applied:", "candidate_detail_applied_date": "Applied date: ", "candidate_detail_cover_letter": "Cover letter", "candidate_detail_view_cv": "View CV", "candidate_detail_add_note": "Add note", "candidate_list_has_cover_letter": "Candidate has cover letter", "candidate_list_no": "No", "candidate_show_best_matched": "Show best matched candidates", "job_list_list_jobs": "List Jobs", "about_company_image_placeholder": "Change  ", "about_company_image_description": "<span class='text-primary cursor'>Browse</span> images to upload", "contact_information_settings": "Settings", "contact_information_contact_message": "Please contact us via <a class='text-primary' href='mailto:customercare{'@'}topdev.vn'>customercare{'@'}topdev.vn</a> or hotline <a class='text-primary' href='tel:+842866567848'>+**************</a> to request update", "contact_information_hide": "HIDE", "contact_information_show": "SHOW", "contact_information_email_placeholder": "Add company email", "contact_information_phone_placeholder": "Add company phone number", "candidate_list_status": "Status", "candidate_list_candidate_location": "Candidate location", "candidate_list_form_date": "From date", "candidate_list_to_date": "To date", "job_list_benefit_icon_placeholder": "Pick an icon", "job_list_benefit_description_placeholder": "Add benefit description", "job_list_expires_one_day": "Expires in 1 day", "job_list_expires_some_days": "Expires in {0} days", "job_list_cant_edit_message": "Sorry! This job has opened for more than 14 days so you can't edit currently. Please contact TopDev via +************** or customercare{'@'}topdev.vn for support.", "job_list_created_at": "Created at:", "job_list_by": "by", "job_list_year_of_experience": "Year of experience:", "job_list_level": "Level:", "job_list_type": "Job Type:", "job_list_description": "Description", "job_list_role": "Your role & responsibilities", "job_list_skill": "Your skills & qualifications", "job_list_benefits": "Job benefits", "job_list_recruitment": "Recruitment process", "job_list_round": "Round", "job_list_email": "Email for Applications", "job_list_note": "Note for TopDev", "job_list_search_placeholder": "Search by ID, job title", "job_form_round": "Round:", "job_form_email_placeholder": "Add multiple email addresses and separate them by pressing enter or using comma", "contact_information_information_should_be_updated": "This information should be updated as your key contact. We will contact you based on this information", "candidate_list_search_placeholder": "Search by candidate, phone number, email", "candidate_list_not_matching_status_describe": "<b>Not matching*:</b> This status has been marked based on the suitability of candidates to the job posting requirements", "candidate_list_best_match_status_describe": "<b>*Best Match:</b> Candidates are sorted into the best fit category based on 3 criteria: years of experience, skills, and location.", "errors_required": "{0} is a required field", "errors_at_least 1 items": "{0} field must have at least 1 items", "errors_invalid_website_url": "Invalid Website URL", "errors_invalid_facebook_url": "Invalid Facebook URL", "errors_invalid_linkedin_url": "Invalid Linkedin URL", "errors_invalid_youtube_url": "Invalid Youtube URL", "errors_invalid_link": "Invalid Link  ", "errors_one_of_two_salary_required": "One of two salary fields must be filled", "errors_from_must_be_larger_than_to": "Salary from must be larger than salary to", "toast_image_not_exceed_5mb": "Image size cannot exceed 5MB", "toast_just_accept_png_jpeg_files": "Just accept png/jpeg files", "toast_save_successfully": "Your data has been successfully saved", "toast_save_failed_message": "Something went wrong. We were unable to complete your request", "toast_email_invalid": "Email is not valid", "toast_finish_form_message": "Please finish your form", "toast_fill_all_information message": "Please fill in all information", "toast_please_try_again": "Please try again", "toast_submission_successful": "Submission successful", "toast_popup_banner_message": "We have received your submission and will contact you soon", "toast_send_me_in_formation": "We have received your request and will contact you soon.", "toast_please_try_again_message": "Please try again.", "toast_send_information_already_request": "You’ve already sent us a request. Please try again tomorrow.", "toast_congrats": "Congrats!", "toast_sorry": "Sorry!", "toast_errors": "Errors", "toast_something_went_wrong": "Something went wrong!", "toast_oops": "Oops!", "toast_warning": "Warning", "toast_upload_failed": "Upload failed", "swal_confirm_havent_saved_your_changes": "You haven't saved your changes yet!", "swal_confirm_changes_not_saved": "Changes you made may not be saved. Are you sure you want to leave without saving?", "swal_confirm_leave_page": "LEAVE PAGE", "swal_confirm_stay_on_this_page": "STAY ON THIS PAGE", "swal_confirm_access_denied": "Access denied!", "swal_confirm_contact_to_active_account": "Your account is Inactive now so all features here are not available. Please contact us via hotline <a href='tel:************'>+8428 6273 3496</a> or email <a href='mailto:contact{'@'}topdev.vn'>contact{'@'}topdev.vn</a> to Activate your employer account.", "swal_confirm_logout": "LOG OUT", "swal_confirm_login": "LOG IN", "swal_confirm_please_login_again": "For your security, account has logged out. Please login again.", "swal_confirm_session_has_expired": "Your session has expired!", "swal_confirm_changes_will_lost": "Changes you made will be permanently lost!", "swal_confirm_action_cannot_undone": "This action cannot be undone. Are you sure you want to discard your changes?", "swal_confirm_discard_changes": "DISCARD CHANGES", "swal_confirm_cancel": "KEEP CHANGES", "swal_confirm_send": "SEND INFORMATION", "swal_confirm_send_information": "Submit unlock(s) top up information", "swal_confirm_action_cannot_undone_send_information": "This action cannot be undone. Are you sure you want to top up unlock(s)?", "swal_confirm_title_change_status_candidates": "Procedure status        ", "swal_confirm_change_status_candidates": "This action cannot be undone. Are you sure you want to change?", "swal_confirm_button_change": "CHANGE", "swal_confirm_button_cancel": "CANCEL", "about_company_other_link": "Other link", "about_company_benefit_icon": "Benefit Icon", "about_company_benefit_description": "Benefit Description", "about_company_question": "Question", "about_company_answer": "Answer", "job_form_benefit_icon": "Benefit Icon", "job_form_benefit_description": "Benefit Description", "job_form_must_less_than_100_characters": "Must be less than 100 characters", "layout_alert_new_system": "🎉 The NEW appearance of TopDev Employer Dashboard is here. Feel free to let us know your feedback via hotline <a href='tel:842866567848'>+**************</a> or email <a href='mailto:customercare{'@'}topdev.vn'>customercare{'@'}topdev.vn</a>", "job_form_benefits_icon_error": "Benefit Icon is a required field", "about_company_benefits_icon_error": "Benefit Icon is a required field", "candidate_detail_loading_text": "CV is loading, you can still click the button in the upper right corner to download and view CV offline", "search_resumes_all_candidates": "All Candidates", "search_resumes_unlock_candidates": "Unlocked candidates", "search_resumes_candidates_match_your_search": "candidates on TopDev matched your search", "search_resumes_candidates_share_resumes": "candidates shared their resumes on TopDev", "search_resumes_show_saved_candidate_only": "Show Saved Candidate Only", "search_resumes_experience": "Experience", "search_resumes_cv_language": "CV’s language", "search_resumes_candidate_location": "Candidate locations", "search_resumes_location": "Location", "search_resumes_more_filters": "More filters", "search_resumes_cv_last_updated": "CV last updated", "search_resumes_candidate_language_proficiency": "Candidate language proficiency", "search_resumes_choose_language": "Choose language", "search_resumes_candidates_language": "Candidates language", "search_resumes_experience_summary": "Experience summary", "search_resumes_contact_information": "Contact information", "search_resumes_expected_salary": "Expected salary:", "search_resumes_last_updated": "Last updated:", "search_resumes_last_active": "Last action:", "search_resumes_days_ago": "days ago", "search_resumes_day_ago": "day ago", "search_resumes_to_day": "Today", "search_resumes_yesterday": "Yesterday", "search_resumes_views": "View(s):", "search_resumes_unlocks": "Unlock(s):", "search_resumes_looking_for_jobs": "Looking for jobs", "search_resumes_candidate_skills": "Skill(s):", "search_resumes_current_position": "Current position:", "search_resumes_work_experience": "Experience:", "search_resumes_education": "Education:", "search_resumes_now": "Now", "search_resumes_credit": "Unlock(s)", "search_resumes_unlock_with_credits": "Unlock now", "search_resumes_paid": "Unlocked", "search_resumes_could_not_find_candidate": "We couldn't find any candidate that matched your search criteria! <br /> Please try using different keywords or remove search filters", "search_resumes_resume_unlock_before": "You’ve already unlocked this candidate", "search_resumes_resume_unlock_success": "You have successfully unlocked this candidate", "search_resumes_toast_not_enough_credit": "Not enough Unlock(s) to unlock this resume", "search_resumes_candidate_from": "Candidate from:", "search_resumes_topdev_search_candidate": "TopDev Search Candidate", "search_resumes_unlock_to_view": "Unlock to view", "search_resumes_credits_number": "{0} Unlock(s)", "search_resumes_unlock_to_download": "Unlock to view", "search_resumes_add": "ADD", "search_resumes_save": "SAVE", "search_resumes_original": "Original", "search_resumes_topdev_format": "TopDev Format", "search_resumes_unlock_resume_html": "<div>\n    <p>View this candidate contact information will cost your Unlock(s). After unlocking, candidate will be saved in <b>Unlocked Candidates</b> tab.</p>\n    <p>Your current balance: <span class='text-warning'>{0} Unlock(s)</span> ( <span class='text-warning'>{1} Unlock(s)</span> will be deducted from your balance )</p>\n    </div>", "search_resumes_can_not_unlock_resume_html": "<div>\n    <p>Your current balance: <span class='text-warning'>{0} Unlock(s)</span> ( You need <span class='text-warning'>{1} Unlock(s)</span> to unlock this candidate )</p>\n    <p>Click <b>'Send me information'</b> button below and TopDev team will support you with the Unlock(s) top up process<p>\n    </div>", "search_resumes_cancel": "CANCEL", "search_resumes_unlock_with_credits_upper": "UNLOCK NOW", "search_resumes_unlock_candidate_information": "Unlock candidate information?", "search_resumes_not_enough_credits": "Not enough Unlock(s)", "search_resumes_i_understand": "I UNDERSTAND", "search_resumes_send_me_information": "SEND ME INFORMATION", "search_resumes_see_more": "See more", "candidate_list_filter_reset": "Reset filter", "search_resumes_save_candidate": "Save candidate", "search_resumes_unsave_candidate": "Unsave candidate", "search_resumes_expired_download_cv": "The download is unavailable because the package has expired.", "search_resumes_note_candidate_info_is_processed_by_expired_at": "- CV PDF download will be disabled after package expiry.", "search_resumes_note_candidate_info_total": "<b>Note:</b> - Candidate information is processed by AI so there may be errors.</br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- CV PDF download will be disabled after package expiry.", "job_form_experience_to_greater_than_from": "\"Minimum year of experience\" must be smaller or equal to \"Maximum year of experience\"", "layout_contact_now": "Contact now", "layout_sign_up_for_consultation": "Sign up for consultation", "candidate_detail_user_has_been_disabled": "User account was disabled", "candidate_detail_cv_deleted_with_user_withdrawal": "The cover letter and CV have been deleted due to the candidate's withdrawal of the application", "candidate_detail_cv_deleted_with_user_be_deleted": "The cover letter and CV have been deleted as the candidate has deleted their account", "candidate_detail_application_has_been_withdrawn": "Application has been withdrawn", "search_resumes_edit_button": "Edit", "search_resumes_delete_button": "Delete", "search_resumes_view_all": "View All", "candidate_detail_unlock_to_view": "Unlock to view", "candidate_list_year": "year", "candidate_list_years": "years", "swal_confirm_unlock_now": "Unlock now", "swal_confirm_thanks_maybe_later": "Thanks, maybe later!", "swal_confirm_available_unlock_title": "<div>\n    <h2 class='swal2-title'>Unlock candidate information?</h2>\n    <p>After unlocking, candidate will be saved in <b>Unlocked Candidates</b> tab.</p>\n    <p>Your current balance: <span class='text-warning'>{0} Unlock(s)</span><br />\n    Unlock this candidate with <span class='text-warning'>{1} Unlock(s)</span> </p>\n    </div>", "swal_confirm_not_enough_credits": "<div>\n    <h2 class='swal2-title'>Not enough Unlock(s)</h2>\n    <p>Your current balance: <span class='text-warning'>{0} Unlock(s)</span> <br/>\n    <span>(You need <span class='text-warning'>{1} Unlock(s)</span> to unlock this candidate)</span>\n    </p>\n    <p>Click \"<strong>Send me information</strong>\" button below and TopDev team will support you with the Unlock(s) top up process.<p>\n    </div>", "layout_credit_management": "Unlock Candidate Management", "credit_management_credit_package": "Unlock(s) package", "credit_management_buy_more_credits": "Buy more Unlock(s)", "credit_management_granted_credits_description": "Your company has been <span class='font-bold highlight-text'>granted Unlock(s)</span>", "credit_management_unlocked_condidate_description": "<span class='employer-name'>{0}</span> has unlocked candidate <span class='target-name'>{1}</span>", "credit_management_refunded_credits_description": "Your company has been <span class='font-bold highlight-text'>returned Unlocks</span>", "candidate_list_work_experience": "Work experience", "candidate_list_all_skills": "All skills", "candidate_list_all_experiences": "All experiences", "candidate_list_all_locations": "All locations", "candidate_list_all_languages": "All languages", "search_resumes_search_resume_placeholder": "Search by keywords and separate them with a comma", "search_resumes_last_update_from": "Laste updated from", "search_resumes_to": "to", "search_resumes_willing_to_work_status": "Candidate turned off \"Open to work\" status", "search_resumes_request_refund": "<PERSON><PERSON><PERSON>", "search_resumes_request_refund_for_search_resume_profile": "For candidate:", "search_resumes_request_refund_reason_placeholder": "Please write down your feedback", "search_resumes_request_refund_notes": "Note:", "search_resumes_request_refund_note_items_1": "Please send feedback to TopDev if there is any mistakes in the candidate's contact information or job search status", "search_resumes_request_refund_confrm": "Send feedback", "swal_confirm_request_refund_cancel": "Cancel", "toast_request_refund_success_message": "Your request sent successfully", "toast_request_refund_failed_message": "Request cannot be sent. Please try again in a few minutes.", "search_resumes_show_saved_candidate": "Show saved candidates", "search_resumes_language_english": "English", "search_resumes_language_japan": "Japan", "search_resumes_language_chinese": "Chinese", "search_resumes_language_korean": "Korean", "search_resumes_language_french": "French", "search_resumes_language_german": "German", "search_resumes_unlock_label": "Unlock", "search_resumes_candidate_address": "Address", "search_resumes_candidate_yoe": "YOE", "search_resumes_candidate_experience": "Candidate experience", "search_resumes_confirm_unlock": "Confirm", "search_resumes_cancel_unlock": "Cancel", "search_resumes_current_number_of_opening": "Your remaining unlocks:", "search_resumes_click_confirm_to_unlock_and_view_candidate_detail": "Click <span class='font-bold'>Confirm</span> to unlock and view candidate's contact infomation.", "search_resumes_send_notification": "Send information", "search_resumes_you_are_not_enough_credits": "You do not have enough balance to unlock the candidate. Please press “Send information” to receive <b>support from the TopDev team</b>.", "search_resumes_search_by_keywords": "Search by keywords", "credit_management_credits_availables": "Unlock(s) availables", "credit_management_your_company_has_expired_unlock": "Your company has <span class='font-bold highlight-text'>expired Unlock(s)</span>", "credit_management_package_paid": "Package", "credit_management_status": "Status", "credit_management_purchased_date": "Purchased date", "credit_management_activated_date": "Activated date", "credit_management_expired_date": "Expired date", "credit_management_usage_log": "Usage log", "credit_management_credit": "Unlock(s)", "credit_management_credits": "Unlock(s)", "search_resumes_present": "Present", "swal_confirm_waiting_approved_ok": "Ok", "swal_confirm_waiting_approved": "Your account is currently awaiting Approval, so this feature is not available. Please contact TopDev via hotline <a hreft='tel:+************'>+8428 6273 3496</a> or email <a href='mailto:contact{'@'}topdev.vn'>contact{'@'}topdev.vn</a> to approve your employer account.", "search_resumes_note_candidate_info_is_processed_by_ai": "<b>Note:</b> Candidate information is processed by AI so there may be errors.", "candidate_list_skill_match": "Skill Match", "candidate_list_skill_match_status_description": "<b>Skill Match:</b> TopDev counts how many skills in candidate CV which match your JD. For example, 3 / 5 means there are 3 skills in candidate CV which match 5 required skill in the JD", "search_resumes_max_allow_unlock_before": "Your actions are quite rushed. Please slow down & check candidates carefully.", "search_resumes_max_allow_click_before": "Your actions are quite rushed. Please slow down & check candidates carefully.", "candidate_list_sort": "Sort", "credit_management_action": "Action", "credit_management_active_button": "Activate", "credit_management_active_unlock_package_confirm": "Please press <b>Confirm</b> to activate and start using the candidate unlocks.", "swal_confirm_activate_button": "Confirm", "credit_management_buy_now": "Buy now", "layout_click_buy_now_button": "Please press <b>“Buy now”</b> button below to get more", "search_resumes_having_inactive_unlock_package": "You currently have <span style='color: #0F5E1C'><b>{0} inactive unlock package(s)</b></span>. Activate them now to start using.", "search_resumes_activate_button": "Activate now", "search_resumes_do_not_having_inactive_unlock_package": "You currently <span style='color: #CE8800'><b>don't have any available unlock packages</b></span>. Buy now to use them. ", "search_resumes_contact_topdev_team_to_be_supported": "Or press <b>“Send information”</b> to receive support from the TopDev team.", "search_resumes_send_information": "Send information", "search_resumes_buy_unlock_package": "Buy more Unlock(s)", "search_resumes_view_less": "See less", "credit_management_activate_inactive_unlock_package_success": "You have been successfully activated this package", "candidate_list_matching_status": "Matching", "candidate_list_cover_letter": "Has cover letter", "candidate_list_not_started": "Not started", "candidate_list_filter_procedure_status": "Procedure status", "candidate_list_interviewed_passed_option": "Passed interview", "candidate_list_interviewed_failed_option": "Failed interview", "candidate_list_best_match_title": "Best Match Badge", "candidate_list_best_match_description": "Earned if candidates meet location, years of experience, and at least 3 job skills.", "candidate_list_other": "Other", "candidate_list_best_match": "Best Match", "candidate_tab_best_match": "Best Match", "candidate_tab_other": "Other", "candidate_tab_all": "All", "layout_job_posting_management": "Job Posting", "layout_my_product_management": "<PERSON><PERSON>", "usage_history_get_more_products": "Get more products", "usage_history_my_product_description": "Manage your Order History, Job Posting and Search Candidates at Topdev.vn.", "usage_history_job_posting_description": "Manage your job posting packages here. Where you can see your available job posting’s package", "usage_history_job_posting_available_title": "Available Job Postings", "usage_history_job_posting_usage_log": "Usage log", "usage_history_job_posting_available_package_text": "(packages)", "usage_history_job_posting_table_invoice_header": "Invoice", "usage_history_job_posting_table_paid_package_number_header": "Total Job Postings Purchased", "usage_history_job_posting_table_paid_at_header": "Purchased Date", "usage_history_job_posting_table_paid_package_header": "Package", "usage_history_job_posting_table_paid_each_package_header": "Job Postings per Package", "usage_history_job_posting_table_used_each_package_header": "Job Postings Used", "usage_history_job_posting_table_remain_each_package_header": "Job Postings Remaining", "usage_history_job_posting_table_expired_at_header": "Expired Date", "usage_history_job_posting_usage_log_use_package": "Package", "usage_history_job_posting_usage_log_use_description": "Your company has been posted <b>{0}</b>", "usage_history_job_posting_package_top_job_title": "Top Job", "usage_history_job_posting_package_distinction_job_title": "Distinction Job", "usage_history_job_posting_package_basic_plus_job_title": "Basic Plus Job", "usage_history_job_posting_package_basic_job_title": "Basic Job", "usage_history_job_posting_package_gift_title": "(Promotion)", "job_detail_package_select": "Job posting package", "job_detail_package_select_placeholder": "Select 1 package to apply for this job", "usage_history_job_posting_not_found_invoices": "You currently have no invoices!", "usage_history_job_posting_not_found_usage_logs": "You currently have no usage logs!", "usage_history_job_posting_usage_log_use_packages": "Packages", "usage_history_job_posting_quota_package_title": "You are out of Job Posting package", "usage_history_job_posting_quota_package_info": "Your available Job Posting Package:", "usage_history_job_posting_quota_package_send_info": "Click <b>Buy now</b> to get more and post your job.<br>Or <b>Send information</b> to request a support from TopDev Team.", "usage_history_job_posting_out_quota_package_title": "Your package is not available", "usage_history_job_posting_out_quota_package_warning": "Someone in your company has just used this package.", "usage_history_job_posting_quota_package_number": "0 package", "usage_history_job_posting_package_is_not_available": "Someone in your company has just used this package. Choose another package!", "candidate_list_under_one_year": "Under 1 year", "candidate_list_one_year": "1 year", "candidate_list_two_year": "2 years", "candidate_list_three_year": "3 years", "candidate_list_four_year": "4 years", "candidate_list_five_year": "5 years", "candidate_list_six_year": "6 years", "candidate_list_seven_year": "7 years", "candidate_list_eight_year": "8 years", "candidate_list_nine_year": "9 years", "candidate_list_ten_year": "10 years", "candidate_list_above_ten_year": "Above 10 years", "candidate_list_experiences": "Experience(s)", "usage_history_job_posting_quota_package_expired_at": "expired at {0}", "about_company_upload_enterprise_registration_certificate": "Upload Enterprise Registration Certificate", "about_company_upload_this_to_verify_your_business_legitimacy": "Upload this to verify your business legitimacy. Please note that the document is not published on our site and be safe with us", "about_company_browse_folder": "Browse folder", "about_company_support_pdf_and_file_ls_5mb": "Support *.pdf, and file &lt; 5MB", "about_company_your_erc_is_being_checked": "Your ERC is being checked. Please wait for confirmation", "about_company_your_erc_is_not_approved": "Your ERC is not approved. Please reupload", "about_company_your_erc_is_approved": "Your ERC is approved. Now you can use TopDev services", "about_company_replace_file": "Replace File", "about_company_erc": "ERC", "store_job_already_have_job_free_open": "You are only allowed to post one free listing at a time.", "store_job_already_have_job_free_open_quota": "You have reached the limit of the number of free posts allowed at one time.", "customer_free_plan_modal_title": "Upgrade to Premium", "customer_free_plan_modal_message_1": "Your free plan only allows you to post 1 IT job and 5 non-IT job postings at a time. Please upgrade to Premium to post more or wait until your current post is expired.", "customer_free_plan_modal_message_2": "Plan to upgrade? Call our customer service at <span class='text-[#5d5d5d] font-bold'>0888 1555 00</span> for more information or; leave us your contact information, we will contact you", "customer_free_plan_form_fullname": "Full name", "customer_free_plan_form_company_name": "Company name", "customer_free_plan_form_phone_number": "Phone number", "customer_free_plan_form_request_button": "Request a call", "customer_free_plan_form_hint": "Click <span class='text-[#5d5d5d] font-bold'>Request a call</spab>, TopDev will contact you soon", "customer_free_plan_form_requested": "Request successfully. TopDev will contact you soon", "customer_free_plan_upgrade_modal_title": "Upgrade to Premium", "customer_free_plan_upgrade_message_1": "TopDev will push your post rank upfront in search result and help design your own customize design JD.", "customer_free_plan_upgrade_message_2": "Call our customer service at <span class='text-[#5d5d5d] font-bold'>0888 1555 00</span> for more information or; leave us your contact information, we will contact you", "hr_dashboard_application_time_series": "Application Time Series", "hr_dashboard_application_today": "Applications Today", "hr_dashboard_total_applications": "Total Applications", "hr_dashboard_not_viewed_applications": "Not Viewed Applications", "hr_dashboard_viewed_applications": "Viewed Applications", "hr_dashboard_total_views": "Total Views", "hr_dashboard_today": "Today", "hr_dashboard_last_seven_days": "Last 7 days", "hr_dashboard_last_thirty_days": "Last 30 days", "hr_dashboard_custom": "Custom", "hr_dashboard_job_titles": "Job titles", "hr_dashboard_application_by_status": "Application By Status", "hr_dashboard_package_balance": "Package Balance", "hr_dashboard_all_premium_jobs_by_status": "All premium jobs by status", "hr_dashboard_all_remaining_post": "All remaining post", "hr_dashboard_expire_in_seven_days": "Expire in 7 days", "hr_dashboard_all_remaining_unlock": "All remaining unlocks", "hr_dashboard_open": "Open", "hr_dashboard_closed": "Closed", "hr_dashboard_review": "Review", "hr_dashboard_draft": "Draft", "hr_dashboard_apply": "Apply", "hr_dashboard_reset": "Reset", "hr_dashboard_premium_job": "Premium Job Post", "hr_dashboard_search_candidate": "Search Candidate", "hr_dashboard_pass_cv_screen": "Pass CV screen", "hr_dashboard_cv_not_matched": "CV not matched", "hr_dashboard_interview_appointment": "Interview appointment", "hr_dashboard_unviewed": "Unviewed", "hr_dashboard_viewed": "Viewed", "hr_dashboard_title": "Dashboard", "hr_dashboard_cv_applied": "CV Applied", "job_posting": {"limit_reached": "You have reached the maximum limit of {max} active non-IT jobs. Please close some jobs before posting new ones. (Current: {current}/{max})", "free_notification_title": "Free Job Posting", "free_notification_description": "You can post jobs for free if you don't have a package. Your job will be live for 30 days.", "it_quota_exceeded": "You have reached the maximum number of free IT job postings. You have used {current} out of {max} allowed IT job postings. Please upgrade to a paid package to post more IT jobs."}, "job_form_see_how_to_add_package": "Don’t know how to Post Job?", "job_form_no_available_packages": "You don't have any available packages. Please contact our sales team for more information.", "job_form_free_available_packages": "You are allowed to post {freeITJobOpening} free IT job(s) and {freeNonITJobOpening} non-IT job(s)", "job_form_free_job_message": "This is a Free Job. You cannot change the package type.", "post_job_tooltip": "Don’t know how to Post Job?", "job_package": {"free_job_quota_request": {"title": "Request Free Job Quota", "description": "You have used all your free job postings. Please fill out the form below to request additional free job quotas.", "name_label": "Your Name", "name_placeholder": "Enter your full name", "company_label": "Company Name", "company_placeholder": "Enter your company name", "phone_label": "Phone Number", "phone_placeholder": "Enter your phone number", "reason_label": "Reason for Request", "reason_placeholder": "Please explain why you need additional free job postings", "submit_button": "Submit Request", "cancel_button": "Cancel", "success_message": "Your request has been submitted successfully. We will review your request and get back to you soon.", "error_message": "Failed to submit your request. Please try again later.", "error_network": "Network error. Please check your connection and try again.", "validation": {"name_required": "Please enter your name", "company_required": "Please enter your company name", "phone_required": "Please enter your phone number", "phone_invalid": "Please enter a valid phone number", "reason_required": "Please provide a reason for your request"}}, "request_quota_button": "Request Quota", "quota_exceeded": "Free quota exceeded", "title": "Job Posting Package", "loading": "Loading packages...", "package": "Package", "po_number": "Invoice Number", "expiry_date": "Expiry Date", "free_posting": "Free posting available", "renew": "<PERSON>w", "remaining": "Total free job postings", "select_package": "Select Package", "no_package_available": "No package available", "contact_support": "Please contact our support team for more information."}, "========": "KEEP THIS AS LAST LINE & NEVER DELETE OR UPDATE THIS LINE"}