import * as yup from "yup";
import { computed } from "vue";
import { translate } from "@/helpers";
import {
  regFacebook,
  regLinkedin,
  regWebsite,
  regYoutube,
} from "@/config/regex";
import { unleash } from "@/plugins";
import { FREE_POST } from "@/plugins/unleash";

export const formAddressesSchema = computed(() =>
  yup.object({
    id: yup.number().nullable(),
    province_id: yup
      .string()
      .required()
      .label(translate("about_company_city_province")),
    district_id: yup
      .string()
      .nullable()
      .label(translate("about_company_district")),
    ward_id: yup.string().nullable().label(translate("about_company_ward")),
    full_address: yup.string().nullable(),
    street: yup.string().nullable().label(translate("about_company_street")),
  })
);

export const formProductSchema = computed(() =>
  yup.object({
    id: yup.string().nullable(),
    name: yup
      .string()
      .required()
      .label(translate("about_company_product_name")),
    description: yup
      .string()
      .required()
      .label(translate("about_company_description")),
    image: yup
      .object({
        id: yup.number().nullable(),
        url: yup.string().nullable(),
      })
      .nullable(),
    link: yup.string().nullable().label(translate("about_company_link")),
  })
);

export const companyProfileSchema = computed(() => {
  let companyProfileSchema: any = {
    display_name: yup
      .string()
      .required()
      .label(translate("about_company_company_name")),
    image_logo: yup.string().required().label(translate("about_company_logo")),
    tagline: yup
      .string()
      .required()
      .label(translate("about_company_company_tagline")),
    nationalities: yup
      .array()
      .of(yup.string())
      .required()
      .min(1)
      .label(translate("about_company_nationality")),
    num_employees: yup
      .mixed()
      .required()
      .label(translate("about_company_company_size")),
    description: yup
      .string()
      .required()
      .label(translate("about_company_introduction")),
    industries_ids: yup
      .array()
      .of(yup.number())
      .required()
      .min(1)
      .label(translate("about_company_industry")),
    skills_ids: yup
      .array()
      .of(yup.number())
      .required()
      .min(1)
      .label(translate("about_company_tech_stack")),
    website: yup
      .string()
      .matches(regWebsite, {
        message: "errors_invalid_website_url",
        excludeEmptyString: true,
      })
      .label(translate("about_company_website")),
    social_network: yup.object({
      facebook: yup
        .string()
        .matches(regFacebook, {
          message: "errors_invalid_facebook_url",
          excludeEmptyString: true,
        })
        .nullable()
        .label("Facebook"),
      linkedin: yup
        .string()
        .matches(regLinkedin, {
          message: "errors_invalid_linkedin_url",
          excludeEmptyString: true,
        })
        .nullable()
        .label("Linkedin"),
      youtube: yup
        .string()
        .matches(regYoutube, {
          message: "errors_invalid_youtube_url",
          excludeEmptyString: true,
        })
        .nullable()
        .label("Youtube"),
      share_alt: yup
        .string()
        .matches(regWebsite, {
          message: "errors_invalid_link",
          excludeEmptyString: true,
        })
        .nullable()
        .label(translate("about_company_other_link")),
    }),
    addresses: yup
      .array()
      .of(formAddressesSchema.value)
      .ensure()
      .min(1)
      .max(10)
      .label(translate("about_company_addresses")),
    benefits: yup
      .string()
      .required()
      .label(translate("about_company_company_benefits")),
    image_cover: yup.string().label(translate("about_company_cover_photo")),
    image_galleries: yup.array().of(
      yup.object({
        id: yup.number().nullable(),
        url: yup.string().nullable(),
      })
    ),
    faqs: yup
      .array()
      .of(
        yup.object({
          question: yup
            .string()
            .required()
            .label(translate("about_company_question")),
          answer: yup
            .string()
            .required()
            .label(translate("about_company_answer")),
        })
      )
      .label(translate("about_company_top_concerns")),
    products: yup.array().of(formProductSchema.value),
  };

  companyProfileSchema = {
    ...companyProfileSchema,
    erc_file: yup.string().required().label(translate("about_company_erc")),
    erc_filename: yup.mixed().nullable(),
    status: yup.mixed().nullable(),
  }

  return yup.object(companyProfileSchema);
});

export const COMPANY_STATUS_ACTIVE = 1;
export const COMPANY_STATUS_INACTIVE = 2;
export const COMPANY_STATUS_REVIEW = 3;
export const COMPANY_STATUS_WAITING = 4;
