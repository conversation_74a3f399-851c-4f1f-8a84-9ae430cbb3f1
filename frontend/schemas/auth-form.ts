import { object, ref, string } from "yup";
import { computed } from "vue";
import { translate } from "@/helpers";

export const changePasswordFormSchema = computed(() =>
  object({
    current_password: string()
      .required()
      .label(translate("contact_information_current_password")),
    new_password: string()
      .required()
      .label(translate("contact_information_new_password")),
    confirm_password: string()
      .oneOf(
        [ref("new_password"), null],
        "contact_information_confirmation_password_does_not_match"
      )
      .required()
      .label("contact_information_confirm_new_password"),
  })
);

export const accountFormSchema = object({
  full_name: string().nullable(),
  position: string().nullable(),
  phone: string(),
});

export const changeEmailPhoneAccountFormSchema = object({
  email: string().required().email().label("Email"),
  phone: string(),
});

export const initialChangePasswordFormValues = {
  current_password: "",
  new_password: "",
  confirm_password: "",
};
