import * as yup from "yup";
import { computed } from "vue";
import { translate } from "@/helpers";
import { useTaxonomiesStore } from "@/stores";

export const jobFormSchemaObject = {
  title: yup.string().required().label(translate("job_list_job_title")),
  content: yup.string().nullable().label(translate("job_form_description")),
  requirements: yup
    .array()
    .of(
      yup.object({
        id: yup.number(),
        name: yup.string(),
        description: yup.string(),
      })
    )
    .required()
    .label(translate("job_form_your_skills")),
  responsibilities: yup
    .array()
    .of(
      yup.object({
        id: yup.number(),
        name: yup.string(),
        description: yup.string(),
      })
    )
    .required()
    .label(translate("job_form_your_role")),
  addresses_id: yup
    .array()
    .of(yup.number())
    .min(1)
    .required()
    .label(translate("job_form_location")),
  salary: yup.object({
    value: yup.string(),
    currency: yup.string(),
    unit: yup.string(),
    is_negotiable: yup.boolean().required(),
    min: yup.mixed().when("is_negotiable", {
      is: 0,
      then: yup.mixed().when("max", {
        is: null,
        then: yup.number().typeError("errors_one_of_two_salary_required"),
      }),
    }),
    max: yup.mixed().when("is_negotiable", {
      is: 0,
      then: yup.mixed().when("min", {
        is: (min) => !Number.isNaN(min) && min != undefined,
        then: yup
          .number()
          .nullable()
          .positive()
          .min(yup.ref("min"), "errors_from_must_be_larger_than_to"),
      }),
    }),
    min_estimate: yup.mixed().when("is_negotiable", {
      is: 1,
      then: yup.mixed().when("max_estimate", {
        is: null,
        then: yup.number().typeError("errors_one_of_two_salary_required"),
      }),
    }),
    max_estimate: yup.mixed().when("is_negotiable", {
      is: 1,
      then: yup.mixed().when("min_estimate", {
        is: (min_estimate) =>
          !Number.isNaN(min_estimate) && min_estimate != undefined,
        then: yup
          .number()
          .nullable()
          .positive()
          .min(yup.ref("min_estimate"), "errors_from_must_be_larger_than_to"),
      }),
    }),
  }),
  experiences_ids: yup.object({
    from: yup
      .mixed()
      .required()
      .test(
        "is-greater-or-equal",
        translate("job_form_experience_to_greater_than_from"),
        function (value) {
          const { to } = this.parent;
          if (!value || !to) {
            return true;
          }

          const taxonomiesStore = useTaxonomiesStore();
          let indexExperienceFromAll = taxonomiesStore.experiences.findIndex(
            (item: any) => item.value == process.env.MIX_EXPERIENCE_FROM_ALL_ID
          );
          let fromExpIndex = taxonomiesStore.experiences.findIndex(
            (item) => item.value == value
          );
          let toExpIndex = taxonomiesStore.experiences.findIndex(
            (item) => item.value == to
          );

          if (fromExpIndex === indexExperienceFromAll) return true;
          return fromExpIndex <= toExpIndex;
        }
      )
      .label(translate("job_form_experience_from")),
    to: yup.mixed().label(translate("job_form_experience_from")),
  }),
  job_levels: yup
    .array()
    .of(yup.number())
    .min(1)
    .required()
    .label(translate("job_form_level")),
  job_types: yup
    .array()
    .of(yup.number())
    .min(1)
    .required()
    .label(translate("job_form_job_type")),
  contract_type: yup
    .array()
    .of(yup.number())
    .min(1)
    .required()
    .label(translate("job_form_contract_type")),
  skills_ids: yup
    .array()
    .of(yup.number())
    .min(1)
    .max(5)
    .required()
    .label(translate("job_form_skills")),
  recruiment_process: yup
    .array()
    .of(
      yup.object({
        name: yup
          .string()
          .test(
            "len",
            "job_form_must_less_than_100_characters",
            (val) => val.length < 100
          ),
      })
    )
    .max(8)
    .min(0)
    .nullable()
    .label(translate("job_form_recruitment_process")),
  emails_cc: yup
    .array()
    .of(yup.string().email().label("Email"))
    .nullable()
    .label("Email"),
  note: yup.string().nullable().label(translate("job_form_note_for_topdev")),
  job_status_id: yup.number().nullable(),
  package_id: yup
    .string()
    .nullable()
    .typeError(translate("job_detail_package_select"))
    .label(translate("job_detail_package_select"))
    .when("level", {
      is: "paid",
      then: yup
        .string()
        .transform((value) => null == value ? undefined : value )
        .required(),
    }),

  level: yup.string().oneOf(["free", "paid"]).required(),

  category_id: yup
    .number()
    .transform((value) => Number.isNaN(value) ? undefined : value )
    .required()
    .label("Job Category"),
  job_category_id: yup.array().of(yup.number()).min(1).required().label("Job Role"),
  education_degree: yup.array().of(yup.number()),
  education_major: yup.array().of(yup.number()),
  education_certificate: yup.string().nullable(),
  benefits: yup.array().of(
    yup.object({
      id: yup.number(),
      description: yup.string(),
    })
  ),

  job_banner: yup.number().nullable().when("level", {
    is: "paid",
    then: yup.number().required(),
  }),
  job_template: yup.number().nullable().when("level", {
    is: "paid",
    then: yup.number().required(),
  }),
  job_template_color: yup.number().nullable().when("level", {
    is: "paid",
    then: yup.number().required(),
  }),

  company_tagline: yup.string().nullable(),
  company_logo: yup.string().nullable(),
};

export const jobFormSchema = computed(() => yup.object(jobFormSchemaObject));

export const defaultJobValues = {
  title: "",
  content: "",
  requirements: [],
  responsibilities: [],
  addresses_id: [],
  salary: {
    currency: "VND",
    unit: "MONTH",
    min: null,
    max: null,
    min_estimate: null,
    max_estimate: null,
    is_negotiable: 0,
  },
  experiences_ids: {
    from: null,
    to: null,
  },
  contract_type: [],
  job_levels: [],
  job_types: [],
  skills_ids: [],
  recruiment_process: [],
  benefits: [],
  emails_cc: [],
  note: "",
  package_id: null,
  level: "free",
  job_status_id: 2,
  category_id: null,
  job_category_id: [],
  education_degree: [],
  education_major: [],
  education_certificate: "",

  job_banner: null,
  job_template: null,
  job_template_color: null,

  company_tagline: null,
  company_logo: null,
};

export const localJobValues = {
  title: "Job test free",
  content: "<p>Job description</p>",
  requirements: [
    {
      id: 9249,
      name: "Industry Knowledge",
      description:
        "Familiarity with specific industries (e.g., e-commerce, finance, healthcare) can enhance design relevance and effectiveness.",
    },
    {
      id: 9250,
      name: "Interpersonal Skills",
      description: "ability to relate, communicate and work with others",
    },
    {
      id: 9341,
      name: "Programming Languages",
      description: "Proficiency in one or more programming languages",
    },
    {
      id: 9344,
      name: "Version Control",
      description:
        "Proficiency with version control systems (e.g., Git) for managing code and configurations.",
    },
    {
      id: 9351,
      name: "Cloud Services",
      description:
        "Experience with Docker/Kubernetes, Cloud Infrastructure and CICD is a plus",
    },
  ],
  responsibilities: [
    {
      id: 9265,
      name: "Requirement Analysis",
      description: "Gather and analyze user requirements.",
    },
    {
      id: 9302,
      name: "Code Review",
      description:
        "Review code written by peers for quality and adherence to standards.",
    },
    {
      id: 9303,
      name: "Testing",
      description:
        "Develop and execute unit tests, integration tests, and system tests.",
    },
    {
      id: 9304,
      name: "Debugging",
      description: "Identify and fix bugs in the application.",
    },
    {
      id: 9305,
      name: "Documentation",
      description: "Document code and systems for future reference.",
    },
  ],
  addresses_id: [184126],
  salary: {
    value: "Negotiable",
    currency: "VND",
    unit: "MONTH",
    min: "2000",
    max: "3000",
    min_estimate: null,
    max_estimate: null,
    is_negotiable: 0,
  },
  experiences_ids: {
    from: 1642,
    to: 1644,
  },
  contract_type: [1622, 4621],
  job_levels: [1616, 1617, 1620],
  job_types: [8792, 8642],
  skills_ids: [1, 10, 100, 101],
  recruiment_process: [
    {
      id: 9271,
      name: "Phone screen",
      description: "Phone screen",
    },
    {
      id: 9321,
      name: "Technical Assessment",
      description: "Technical Assessment",
    },
    {
      id: 9322,
      name: "Technical Interview",
      description: "Technical Interview",
    },
    {
      id: 9323,
      name: "Interview with HR",
      description: "Interview with HR",
    },
  ],
  benefits: [
    {
      id: 9268,
      name: "Interpersonal Skills",
      description: "ability to relate, communicate and work with others",
    },
    {
      id: 9269,
      name: "Industry Knowledge",
      description:
        "Familiarity with specific industries (e.g., e-commerce, finance, healthcare) can enhance design relevance and effectiveness.",
    },
    {
      id: 9280,
      name: "offered in some technical leadership positions",
      description: "offered in some technical leadership positions",
    },
    {
      id: 9282,
      name: "Health and Insurance",
      description:
        "medical coverage, dental, social insurance, family coverage.",
    },
  ],
  emails_cc: ["<EMAIL>", "<EMAIL>"],
  note: "Note for topdev",
  package_id: null,
  level: "free",
  job_status_id: 2,
  role: 9257,
  education_degree: [9251, 9252],
  education_major: [9319],
  education_certificate: "PHP cert",
  job_banner: null,
  job_template: null,
  job_template_color: null,
  company_tagline: null,
  company_logo: null,
};
