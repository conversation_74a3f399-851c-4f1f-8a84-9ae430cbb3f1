<template>
  <AppBlockUI />
  <div class="alert alert-warning topdev-new-alert" v-if="showAlert">
    <span
      class="alert-title"
      v-html="translate('layout_alert_new_system')"
    ></span>
    <button
      type="button"
      class="btn-close"
      aria-label="Close"
      @click="layoutStore.hideAnnouncementBar()"
    ></button>
  </div>

  <!-- begin:: Body -->
  <div class="page d-flex flex-row flex-column-fluid">
    <!-- begin:: Aside Left -->
    <KTAside :class="{ 'topdev-alert-show': showAlert }" />
    <!-- end:: Aside Left -->

    <!-- The notification bar  -->
    <div v-if="authStore.isLogged && !authStore.user.is_unlocked">
      <TheNotificationBarNotApprove />
    </div>
    <div v-if="authStore.isLogged && authStore.user.is_unlocked">
      <TheNotificationBar />
    </div>
    <!-- end the notification bar  -->

    <div
      id="kt_wrapper"
      class="d-flex flex-column flex-row-fluid wrapper wrapper-custom"
      :class="{ 'topdev-alert-show': showAlert }"
    >
      <KTHeader :class="{ 'topdev-alert-show': showAlert }" />

      <!-- begin:: Content -->
      <div id="kt_content" class="d-flex flex-column flex-column-fluid">
        <!-- begin:: Content Body -->
        <div id="kt_content_container" class="layout-content">
          <router-view />
        </div>
        <!-- end:: Content Body -->
        <TheFooter />
      </div>
      <!-- end:: Content -->
    </div>
  </div>
  <!-- end:: Body  -->
  <div v-if="authStore.isLogged">
    <PopupNewFeature />
    <PopupNewFeatureCustomerNotApprove />
    <AppModalAccountSettings />
    <AppModalCreditsUsage />
    <AppModalTopUpCreditsUsage />
    <AppModalTopUpPackagesUsage />
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, computed } from "vue";

import AppBlockUI from "@/components/AppBlockUI.vue";
import PopupNewFeature from "./components/ThePopupNewFeature.vue";
import PopupNewFeatureCustomerNotApprove from "./components/ThePopupNewFeatureCustomerNotApprove.vue";
import AppModalAccountSettings from "@/components/AppModalAccountSettings.vue";
import AppModalCreditsUsage from "@/components/AppModalCreditsUsage.vue";
import AppModalTopUpCreditsUsage from "@/components/AppModalTopUpCreditsUsage.vue";
import AppModalTopUpPackagesUsage from "@/components/AppModalTopUpPackagesUsage.vue";
import KTAside from "@/layout/aside/Aside.vue";
import KTHeader from "@/layout/header/TheHeader.vue";
import TheFooter from "@/layout/footer/TheFooter.vue";

import { useAuthStore, useLayoutStore } from "./stores";
import { setLocale } from "yup";
import { initializeComponents } from "@/plugins";
import { showWarningToast, translate } from "@/helpers";
import { Tooltip } from "bootstrap";
import TheNotificationBar from "@/layout/header/TheNotificationBar.vue";
import TheNotificationBarNotApprove from "@/layout/header/TheNotificationBarNotApprove.vue";

const authStore = useAuthStore();
const layoutStore = useLayoutStore();

onMounted(() => {
  //Init Shopping Me
  // searchResumesStore.getShoppingMe();

  //Set yup locale
  setLocale({
    mixed: {
      required: ({ label }) => ({ key: "errors_required", label }),
    },
    array: {
      min: ({ label }) => ({ key: "errors_at_least 1 items", label }),
    },
  });

  new Tooltip(document.body, {
    selector: "[data-bs-toggle='tooltip']",
  });

  authStore.getUserInfo();

  nextTick(() => {
    initializeComponents();
  });
});

const showAlert = computed(() => layoutStore.isShowAnnouncementBar);
</script>
