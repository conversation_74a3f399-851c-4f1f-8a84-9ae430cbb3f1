import { JobPostingHistoryRequestType, JobPackageSummary } from '@/models/usage-history';

export const FRONTEND_URL = process.env.MIX_FRONTEND_URL;
export const FRONTEND_PRODUCT_URL = `${FRONTEND_URL}/products`;

export const JOB_POSTING_HISTORY_REQUEST_DEFAULT_PARAMS: JobPostingHistoryRequestType = {
  page_invoice: 1,
  per_page_invoice: 5,
  page_usage: 1,
  per_page_usage: 10
}

export const JOB_PACKAGE_SUMMARY_DEFAULT_VALUE: JobPackageSummary = {
  total: 0,
  basic: 0,
  basic_plus: 0,
  top_job: 0,
  distinction: 0,
}