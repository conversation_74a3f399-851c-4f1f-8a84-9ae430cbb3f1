import request from "./utils";
import { Contact, Employer } from "@/models/employer";

export function getContactInformation() {
  return request({
    url: "/api/company/contact",
    method: "get",
  });
}

export function updateContactInformation(contactData: Contact) {
  return request({
    url: "/api/company/contact",
    method: "put",
    data: contactData,
  });
}

export function getEmployerAccount(page = 1) {
  return request({
    url: "/api/company/employers",
    method: "get",
  });
}

export function updateEmployerAccount(
  employeeId: string | number,
  data: Employer
) {
  return request({
    url: `/api/company/employers/${employeeId}`,
    method: "put",
    data: data,
  });
}
