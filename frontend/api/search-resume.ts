import request from "./utils";
import { AxiosPromise } from "axios";

export function fetchResumes(params) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: "/api/search-candidates",
    method: "GET",
    params: { ...params }
  });
}

export function fetchResumeDetail(id: number) {
  return request({
    url: `/api/search-candidates/${id}`,
    method: "GET"
  });
}

export function fetchResumePreviewCV(id: number, params) {
  return request({
    url: `/api/search-candidates/${id}/preview-cv`,
    method: "POST",
    params: params
  });
}

export function fetchShoppingMe() {
  return request({
    baseURL: process.env.MIX_API_CV_URL,
    url: "/shopping/me",
    method: "GET"
  });
}

export function fetchShoppingMeHistories(timeRange) {
  return request({
    baseURL: process.env.MIX_API_CV_URL,
    url: "/shopping/histories",
    method: "GET",
    params: timeRange
  });
}

export function fetchShoppingMeNotificationTopup() {
  return request({
    baseURL: process.env.MIX_API_CV_URL,
    url: "/shopping/notification-topup",
    method: "POST"
  });
}

export function getMyResumeNotes(resumeId: number) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/notes`,
    method: "GET"
  });
}

export function fetchSendMeNotification(resumeId: number = 0) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/send-me-notification`,
    method: "GET"
  });
}

export function createMyResumeNotes(resumeId: number, noteValue: object) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/notes`,
    method: "POST",
    data: noteValue
  });
}

export function updateMyResumeNotes(
  resumeId: number,
  noteId: number,
  noteValue: object
) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/notes/${noteId}`,
    method: "PUT",
    data: noteValue
  });
}

export function deleteMyResumeNotes(resumeId: number, noteId: number) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/notes/${noteId}`,
    method: "DELETE"
  });
}

export function unlockResume(id: number, key: string): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${id}/unlock`,
    method: "POST",
    data: { key }
  });
}

export function updateWishList(id: number) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${id}/save`,
    method: "POST"
  });
}

export function removeWishList(id: number) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${id}/remove`,
    method: "DELETE"
  });
}

export function fetchSearchCandidateUnlockCV(resumeId: number) {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/cv`,
    method: "POST",
  });
}

export function downloadResume(resumeId: number) {
  return request({
    baseURL: process.env.MIX_API_CV_URL,
    url: `/resumes-search/${resumeId}/download`,
    method: "POST",
    responseType: "blob",
    headers: {
      Accept: "application/pdf, application/msword"
    }
  });
}

export function getJsonResume(id: number) {
  return request({
    baseURL: process.env.MIX_API_CV_URL,
    url: `/resumes-search/${id}/json`,
    method: "GET"
  });
}

export function exportThemeResume(template: string, resume: object) {
  return request({
    baseURL: process.env.MIX_API_AMS_ORIGINAL,
    url: `/export/v2/theme/${template}`,
    method: "POST",
    responseType: "blob",
    headers: {
      Accept: "application/pdf, application/msword"
    },
    data: resume
  });
}

export function checkCreditBeforeUnlockCandidate(
  resumeId: number
): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/check-credit`,
    method: "GET"
  });
}

export function checkMaxAllowBeforeUnlockCandidate(): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/max-allow-unlock`,
    method: "GET"
  });
}

export function checkMaxAllowClickCandidate(): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/max-click-candidate`,
    method: "POST"
  });
}

export function createUnlockResumeDraft(resumeId: number, credit: number): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/unlock-draft`,
    method: "POST",
    data: { credit }
  });
}

export function requestRefundCredit(resumeId: number, reason: string): AxiosPromise<any> {
  return request({
    baseURL: process.env.MIX_API_URL,
    url: `/api/search-candidates/${resumeId}/request-refund`,
    method: "POST",
    data: { reason }
  });
}

export function exportUnlockedSearchResumes(searchParams: object) {
  return request({
    url: `/api/search-candidates/export`,
    method: "POST",
    data: searchParams,
  });
}
