import request from "./utils";
import { CandidatesParamsRequest } from "@/models/candidates";

export function fetchCandidates(query: CandidatesParamsRequest) {
  return request({
    url: `/api/candidates`,
    method: "GET",
    params: query
  });
}

export function fetchCandidateDetail(id: number) {
  return request({
    url: `/api/candidates/${id}`,
    method: "GET"
  });
}

export function updateCandidate(id: number, candidate) {
  return request({
    url: `/api/candidates/${id}`,
    method: "PUT",
    data: candidate
  });
}

export function fetchCandidateCV(id: number) {
  return request({
    url: `/api/candidates/${id}/cv`,
    method: "POST"
  });
}

export function fetchCandidatePreviewCV(id: number) {
  return request({
    url: `/api/candidates/${id}/preview-cv`,
    method: "POST"
  });
}

export function exportCandidates(searchParams: object) {
  return request({
    url: `/api/candidates/export`,
    method: "POST",
    data: searchParams
  });
}
