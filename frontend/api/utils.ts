import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { confirmUserDenied } from '@/helpers';
import { useLayoutStore } from '@/stores';

// Types
type RetryConfig = AxiosRequestConfig & { _retry?: boolean };

// Constants
const API_TIMEOUT = 20000;
const UNAUTHORIZED_STATUSES = [401, 419];
const SUCCESS_STATUSES = [200, 204];

/**
 * Create and configure an axios instance with interceptors
 */
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: process.env.MIX_API_URL,
    timeout: API_TIMEOUT,
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config: AxiosRequestConfig) => {
      // Uncomment and implement token logic when needed
      // const token = getCookieToken();
      // if (token && config.headers) {
      //   config.headers.Authorization = `Bearer ${token}`;
      // }
      return config;
    },
    (error: AxiosError) => Promise.reject(error)
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (error: AxiosError) => {
      const { config, response } = error;
      const originalRequest = config as RetryConfig;

      // Handle network errors
      if (!response) {
        console.error('Network Error:', error.message);
        return Promise.reject(error);
      }

      const { status, statusText } = response;

      // Handle CSRF token refresh for unauthorized requests
      if (UNAUTHORIZED_STATUSES.includes(status) && !originalRequest._retry) {
        try {
          originalRequest._retry = true;
          const { status: csrfStatus } = await axios.get('/sanctum/csrf-cookie');
          
          if (SUCCESS_STATUSES.includes(csrfStatus)) {
            return instance(originalRequest);
          }
          window.location.reload();
        } catch (csrfError) {
          console.error('CSRF token refresh failed:', csrfError);
          return Promise.reject(csrfError);
        }
      }

      // Handle specific status codes
      switch (status) {
        case 401:
          if (statusText === 'Unauthorized') {
            window.location.href = '/auth/redirect';
          }
          break;
          
        case 422: // Validation errors
        case 500: // Server errors
          return Promise.reject(error);
          
        default:
          // Handle other errors
          const layoutStore = useLayoutStore();
          layoutStore.unBlockPage();
          
          const confirmation = await confirmUserDenied();
          if (confirmation?.isConfirmed && process.env.MIX_OAUTH2_URL_LOGOUT) {
            window.location.href = process.env.MIX_OAUTH2_URL_LOGOUT;
          }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create and export the configured axios instance
const api = createAxiosInstance();

export default api;
