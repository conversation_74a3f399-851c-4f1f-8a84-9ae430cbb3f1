import { JobPostingHistoryRequestType, JobPostingResponse, CompanyRequestUpgradeType} from "@/models/usage-history";
import request from "./utils";

export function fetchCompanyInfo() {
  return request({
    url: "/api/company/profile",
    method: "get",
  });
}

export function updateCompany(company: any) {
  return request({
    url: "/api/company/profile",
    method: "put",
    data: company,
  });
}

export const fetchCreditHistory = (params?: any) => {
  return request({
    url: "/api/company/credit-history",
    method: "GET",
    params,
  });
};

export const activateUnlockPackage = (params: { company_search_package_id: number }) => {
  return request({
    url: "/api/company/active-unlock-package",
    method: "POST",
    data: params,
  });
}

export const fetchJobPostingHistory = async (params?: JobPostingHistoryRequestType): Promise<JobPostingResponse> => {
  return (await request({
    url: "/api/company/job-postings/histories",
    method: "GET",
    params,
  }));
}

export const sendBuyPackageNotification = async (data: CompanyRequestUpgradeType) => {
  return (await request({
    url: "/api/company/job-postings/send-buy-package-notification",
    method: "POST",
    data,
  }));
}

export const fetchCheckAvailablePackage = async() => {
  return (await request({
    url: "/api/company/job-postings/check-available-package",
    method: "GET",
  }));
}

export const fetchAvailablePackages = async(packageId: string) => {
  return (await request({
    url: "/api/company/job-postings/get-available-packages",
    method: "GET",
    params: {
      package_id: packageId
    }
  }));
}

export const checkRequestPackageTicket = async() => {
  return (await request({
    url: "/api/company/job-postings/check-is-requesting-package",
    method: "GET",
  }));
}

export function checkItJobPostingQuotaAction() {
  return request({
    url: "/api/company/job-postings/check-it-quota",
    method: "get",
  });
}

export function checkFreeJobLimit() {
  return request({
    url: "/api/company/job-postings/check-free-job-limit",
    method: "get",
  });
}

export const fetchCheckHasFreeJobOpened = async() => {
  return (await request({
    url: "/api/company/job-postings/check-has-free-job-opened",
    method: "GET",
  }));
}
