import { formatDate } from "@/helpers";
import request from "./utils";

interface DashboardParams {
  from_date?: string;
  to_date?: string;
  job_id?: number[] | string;
}

const getDefaultDates = () => {
  const today = new Date();
  return {
    from_date: formatDate(today),
    to_date: formatDate(today),
    job_id:[]
  };
};

export const getPremiumJobsByStatus = () => {
  return request({
    url: "/api/dashboard/get-premium-job-by-status",
    method: "get",
  });
}

export const getApplicationScoreCards = (params: Partial<DashboardParams> = {}) => {
  const defaultDates = getDefaultDates();
  const finalParams = {
    ...defaultDates,
    ...params
  };
  if (finalParams.job_id && Array.isArray(finalParams.job_id)) {
    finalParams.job_id = `${finalParams.job_id.join(",")}`;
  }
  return request({
    url: '/api/dashboard/get-application-score-cards',
    method: "get",
    params: finalParams,
    
  });
};

export const getApplicationByStatus = (params: Partial<DashboardParams> = {}) => {
  const defaultDates = getDefaultDates();
  const finalParams = {
    ...defaultDates,
    ...params
  };
  if (finalParams.job_id && Array.isArray(finalParams.job_id)) {
    finalParams.job_id = `${finalParams.job_id.join(",")}`;
  }
  return request({
    url: '/api/dashboard/get-application-by-status',
    method: "get",
    params: finalParams
  });
};

export const getAllJobTitle = () => {
  return request({
    url: "/api/form-values/get-all-job-title",
    method: "get",
  });
}

export const getApplicationTimeSeries = (params: Partial<DashboardParams> = {}) => {
  const defaultDates = getDefaultDates();
  const finalParams = {
    ...defaultDates,
    ...params
  };
  if (finalParams.job_id && Array.isArray(finalParams.job_id)) {
    finalParams.job_id = `${finalParams.job_id.join(",")}`;
  }
  return request({
    url: '/api/dashboard/get-application-time-series',
    method: "get",
    params: finalParams
  });
}
export const getPackageBalance = (params: Partial<DashboardParams> = {}) => {
  return request({
    url: '/api/dashboard/get-package-balance',
    method: "get",
  });
}