import request from "./utils";

export function uploadMedia(type: string, file: File) {
  const formData = new FormData();
  formData.append("type", type);
  formData.append("file", file);

  return request({
    url: "/api/media/upload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: formData,
  });
}

export function uploadErc(file: File) {
  const formData = new FormData();
  formData.append("file", file);

  return request({
    url: "/api/erc/upload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: formData,
  });
}
