import request from "./utils";
import { JobCategories, JobForm, JobsParams } from "@/models/jobs";

export function fetchJobs(query: JobsParams) {
  return request({
    url: `/api/jobs`,
    method: "GET",
    params: query,
  });
}

export function createJob(jobData) {
  let newExperiences = {
    experiences_ids: jobData.experiences_ids.to
      ? [jobData.experiences_ids.from, jobData.experiences_ids.to]
      : [jobData.experiences_ids.from],
  };

  if (
    Number(jobData.experiences_ids.from) ===
    Number(process.env.MIX_EXPERIENCE_FROM_ALL_ID)
  ) {
    newExperiences = {
      experiences_ids: [jobData.experiences_ids.from],
    };
  }

  const newValues = {
    ...jobData,
    ...newExperiences,
  };

  return request({
    url: `/api/jobs`,
    method: "post",
    data: newValues,
  });
}

export function fetchJobDetail(jobId: number) {
  return request({
    url: `/api/jobs/${jobId}`,
    method: "GET",
  });
}

export function updateJob(id: number, jobData) {
  return request({
    url: `/api/jobs/${id}`,
    method: "PUT",
    data: jobData,
  });
}

export function exportJobs(searchParams: object) {
  return request({
    url: `/api/jobs/export`,
    method: "POST",
    data: searchParams,
  });
}

export function requestDesign(job_id) {
  return request({
    url: `/api/jobs/request-design`,
    method: "POST",
    data: {
      job_id
    },
  });
}

export function previewUrl(job_id: number) {
  return request({
    url: `/api/jobs/preview-url`,
    method: "POST",
    params: {
      job_id: job_id
    }
  });
}

export async function getJobCategories() : Promise<JobCategories> {
  return (await request({url: `/api/jobs/categories`})).data;
}