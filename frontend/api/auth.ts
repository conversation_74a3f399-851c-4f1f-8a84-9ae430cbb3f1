import request from "./utils";

export function login(data: []) {
  return request({
    baseURL: "/api/auth/login",
    method: "post",
    data: data,
  });
}

export function logout() {
  return request({
    baseURL: "/api/auth/logout",
    method: "post",
  });
}

export function issueToken() {
  return request({
    baseURL: "/oauth2/issueToken",
    method: "get",
  });
}

export function changePassword(data) {
  return request({
    url: "/api/user/password",
    method: "PUT",
    data: data,
  });
}

export function getInfo() {
  return request({
    url: "/api/user",
    method: "GET",
  });
}
