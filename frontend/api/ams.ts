import request from "./utils";

export function fetchProvince() {
  return request({
    baseURL: process.env.MIX_AMS_API,
    url: "/areas/province",
    method: "get",
  })
  .then((response) => { return response.data; });
}

export function fetchDistrict(province_id: number) {
  return request({
    baseURL: process.env.MIX_AMS_API,
    url: `/areas/province/${province_id}/district`,
    method: "get",
  });
}

export function fetchWard(district_id: number) {
  return request({
    baseURL: process.env.MIX_AMS_API,
    url: `/areas/district/${district_id}/ward`,
    method: "get",
  });
}

export function fetchTaxonomies(fields: string | number) {
  return request({
    baseURL: process.env.MIX_AMS_API,
    url: "/taxonomies/",
    method: "get",
    params: {
      fields: fields,
      is_active: 1
    },
  });
}

export function fetchAllJobTitle(needPublishedAt = false) {
  return request({
    url: `/api/form-values/get-all-job-title?need_published_at=${needPublishedAt ? 1 : 0}`,
    method: "GET",
  });
}

export function fetchAllEmployerName() {
  return request({
    url: `/api/form-values/get-all-employer-name`,
    method: "GET",
  });
}

export function fetchAllCompanyProvince() {
  return request({
    url: `api/form-values/get-all-company-province`,
    method: "GET",
  });
}

export function fetchAllCandidatesProvince() {
  return request({
    url: "api/form-values/get-all-candidates-province",
    method: "GET",
  });
}

export function getBannerPromoteApp(type: string) {
  return request({
    baseURL: process.env.MIX_AMS_API,
    url: "/get_banner_promote_app",
    method: "get",
    params: {
      type: type,
      width: 245,
      height: 325
    },
  });
}
