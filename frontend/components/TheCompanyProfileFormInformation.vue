<template>
  <h2>{{ translate("about_company_company_information") }}</h2>

  <div class="row mt-4">
    <!-- Logo  -->
    <div class="col-6 col-lg-2">
      <div class="mb-10">
        <label
          for="companyLogoInput"
          class="required form-label fw-bolder mb-0"
          >{{ translate("about_company_logo") }}</label
        >
        <p class="heading lh-sm mb-4">
          {{ translate("about_company_types_image") }}
        </p>
        <div
          id="logo-upload-container"
          :class="{ 'is-invalid': logoMeta.touched && logoError }"
        >
          <AppImageUpload
            type="logo"
            :url="logo?.toString()"
            @uploaded="changeLogo"
            id="logo-image"
            isHideDescription
          />
        </div>
        <AppErrorMessage name="image_logo" />
      </div>
    </div>
    <!-- End Logo   -->

    <div class="col-12 col-lg-7">
      <!-- Company name  -->
      <div class="mb-8">
        <label for="companyNameInput" class="required form-label">{{
          translate("about_company_company_name")
        }}</label>
        <input
          type="text"
          name="display_name"
          class="form-control form-control-solid"
          :class="{ 'is-invalid': displayNameMeta.dirty && displayNameError }"
          :placeholder="
            translate('about_company_enter_shortened_and_recognizable_name')
          "
          v-model="displayName"
        />
        <AppErrorMessage name="display_name" />
      </div>
      <!-- Company Tagline  -->
      <div class="mb-8">
        <label for="companyTaglineInput" class="required form-label">{{
          translate("about_company_company_tagline")
        }}</label>
        <input
          type="text"
          class="form-control form-control-solid"
          :class="{ 'is-invalid': taglineMeta.dirty && taglineError }"
          :placeholder="translate('about_company_enter_your_company_tag_line')"
          v-model="tagline"
          name="tagline"
        />
        <AppErrorMessage name="tagline" />
      </div>
    </div>

    <div class="col-12 col-lg-3">
      <!-- Nationality  -->
      <div class="mb-8">
        <label for="NationalityInput" class="required form-label">{{
          translate("about_company_nationality")
        }}</label>
        <Multiselect
          v-model="nationalities"
          :options="taxonomiesStore.nationalities"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          mode="tags"
          name="nationalities"
          :class="{
            'is-invalid': nationalitiesMeta.dirty && nationalitiesError,
          }"
        />
        <AppErrorMessage name="nationalities" />
      </div>
      <!-- Company Size  -->
      <div class="mb-8">
        <label for="CompanySizeInput" class="required form-label">{{
          translate("about_company_company_size")
        }}</label>
        <Multiselect
          v-model="numEmployee"
          :options="taxonomiesStore.numEmployee"
          mode="single"
          name="num_employees"
          :class="{ 'is-invalid': numEmployeeMeta.dirty && numEmployeeError }"
        />
        <AppErrorMessage name="num_employees" />
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Introduction  -->
    <div class="col-6">
      <label for="companyLogoInput" class="required form-label mb-0">{{
        translate("about_company_introduction")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("about_company_tell_job_seekers_about_your_company") }}
      </p>
      <AppTextEditor
        :initValue="description?.toString()"
        :height="450"
        @update:initValue="(value) => changeDescription(value)"
        name="description"
        :class="{ 'is-invalid': descriptionMeta.dirty && descriptionError }"
      />
      <AppErrorMessage name="description" />
    </div>
    <!-- End Introduction  -->
    <div class="col-6">
      <!-- Industry  -->
      <div class="mb-5">
        <label for="IndustryInput" class="required form-label">{{
          translate("about_company_industry")
        }}</label>
        <Multiselect
          v-model="industries"
          :options="taxonomiesStore.industries"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          mode="tags"
          name="industries_ids"
          :class="{ 'is-invalid': industriesMeta.dirty && industriesError }"
        />
        <AppErrorMessage name="industries_ids" />
      </div>
      <!-- Tech stack  -->
      <div class="mb-5">
        <label for="TechStackInput" class="required form-label">{{
          translate("about_company_tech_stack")
        }}</label>
        <Multiselect
          v-model="skills"
          :options="taxonomiesStore.skills"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          mode="tags"
          name="skill_ids"
          :class="{ 'is-invalid': skillsMeta.dirty && skillsError }"
        />
        <AppErrorMessage name="skills_ids" />
      </div>

      <!-- Website  -->
      <div class="mb-5">
        <label for="WebsiteInput" class="form-label">{{
          translate("about_company_website")
        }}</label>
        <div class="position-relative">
          <input
            type="text"
            name="website"
            class="form-control form-icon form-control-solid"
            :class="{ 'is-invalid': websiteError }"
            :placeholder="translate('about_company_website')"
            v-model="website"
          />

          <div
            class="icon-form-icon"
            :class="{ 'invalid-icon-form-icon': websiteError }"
          >
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/social-media/website.svg" />
            </span>
          </div>

          <AppErrorMessage name="website" />
        </div>
      </div>

      <!-- Social Media  -->
      <div>
        <label for="SocialMediaInput" class="form-label">{{
          translate("about_company_social_media")
        }}</label>
        <!-- Facebook  -->
        <div class="position-relative">
          <input
            type="text"
            name="facebook"
            class="form-control form-icon form-control-solid"
            :class="{ 'is-invalid': facebookError }"
            placeholder="Eg. https://facebook.com/topdevvietnam"
            v-model="facebook"
          />

          <div
            class="icon-form-icon"
            :class="{ 'invalid-icon-form-icon': facebookError }"
          >
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/social-media/fb.svg" />
            </span>
          </div>

          <AppErrorMessage name="social_network.facebook" />
        </div>

        <!-- Linkedin  -->
        <div class="position-relative mt-3">
          <input
            type="text"
            name="linkedin"
            class="form-control form-icon form-control-solid"
            :class="{ 'is-invalid': linkedinError }"
            placeholder="Eg. https://www.linkedin.com/company/topdev-vn"
            v-model="linkedin"
          />

          <div
            class="icon-form-icon"
            :class="{ 'invalid-icon-form-icon': linkedinError }"
          >
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/social-media/linkedin.svg" />
            </span>
          </div>

          <AppErrorMessage name="social_network.linkedin" />
        </div>

        <!-- Youtube  -->
        <div class="position-relative my-3">
          <input
            type="text"
            name="youtube"
            class="form-control form-icon form-control-solid"
            :class="{ 'is-invalid': youtubeError }"
            placeholder="Eg. https://www.youtube.com/channel"
            v-model="youtube"
          />

          <div
            class="icon-form-icon"
            :class="{ 'invalid-icon-form-icon': youtubeError }"
          >
            <span class="svg-icon svg-icon-2">
              <inline-svg src="/assets/icons/social-media/youtube.svg" />
            </span>
          </div>

          <AppErrorMessage name="social_network.youtube" />
        </div>

        <!-- Link -->
        <div
          class="d-flex justify-content-between align-items-start"
          v-if="isShowAddLink"
        >
          <div class="position-relative flex-grow-1">
            <input
              type="text"
              class="form-control form-icon form-control-solid"
              :class="{ 'is-invalid': shareAltError }"
              placeholder="Eg. https://www.link"
              v-model="share_alt"
            />

            <div
              class="icon-form-icon"
              :class="{ 'invalid-icon-form-icon pb-2': shareAltError }"
            >
              <span class="svg-icon svg-icon-2">
                <inline-svg src="/assets/icons/social-media/link.svg" />
              </span>
            </div>

            <AppErrorMessage name="social_network.share_alt" />
          </div>
          <button
            @click="onCloseLinkInput"
            type="button"
            class="btn hover-svg-primary ms-3 mt-1 px-0 py-0"
          >
            <inline-svg src="/assets/icons/trash.svg" />
          </button>
        </div>

        <!-- Add Link Button  -->
        <button
          v-if="!isShowAddLink"
          @click="isShowAddLink = !isShowAddLink"
          class="btn svg-icon svg-icon-2 text-primary fw-bolder d-flex align-items-center px-0 py-0"
          type="button"
        >
          <inline-svg src="/assets/icons/plus.svg" class="me-1" />
          {{ translate("about_company_add_link") }}
        </button>
      </div>
      <!-- End Social Media  -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useField } from "vee-validate";
import Multiselect from "@vueform/multiselect";

import AppErrorMessage from "@/components/AppErrorMessage.vue";
import AppImageUpload from "@/components/AppImageUpload.vue";
import AppTextEditor from "@/components/AppTextEditor.vue";

import { useTaxonomiesStore } from "@/stores/taxonomies";
import { translate } from "@/helpers";

/**
 * Define store
 */
const taxonomiesStore = useTaxonomiesStore();

/**
 * Define field
 */
const {
  value: logo,
  errorMessage: logoError,
  meta: logoMeta,
  handleChange: changeLogo,
} = useField("image_logo");
const {
  value: displayName,
  errorMessage: displayNameError,
  meta: displayNameMeta,
} = useField("display_name");
const {
  value: tagline,
  errorMessage: taglineError,
  meta: taglineMeta,
} = useField("tagline");
const {
  value: nationalities,
  errorMessage: nationalitiesError,
  meta: nationalitiesMeta,
} = useField("nationalities");
const {
  value: numEmployee,
  errorMessage: numEmployeeError,
  meta: numEmployeeMeta,
} = useField("num_employees");
const {
  value: description,
  errorMessage: descriptionError,
  meta: descriptionMeta,
  handleChange: changeDescription,
} = useField("description");
const {
  value: industries,
  errorMessage: industriesError,
  meta: industriesMeta,
} = useField("industries_ids");
const {
  value: skills,
  errorMessage: skillsError,
  meta: skillsMeta,
} = useField("skills_ids");
const { value: website, errorMessage: websiteError } = useField("website");

const { value: facebook, errorMessage: facebookError } = useField(
  "social_network.facebook"
);
const { value: linkedin, errorMessage: linkedinError } = useField(
  "social_network.linkedin"
);
const { value: youtube, errorMessage: youtubeError } = useField(
  "social_network.youtube"
);
const { value: share_alt, errorMessage: shareAltError } = useField(
  "social_network.share_alt"
);

/**
 * Define data
 */
const isShowAddLink = ref(!!share_alt.value);

/**
 * Define function
 */
const onCloseLinkInput = () => {
  share_alt.value = "";
  isShowAddLink.value = false;
};
</script>
