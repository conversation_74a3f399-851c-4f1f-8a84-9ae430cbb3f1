<template>
  <ul
      class="w-100 ps-0 mt-4"
      v-if="notesData.listNotes.length > 0"
  >
    <li
        class="w-100 py-2 d-flex flex-row justify-content-between align-items-center"
        v-for="(note, index) in listOverview"
        :key="index"
    >
      <div class="w-100 d-flex flex-column justify-content-between gap-2">
        <div class="d-flex flex-row justify-content-start align-items-center gap-4">
          <span class="font-bold">{{ note.user_name }}</span>
          <span class="text-muted text-sm-end">{{ note.updated_at}}</span>
        </div>

        <div class="w-100 d-flex flex-row justify-content-start align-items-center note-item-content">
          <div class="w-100 bg-white p-2">
            <span>{{ note.content }}</span>
          </div>
          <div  v-if="note.user_id == userAuth.user.id"
                class="p-2 note-item-actions btn-group dropstart">
            <button
              type="button"
              data-bs-toggle="dropdown"
              aria-expanded="false"
              class="btn btn-link hover-svg-primary svg-icon svg-icon-3">
              <inline-svg src="/assets/icons/dots-horizontal.svg" class="text-dark" />
            </button>
            <ul class="dropdown-menu">
              <li>
                <button
                  type="button"
                  class="w-100 btn btn-default btn-edit-note py-2 text-nowrap text-start"
                  @click="onOpenEditNoteForm(note.id)"
                >
                  {{ translate("search_resumes_edit_button") }}
                </button>
              </li>
              <li>
                <button
                  type="button"
                  class="w-100 btn btn-default btn-delete-note py-2 text-nowrap text-start"
                  @click="onDeleteMyResumeNote(note.id)"
                >
                  {{ translate("search_resumes_delete_button") }}
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </li>
  </ul>

  <div v-if="notesData.listNotes.length > 2" class="d-flex flex-row justify-content-start align-items-center gap-2 p-2">
    <button type="button" @click="onOpenPopupViewAll" class="btn btn-link text-dark btn-sm text-sm-center">
      {{ translate('search_resumes_view_all') }}
    </button>
  </div>

  <TheSearchResumesViewAllNotesModal
    :resumeDetail="resumeDetail"
    :notesData="notesData"
    :onDeleteMyResumeNote="onDeleteMyResumeNote"
    :submitEditNote="submitEditNote"
    :isUnlocked="isUnlocked"
    :submitAddNote="submitAddNote"
    :isOpenForm="isOpenForm"
    :open-form="openForm"
    :cancel-handler="cancelHandler"
  />
</template>

<script setup lang="ts">

import {NotesDataState, SearchCandidate} from "@/models/search-resumes";
import {computed, onMounted, ref} from "vue";
import {translate} from "@/helpers";
import {Modal} from "bootstrap";
import TheSearchResumesViewAllNotesModal from "@/components/TheSearchResumesViewAllNotesModal.vue";
import {InlineSvg} from "@/plugins";
import {useAuthStore} from "@/stores";

interface Props {
  openForm: () => void;
  isOpenForm: boolean;
  cancelHandler: () => void;
  isUnlocked: boolean,
  resumeDetail: SearchCandidate,
  notesData: NotesDataState,
  onDeleteMyResumeNote: (id: number) => void,
  submitEditNote: () => void,
  submitAddNote: () => void,
  onOpenEditNoteForm: (id: number) => void
}

const userAuth = useAuthStore();
const props = defineProps<Props>();
const listOverview = computed(() => {
  return props.notesData.listNotes.filter(function (value, index) {
    return index < 2;
  });
});
const isViewAll = ref<boolean>(false);

onMounted(async () => {
  isViewAll.value = props.notesData.listNotes.length > 2;
});

const onOpenPopupViewAll = () => {
  // Manually open
  Modal.getOrCreateInstance("#modal-view-all-notes").show();
};
</script>

<style scoped>
.note-item-actions {
  display: none;
}
.note-item-actions:hover {
  display: inline-block;
}
.note-item-content:hover .note-item-actions {
  display: inline-block;
}
</style>
