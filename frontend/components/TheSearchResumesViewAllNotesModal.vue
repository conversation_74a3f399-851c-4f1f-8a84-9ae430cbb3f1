<template>
  <div class="modal fade" tabindex="-1" id="modal-view-all-notes">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header border-bottom mb-3 bg-light-dark gap-1">
          <h4 class="modal-title">{{ resumeName }} </h4>
          <span>
            <inline-svg v-if="resumeDetail.current_job" src="/assets/icons/one-dot.svg"/>
          </span>
          <span class="fw-normal"> {{ resumeDetail.current_job }} </span>
          <button
            type="button"
            class="btn-close"
            aria-label="Close"
            data-bs-dismiss="modal"
          ></button>
        </div>

        <div class="h-50 modal-body d-flex flex-column gap-4 overflow-scroll">
          <ul
            class="w-100 ps-0"
            v-if="notesData.listNotes.length > 0"
          >
            <li
              class="w-100 py-2 d-flex flex-row justify-content-between align-items-center"
              v-for="(note, index) in notesData.listNotes"
              :key="index"
            >
              <div class="w-100 d-flex flex-column justify-content-between gap-2">
                <div class="d-flex flex-row justify-content-start align-items-center gap-4">
                  <span class="font-bold">{{ note.user_name }}</span>
                  <span class="text-muted text-sm-end">{{ note.updated_at}}</span>
                </div>

                <div class="w-100 d-flex flex-row justify-content-start align-items-center note-item-content">
                  <div class="w-100 bg-light-dark p-2">
                    <span>{{ note.content }}</span>
                  </div>
                  <div  v-if="note.user_id == userAuth.user.id"
                        class="p-2 note-item-actions btn-group dropstart">
                    <button
                      type="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                      class="btn btn-link hover-svg-primary svg-icon svg-icon-3">
                      <inline-svg src="/assets/icons/dots-horizontal.svg" class="text-dark" />
                    </button>
                    <ul class="dropdown-menu">
                      <li>
                        <button
                          type="button"
                          class="w-100 btn btn-default btn-edit-note py-2 text-nowrap text-start"
                          @click="onOpenEditNoteForm(note.id)"
                        >
                          {{ translate("search_resumes_edit_button") }}
                        </button>
                      </li>
                      <li>
                        <button
                          type="button"
                          class="w-100 btn btn-default btn-delete-note py-2 text-nowrap text-start"
                          @click="onDeleteMyResumeNote(note.id)"
                        >
                          {{ translate("search_resumes_delete_button") }}
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <div class="modal-footer bg-light-dark">

          <!-- Add note -->
          <TheSearchResumesAddNoteForm
                :notesData="notesData"
                :submit-add-note="submitAddNote"
                :submit-edit-note="submitEditNoteHandler"
                :isOpenForm="isOpenForm"
                :open-form="openForm"
                :cancel-handler="cancelHandler" />

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {NotesDataState, SearchCandidate} from "@/models/search-resumes";
import {InlineSvg} from "@/plugins";
import {computed, ref} from "vue";
import TheSearchResumesAddNoteForm from "@/components/TheSearchResumesAddNoteForm.vue";
import {useAuthStore} from "@/stores";
import {translate} from "@/helpers";

interface Props {
  openForm: () => void;
  isOpenForm: boolean;
  cancelHandler: () => void;
  isUnlocked: boolean,
  resumeDetail: SearchCandidate,
  notesData: NotesDataState,
  onDeleteMyResumeNote: (id: number) => void,
  submitEditNote: () => void,
  editId?: number,
  submitAddNote: () => void
}

const props = defineProps<Props>();
const isEditingNote = ref<boolean>(false);
const resumeName = computed(() => {
  if (props.isUnlocked) {
    return props.resumeDetail.fullname;
  } else {
    const arrName = props.resumeDetail.fullname.split(' ');
    let hiddenFullName = '';
    arrName.map((value) => {
      hiddenFullName += value.charAt(0).toUpperCase() + ". ";
    });
    return hiddenFullName;
  }
});
const userAuth = useAuthStore();

const submitEditNoteHandler = () => {
  props.submitEditNote();
  isEditingNote.value = false;
}

const onOpenEditNoteForm = (id: number) => {
  props.notesData.editId = id;
  const noteById = props.notesData.listNotes.find((value) => value.id == id);
  props.notesData.noteInputValue = noteById.content;

  isEditingNote.value = true;
}
</script>
