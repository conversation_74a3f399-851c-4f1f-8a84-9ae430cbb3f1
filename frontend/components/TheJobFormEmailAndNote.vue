<template>
  <div>
    <!-- Email for Applications  -->
    <div class="mb-6">
      <label for="emailInput" class="form-label mb-0">{{
        translate("job_form_email_for_applications")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_you_can_add_other_email_addresses_here") }}
      </p>
      <input
        class="form-control form-control-solid custom-tagify py-0 fs-7"
        id="emailInput"
        :value="emailValue"
      />
    </div>
    <!-- Note for TopDev  -->
    <div class="mb-6">
      <label for="noteInput" class="form-label mb-0">{{
        translate("job_form_note_for_topdev")
      }}</label
      ><span class="heading ms-2">{{
        translate("job_form_this_information_wont_be_displayed")
      }}</span>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_let_us_know_your_addition_requirements") }}
      </p>
      <textarea
        type="text"
        placeholder="Add note"
        class="form-control form-control-solid"
        v-model="noteValue"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useField } from "vee-validate";
import Tagify from "@yaireo/tagify";
import * as yup from "yup";

import { translate } from "@/helpers";

//Define data
const tagify = ref();

const { value: emailValue } = useField<Array<string>>("emails_cc");
const { value: noteValue } = useField<string>("note");

//Function
const onAddEmail = (e: any) => {
  const email = e.detail.data.value;
  emailValue.value = [...emailValue.value, email];
};
const onRemoveEmail = (e: any) => {
  const newEmailValue = [...emailValue.value];
  emailValue.value = newEmailValue.filter(
    (email, index) => index !== e.detail.index
  );
};

//Life cycle
onMounted(() => {
  const emailInput = document.getElementById("emailInput") as HTMLInputElement;
  tagify.value = new Tagify(emailInput, {
    placeholder: translate("job_form_email_placeholder"),
    validate: (tagData) => {
      try {
        yup.string().email().validateSync(tagData.value);

        return true;
      } catch (e) {
        return false;
      }
    },
  });
  tagify.value.on("add", onAddEmail).on("remove", onRemoveEmail);
});
</script>
