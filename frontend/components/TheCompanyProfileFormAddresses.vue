<template>
  <section id="company-address-container">
    <!--begin::Section header-->
    <div class="d-flex justify-content-between">
      <div>
        <h2 class="mb-0 required">
          {{ translate("about_company_addresses") }}
        </h2>
        <p class="heading">{{ translate("about_company_add_addresses") }}</p>
      </div>

      <button
        class="btn svg-icon svg-icon-2 text-primary fw-bolder d-flex align-items-center px-0 py-0"
        type="button"
        data-bs-toggle="modal"
        data-bs-target="#modal-addresses"
        @click="openAddAddressModal"
      >
        <inline-svg src="/assets/icons/plus.svg" class="me-1" />
        {{ translate("about_company_add") }}
      </button>
    </div>
    <!--end::Section header-->

    <!--begin::Section body-->
    <div>
      <ul class="sortable list-unstyled address-list is-invalid">
        <draggable v-model="addressList" item-key="key">
          <template #item="{ element, index }">
            <li class="d-flex justify-content-between align-items-center gap-4">
              <div class="grab">
                <inline-svg src="/assets/icons/drag-drop.svg" />
              </div>
              <div class="flex-grow-1">
                {{ element.full_address }}
              </div>
              <div class="d-flex justify-content-between">
                <button
                  @click="openEditAddressModal(element, index)"
                  type="button"
                  class="btn btn-sm hover-svg-primary p-0"
                >
                  <inline-svg src="/assets/icons/pen.svg" />
                </button>
                <button
                  @click="onDeleteAddress(index)"
                  type="button"
                  class="btn btn-sm hover-svg-primary p-0"
                >
                  <inline-svg src="/assets/icons/trash.svg" class="ms-3" />
                </button>
              </div>
            </li>
          </template>
          >
        </draggable>
      </ul>
      <AppErrorMessage name="addresses" />
    </div>
    <!--end::Section body-->

    <!--begin::Modal -->
    <TheCompanyProfileFormModalAddresses
      :address="currentAddress"
      @update:address="(value) => updateAddress(value)"
    />
    <!--end::Modal -->
  </section>
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
import { Field, useFieldArray } from "vee-validate";
import draggable from "vuedraggable";
import { Modal } from "bootstrap";

import AppErrorMessage from "@/components/AppErrorMessage.vue";
import TheCompanyProfileFormModalAddresses from "@/components/TheCompanyProfileFormModalAddresses.vue";
import { translate } from "@/helpers";
import { Addresses } from "@/models/employer";

/**
 * Define data
 */
const {
  fields: addressFields,
  update: updateAddressField,
  push: pushAddressField,
  remove: removeAddressField,
  replace: replaceAddresses,
} = useFieldArray("addresses");

const currentAddress: any = ref<Addresses>();
const isCreateNewAddress = ref(false);
const edittingAddressIndex: any = ref(-1);
const addressList = computed({
  get() {
    return addressFields.value.map((item) => item.value);
  },

  set(value: any) {
    replaceAddresses(value);
  },
});

/**
 * Define function
 */
const updateAddress = (addressData: any) => {
  if (isCreateNewAddress.value) {
    pushAddressField(addressData);
  } else {
    updateAddressField(edittingAddressIndex.value, addressData);
  }
};

const openAddAddressModal = () => {
  isCreateNewAddress.value = true;
  currentAddress.value = {};

  // Manually open
  Modal.getOrCreateInstance("#modal-addresses").show();
};

const openEditAddressModal = (selectedAddress: any, index: any) => {
  isCreateNewAddress.value = false;

  currentAddress.value = selectedAddress;
  edittingAddressIndex.value = index;

  // Manually open
  Modal.getOrCreateInstance("#modal-addresses").show();
};

const onDeleteAddress = (index: any) => {
  removeAddressField(index);
};
</script>
