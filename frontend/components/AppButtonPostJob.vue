<template>
  <router-link
    :to="{ name: 'post-job' }"
    class="btn btn-topdev-1 d-flex align-items-center justify-content-center"
    :class="[size === 'md' ? 'btn-md' : '', size === 'sm' ? 'btn-sm' : '']"
    @click="onClick"
  >
    <span class="title">{{ translate("layout_post_job") }}</span>
    <span class="icon-post-job d-none svg-icon svg-icon-2">
      <inline-svg src="/assets/icons/aside/post-job.svg" />
    </span>
  </router-link>
</template>

<script lang="ts" setup>
import { useJobStore } from "@/stores";
import { translate } from "@/helpers";

interface Props {
  size?: "md" | "sm";
}
const jobStore = useJobStore();
const { size = "md" } = defineProps<Props>();

const onClick = () => {
  jobStore.setIDAndStatus(undefined, "add");
};
</script>
