<template>
  <div>
    <!-- Package  -->
    <div class="mb-6">
      <label for="locationInput" class="form-label mb-0" :class="{required: jobStatusValue == 2}">{{
        translate("job_detail_package_select")
      }}</label>
      <Multiselect
        name="package_id"
        :placeholder="translate('job_detail_package_select_placeholder')"
        :class="{
          'is-invalid': packageMeta.touched && !!packageError,
        }"
        :disabled="jobStatusValue != 2"
        :options="getAvailablePackages"
        v-model="packageValue"
        @blur="handlePackageBlur"
      />
      <AppErrorMessage name="package_id" />
    </div>

    <!-- Location  -->
    <div class="mb-6">
      <label for="locationInput" class="required form-label mb-0">{{
        translate("job_form_location")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_select_the_address_that_has_been_added") }}
      </p>
      <Multiselect
        mode="tags"
        name="addresses_id"
        :placeholder="translate('job_form_select_location')"
        :hideSelected="false"
        :closeOnSelect="false"
        :class="{ 'is-invalid': addressesMeta.touched && addressesError }"
        :options="companyAddresses"
        v-model="addressesValue"
        @blur="handleAddressesBlur"
      >
        <template v-slot:tag="{ option, handleTagRemove, disabled }">
          <div
            class="multiselect-tag is-user"
            :class="{
              'is-disabled': disabled,
            }"
          >
            <span class="address-tag-content">{{ option.label }}</span>
            <span
              v-if="!disabled"
              class="multiselect-tag-remove"
              @click="handleTagRemove(option, $event)"
            >
              <span class="multiselect-tag-remove-icon"></span>
            </span>
          </div>
        </template>
      </Multiselect>
      <AppErrorMessage name="addresses_id" />
    </div>

    <!-- Salary  -->
    <div class="mb-6">
      <div
        class="d-flex align-items-center flex-wrap justify-content-between mb-4"
      >
        <label for="salaryInput" class="required form-label mb-0">
          {{ translate("job_form_salary") }}
        </label>
        <!-- Currency -->
        <div
          class="btn-group mb-3"
          role="group"
          aria-label="salary-button-group"
        >
          <button
            type="button"
            class="btn btn-currency"
            :class="{ primary: salaryValue.currency === 'USD' }"
            @click="onChangeCurrency('USD')"
          >
            USD
          </button>
          <button
            type="button"
            class="btn btn-currency"
            :class="{ primary: salaryValue.currency === 'VND' }"
            @click="onChangeCurrency('VND')"
          >
            VND
          </button>
        </div>
      </div>
      <div>
        <!-- Min - Max Salary  -->
        <div
          class="d-flex align-items-center flex-wrap justify-content-between mb-4"
        >
          <button
            type="button"
            class="btn-negotiable"
            :class="{ active: salaryValue.is_negotiable === 0 }"
            @click="onChangeSalaryType(0)"
          ></button>
          <div
            class="d-flex justify-content-between box-salary"
            :class="{ 'is-invalid': salaryMeta.touched }"
          >
            <div class="justify-content-between me-2">
              <div class="d-flex align-items-center">
                <label class="heading heading-1 me-2">{{
                  translate("job_form_from")
                }}</label>
                <AppCurrencyInput
                  v-model.lazy="minSalaryValue"
                  :options="{ currency: salaryValue.currency }"
                  :isInvalid="
                    salaryMeta.touched && (!!minSalaryError || !!maxSalaryError)
                  "
                  :disabled="salaryValue.is_negotiable === 1"
                />
              </div>
            </div>
            <div class="justify-content-between">
              <div
                class="d-flex align-items-center"
                :class="{
                  'is-invalid': salaryMeta.touched,
                }"
              >
                <label class="heading heading-1 me-2">{{
                  translate("job_form_to")
                }}</label>
                <AppCurrencyInput
                  v-model.lazy="maxSalaryValue"
                  :options="{ currency: salaryValue.currency }"
                  :isInvalid="
                    salaryMeta.touched && (!!minSalaryError || !!maxSalaryError)
                  "
                  :disabled="salaryValue.is_negotiable === 1"
                />
              </div>
            </div>
          </div>
          <AppErrorMessage v-if="minSalaryError" name="salary.min" />
          <AppErrorMessage v-else-if="maxSalaryError" name="salary.max" />
        </div>

        <!-- Min Estimate - Max Estimate Salary  -->
        <div class="d-flex flex-wrap justify-content-between">
          <button
            type="button"
            class="btn-negotiable btn-negotiable-estimate mt-2px"
            :class="{ active: salaryValue.is_negotiable === 1 }"
            @click="onChangeSalaryType(1)"
          ></button>
          <div class="box-salary">
            <label
              class="form-label cursor mb-0"
              @click="onChangeSalaryType(1)"
            >
              {{ translate("job_form_negotiable") }}
            </label>
            <div
              v-if="salaryValue.is_negotiable === 1"
              :class="{ 'is-invalid': salaryMeta.touched }"
              class="d-flex justify-content-between flex-wrap w-100"
            >
              <p class="heading my-2">
                {{ translate("job_form_this_salary_will_be") }}
              </p>
              <div
                class="d-flex justify-content-between"
                aria-label="min-estimate"
              >
                <div
                  class="d-flex justify-content-between align-items-center me-2"
                >
                  <label class="heading heading-1 me-2">{{
                    translate("job_form_from")
                  }}</label>
                  <AppCurrencyInput
                    v-model.lazy="minEstimateSalaryValue"
                    :options="{ currency: salaryValue.currency }"
                    :isInvalid="
                      salaryMeta.touched &&
                      (!!minEstimateSalaryError || !!maxEstimateSalaryError)
                    "
                  />
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <label class="heading heading-1 me-2">{{
                    translate("job_form_to")
                  }}</label>
                  <AppCurrencyInput
                    v-model.lazy="maxEstimateSalaryValue"
                    :options="{ currency: salaryValue.currency }"
                    :isInvalid="
                      salaryMeta.touched &&
                      (!!minEstimateSalaryError || !!maxEstimateSalaryError)
                    "
                  />
                </div>
              </div>
            </div>
            <AppErrorMessage
              v-if="minEstimateSalaryError"
              name="salary.min_estimate"
            />
            <AppErrorMessage
              v-else-if="maxEstimateSalaryError"
              name="salary.max_estimate"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- End salary -->

    <!-- Level  -->
    <div class="mb-6">
      <label for="levelInput" class="required form-label mb-0">{{
        translate("job_form_level")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_select_job_level") }}
      </p>
      <Multiselect
        mode="tags"
        name="job_levels"
        :placeholder="translate('job_form_select_job_level')"
        :hideSelected="false"
        :closeOnSelect="false"
        :class="{ 'is-invalid': jobLevelsMeta.touched && jobLevelsError }"
        :options="taxonomiesStore.jobLevels"
        v-model="jobLevelsValue"
        @blur="handleJobLevelsBlur"
      />
      <AppErrorMessage name="job_levels" />
    </div>

    <!-- Year of experience  -->
    <div class="mb-6">
      <label for="experienceInput" class="required form-label mb-0">{{
        translate("job_form_year_of_exp")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_input_year_experience_requirement") }}
      </p>
      <!-- From -->
      <div>
        <div
          class="d-flex justify-content-between align-items-center"
          :class="{
            'is-invalid': experienceFromMeta.touched && !!experienceFromError,
          }"
        >
          <label class="heading required w-55px">{{
            translate("job_form_from")
          }}</label>
          <Multiselect
            :placeholder="translate('job_form_minimum_year_experience')"
            :class="{
              'is-invalid': experienceFromMeta.touched && !!experienceFromError,
            }"
            :options="taxonomiesStore.experiences"
            v-model="experienceFromValue"
            @blur="handleExperienceFromBlur"
          />
        </div>
        <AppErrorMessage name="experiences_ids.from" />
      </div>

      <!-- To -->
      <div
        v-if="experienceFromValue !== Number(EXPERIENCE_FROM_ALL_ID)"
        class="d-flex justify-content-between align-items-center mt-2"
      >
        <label class="heading w-55px">{{ translate("job_form_to") }}</label>
        <Multiselect
          :placeholder="translate('job_form_maximum_year_experience')"
          :options="
            taxonomiesStore.experiences.filter(
              (item) => item.value !== Number(EXPERIENCE_FROM_ALL_ID)
            )
          "
          v-model="experienceToValue"
        />
      </div>
    </div>

    <!-- Job type  -->
    <div class="mb-6">
      <label for="jobTypeInput" class="required form-label mb-0">{{
        translate("job_form_job_type")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_select_job_type") }}
      </p>
      <Multiselect
        mode="tags"
        name="job_types"
        :placeholder="translate('job_form_select_job_type')"
        :hideSelected="false"
        :closeOnSelect="false"
        :class="{ 'is-invalid': jobTypesMeta.touched && jobTypesError }"
        :options="taxonomiesStore.jobTypes"
        v-model="jobTypesValue"
        @blur="handleJobTypesBlur"
      />
      <AppErrorMessage name="job_types" />
    </div>

    <!-- Contract type  -->
    <div class="mb-6">
      <label for="contractTypeInput" class="required form-label mb-0">{{
        translate("job_form_contract_type")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_select_contract_type") }}
      </p>
      <Multiselect
        mode="tags"
        name="contract_type"
        :placeholder="translate('job_form_select_contract_type')"
        :class="{ 'is-invalid': contractTypeMeta.touched && contractTypeError }"
        :options="taxonomiesStore.contractTypes"
        v-model="contractTypeValue"
        @blur="handleContractTypeBlur"
      />
      <AppErrorMessage name="contract_type" />
    </div>

    <!-- Skill  -->
    <div class="mb-6">
      <label for="skillsInput" class="required form-label mb-0">{{
        translate("job_form_skills")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_select_job_skills") }}
      </p>
      <Multiselect
        mode="tags"
        name="skills_ids"
        :placeholder="translate('job_form_select_job_skills_placeholder')"
        :searchable="true"
        :hideSelected="false"
        :closeOnSelect="false"
        :class="{ 'is-invalid': skillsMeta.touched && skillsError }"
        :options="taxonomiesStore.skills"
        :max="3"
        v-model="skillsValue"
        @blur="handleSkillsBlur"
      />
      <AppErrorMessage name="skills_ids" />
    </div>

    <!-- Recruitment process  -->
    <div class="mb-6">
      <label for="processInput" class="form-label mb-0">{{
        translate("job_form_recruitment_process")
      }}</label>
      <p class="heading lh-sm mb-2">
        {{ translate("job_form_maxium_100_characters") }}
      </p>
      <!-- Process Input  -->
      <div class="input-with-icon mb-2">
        <input
          type="text"
          class="form-control ps-3 pe-75"
          v-model="recruimentProcess"
          :placeholder="translate('job_form_interview_with_line_manager')"
        />
        <span class="right-icon">
          <button
            class="btn text-primary btn-link"
            type="button"
            :disabled="!recruimentProcess"
            @click="onSaveRecruimentProcess"
          >
            {{
              isEditRecruiment
                ? translate("job_form_save")
                : translate("job_form_add")
            }}
          </button>
        </span>
      </div>

      <!-- Process List  -->
      <ul
        class="ps-0 process-list-container"
        v-if="recruimentProcessValue.length > 0"
      >
        <li v-for="(process, index) in recruimentProcessValue" :key="index">
          <div class="d-flex justify-content-between align-items-center">
            <div class="w-100">
              <p class="fw-bold my-0 text-break">
                {{ `${translate("job_form_round")} ${index + 1} ` }}
              </p>
              <p class="text-break mb-0 fs-8">{{ process.name }}</p>
            </div>
            <div class="d-flex ms-2">
              <button
                type="button"
                class="btn btn-link hover-svg-primary"
                @click="onEditRecruimentProcess(index)"
              >
                <span
                  :class="{ 'pen-active-icon': index === recruimentEditIndex }"
                >
                  <inline-svg src="/assets/icons/pen.svg" />
                </span>
              </button>
              <button
                type="button"
                class="btn btn-link hover-svg-primary ms-2"
                @click="onDeleteRecruimentProcess(index)"
              >
                <inline-svg src="/assets/icons/trash.svg" />
              </button>
            </div>
          </div>
        </li>
      </ul>
      <!-- End Process List  -->

      <!-- Empty recruitment process list -->
      <div v-else class="text-center w-75 mt-5 mx-auto">
        <span class="svg svg-icon-1">
          <inline-svg
            src="/assets/icons/empty-file.svg"
            width="100px"
            height="100px"
          />
        </span>
        <p class="text-topdev-2">
          {{
            translate(
              "job_form_attract_quality_candidates_by_adding_recruitment_process"
            )
          }}
        </p>
      </div>
      <!-- End Empty recruitment process list -->
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import Multiselect from "@vueform/multiselect";
import { useField } from "vee-validate";

import AppErrorMessage from "@/components/AppErrorMessage.vue";
import AppCurrencyInput from "@/components/AppCurrencyInput.vue";

import { fetchAvailablePackages, fetchCompanyInfo } from "@/api/company";
import { useTaxonomiesStore } from "@/stores";
import { translate } from "@/helpers";
import { Salary } from "@/models/jobs";
import { AvailablePackage } from "@/models/usage-history";

interface RecruimentProcess {
  name: string;
}

//Define store
const taxonomiesStore = useTaxonomiesStore();

//Define form
const {
  value: addressesValue,
  meta: addressesMeta,
  handleBlur: handleAddressesBlur,
  errorMessage: addressesError,
} = useField("addresses_id");

const { value: salaryValue, meta: salaryMeta } = useField<Salary>("salary");

const { value: minSalaryValue, errorMessage: minSalaryError } = useField<
  number | null
>("salary.min");

const { value: maxSalaryValue, errorMessage: maxSalaryError } = useField<
  number | null
>("salary.max");

const { value: minEstimateSalaryValue, errorMessage: minEstimateSalaryError } =
  useField<number | null>("salary.min_estimate");

const { value: maxEstimateSalaryValue, errorMessage: maxEstimateSalaryError } =
  useField<number | null>("salary.max_estimate");

const {
  value: experienceFromValue,
  meta: experienceFromMeta,
  handleBlur: handleExperienceFromBlur,
  errorMessage: experienceFromError,
} = useField("experiences_ids.from");

const { value: experienceToValue } = useField("experiences_ids.to");

const {
  value: jobLevelsValue,
  meta: jobLevelsMeta,
  handleBlur: handleJobLevelsBlur,
  errorMessage: jobLevelsError,
} = useField("job_levels");

const {
  value: jobTypesValue,
  meta: jobTypesMeta,
  handleBlur: handleJobTypesBlur,
  errorMessage: jobTypesError,
} = useField("job_types");

const {
  value: skillsValue,
  meta: skillsMeta,
  handleBlur: handleSkillsBlur,
  errorMessage: skillsError,
} = useField("skills_ids");

const {
  value: contractTypeValue,
  meta: contractTypeMeta,
  handleBlur: handleContractTypeBlur,
  errorMessage: contractTypeError,
} = useField("contract_type");

const { value: recruimentProcessValue } =
  useField<Array<RecruimentProcess>>("recruiment_process");


const {
  value: packageValue,
  meta: packageMeta,
  handleBlur: handlePackageBlur,
  errorMessage: packageError,
} = useField("package_id");

const { value: jobStatusValue } = useField<number | null>("job_status_id");

//Define data
const companyAddresses = ref();
const recruimentProcess = ref("");
const isEditRecruiment = ref(false);
const recruimentEditIndex = ref(-1);
const availablePackages = ref<AvailablePackage[]>([]);

//Function
const onChangeCurrency = (value: string) => {
  salaryValue.value.currency = value;
};

const onChangeSalaryType = (value: number) => {
  salaryValue.value.is_negotiable = value;
  salaryValue.value.min = 0;
};

const onSaveRecruimentProcess = () => {
  if (!!recruimentProcess.value.trim()) {
    if (isEditRecruiment.value) {
      // Edit Recruiment
      recruimentProcessValue.value[recruimentEditIndex.value] = {
        name: recruimentProcess.value,
      };
      isEditRecruiment.value = false;
      recruimentProcess.value = "";
      recruimentEditIndex.value = -1;
    } else {
      //Add Recruiment
      if (recruimentProcessValue.value.length == 8) return;

      recruimentProcessValue.value.push({ name: recruimentProcess.value });
      recruimentProcess.value = "";
    }
  }
};

const onDeleteRecruimentProcess = (index: number) => {
  const newRecruimentProcess = [...recruimentProcessValue.value];
  recruimentProcessValue.value = newRecruimentProcess.filter(
    (process, i) => i !== index
  );
};

const onEditRecruimentProcess = (index: number) => {
  isEditRecruiment.value = true;
  recruimentEditIndex.value = index;
  recruimentProcess.value = recruimentProcessValue.value[index]?.name;
};

/**
 * Handle Job Experiences Changes
 */
const EXPERIENCE_FROM_ALL_ID = ref(process.env.MIX_EXPERIENCE_FROM_ALL_ID);

//Life cycle hook
onMounted(async () => {
  //Get company addresses
  const result = await fetchCompanyInfo();
  if (result.data) {
    companyAddresses.value = result.data.addresses.map((address: any) => ({
      value: address.id,
      label: address.full_address,
    }));
  }

  const { data } = await fetchAvailablePackages(packageValue.value as string);
  if (data) {
    availablePackages.value = data;
  }
});

const getAvailablePackages = computed(
    () => availablePackages.value.map((item: AvailablePackage) => ({
      value: item.value,
      label: `${item.is_free_package ? `${item.label} ${translate('usage_history_job_posting_package_gift_title')}` : item.label} (${translate('usage_history_job_posting_quota_package_expired_at', [item.expired_at])})`
    }))
  );
</script>
