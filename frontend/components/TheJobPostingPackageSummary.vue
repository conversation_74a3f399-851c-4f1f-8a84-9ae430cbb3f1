<template>
  <div class="d-flex gap-3 justify-content-between align-items-stretch">
    <div class="d-flex flex-grow-1 justify-center align-items-center bg-white rounded-3 ps-2">
      <div class="flex-none p-2 me-2 pt-0 pb-0 available-package">
        <h2 class="text-topdev-1 display-6 m-0">{{ summary.total }}</h2>
      </div>
      <div>
        <span class="text-topdev-2 text-sm">{{ translate(packageLangByQuantity(summary.total)) }}</span>
        <h3 class="text-base">{{ translate("usage_history_job_posting_available_title") }}</h3>
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-center align-items-center bg-white rounded-3 ps-2">
      <div class="flex-none p-3">
        <h2 class="display-6">{{ summary.top_job }}</h2>
      </div>
      <div>
        <span class="text-topdev-2 text-sm">{{ translate(packageLangByQuantity(summary.top_job)) }}</span>
        <h3 class="text-base">{{ translate("usage_history_job_posting_package_top_job_title") }}</h3>
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-center align-items-center bg-white rounded-3 ps-2">
      <div class="flex-none p-3">
        <h2 class="display-6">{{ summary.distinction }}</h2>
      </div>
      <div>
        <span class="text-topdev-2 text-sm">{{ translate(packageLangByQuantity(summary.distinction)) }}</span>
        <h3 class="text-base">{{ translate("usage_history_job_posting_package_distinction_job_title") }}</h3>
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-center align-items-center bg-white rounded-3 ps-2">
      <div class="flex-none p-3">
        <h2 class="display-6">{{ summary.basic_plus }}</h2>
      </div>
      <div>
        <span class="text-topdev-2 text-sm">{{ translate(packageLangByQuantity(summary.basic_plus)) }}</span>
        <h3 class="text-base">{{ translate("usage_history_job_posting_package_basic_plus_job_title") }}</h3>
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-center align-items-center bg-white rounded-3 ps-2">
      <div class="flex-none p-3">
        <h2 class="display-6">{{ summary.basic }}</h2>
      </div>
      <div>
        <span class="text-topdev-2 text-sm">{{ translate(packageLangByQuantity(summary.basic)) }}</span>
        <h3 class="text-base">{{ translate("usage_history_job_posting_package_basic_job_title") }}</h3>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { translate } from "@/helpers";
import { JobPackageSummary } from "@/models/usage-history";

type Summary = {
  summary: JobPackageSummary
}
defineProps<Summary>();

const packageLangByQuantity = (qty: number) => {
  return (qty > 1) ? 'usage_history_job_posting_usage_log_use_packages' : 'usage_history_job_posting_usage_log_use_package';
}

</script>
