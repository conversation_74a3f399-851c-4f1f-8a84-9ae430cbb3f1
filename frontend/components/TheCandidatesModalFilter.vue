<template>
  <div
    class="modal fade"
    id="filter-modal"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-dialog-centered mw-728px">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{ translate("candidate_list_filter") }}</h4>
          <button
            type="button"
            class="btn-close"
            aria-label="Close"
            @click="onHide"
          ></button>
        </div>
        <div class="modal-body">
          <div class="row align-items-center show-best-matched">
            <div class="col">
              <label class="form-check-label" for="showBestMatched">{{ translate("candidate_show_best_matched") }}</label>
            </div>
            <div class="col-auto form-check form-switch">
              <input class="form-check-input" type="checkbox" id="showBestMatched" v-model="matching_status" :true-value="1"
                     :false-value="null">
            </div>
          </div>
          <div class="row">
            <div class="col-6">
              <!-- Job title -->
              <div class="my-5">
                <label for="" class="form-label">{{
                    translate("candidate_list_job_title")
                  }}</label>
                <Multiselect
                  :options="candidatesStore.candidatesTaxonomies.jobTitles"
                  v-model="job_id"
                  mode="single"
                  :searchable="true"
                  :placeholder="translate('candidate_list_all_jobs')"
                  class="job-id"
                >
                  <template
                    v-slot:singlelabel="{ value }: { value: OptionTitle }"
                  >
                    <div class="multiselect-multiple-label">
                      <span>
                        {{ translate("candidate_list_job_title") }}:
                        {{ `${value.title}(#${value.value})` }}
                      </span>
                    </div>
                  </template>
                  <template v-slot:option="{ option }: { option : OptionTitle }">
                    {{ option.value ? `${option.title}(#${option.value} - ${translate('job_list_publised_date')} ${option.published_at})` : translate('candidate_list_all_jobs') }}
                  </template>
                </Multiselect>
              </div>
              <!-- YOE -->
              <div class="my-5 custom-checkbox-filter checkbox-experiences">
                <label for="" class="form-label">{{
                    translate("candidate_list_experiences")
                  }}</label>
                <Multiselect
                  :placeholder="translate('candidate_list_experiences')"
                  :options="candidatesStore.candidatesTaxonomies.experiences"
                  :searchable="true"
                  :hideSelected="false"
                  :closeOnSelect="false"
                  v-model="experience_ids"
                  mode="multiple"
                >
                  <template v-slot:multiplelabel="{ values }: ValuesType">
                    <div class="multiselect-multiple-label">
                      <span>
                        {{ translate("candidate_list_experiences") }}:
                        {{
                          values.length > 2
                            ? `(+${values.length})`
                            : values.map(({ value, label }) => translate(label)).join(", ")
                        }}
                      </span>
                    </div>
                  </template>

                  <template v-slot:option="{ option }: { option: { label: string, value: number } }">
                    <div class="custom-option">
                      <input type="checkbox" :checked="experience_ids.includes(option.value)">
                      <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
                    </div>
                  </template>
                </Multiselect>
              </div>
              <!-- Candidates -->
              <div class="my-5">
                <label for="" class="form-label">{{
                    translate("candidate_list_candidate_location")
                  }}</label>
                <Multiselect
                  :placeholder="translate('candidate_list_province_city')"
                  :options="candidatesStore.candidatesTaxonomies.candidatesProvince"
                  v-model="location_id"
                  :searchable="true"
                />
              </div>
            </div>
            <div class="col-6">
              <!-- Skills -->
              <div class="my-5 custom-checkbox-filter checkbox-skills">
                <label for="" class="form-label">{{
                    translate("candidate_list_skills")
                  }}</label>
                <Multiselect
                  :placeholder="translate('candidate_list_skills')"
                  :options="taxonomiesStore.skills"
                  :searchable="true"
                  :hideSelected="false"
                  :closeOnSelect="false"
                  v-model="skills_id"
                  mode="tags"
                >
                  <template v-slot:option="{ option }: { option: { label: string, value: string } }">
                    <div class="custom-option">
                      <input type="checkbox" :checked="skills_id.includes(option.value)">
                      <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
                    </div>
                  </template>
                </Multiselect>
              </div>
              <!-- Procedure status -->
              <div class="my-5">
                <label for="" class="form-label">{{
                    translate("candidate_list_filter_procedure_status")
                  }}</label>
                <Multiselect
                  :placeholder="translate('candidate_list_filter_procedure_status')"
                  :options="candidatesStore.candidatesTaxonomies.procedureStatus"
                  v-model="procedure_status"
                >
                  <template
                    v-slot:singlelabel="{ value }: { value: { label: string } }"
                  >
                    <div class="multiselect-multiple-label">
                    <span>
                      {{ translate(value.label) }}
                    </span>
                    </div>
                  </template>
                  <template v-slot:option="{ option }: { option: { label: string } }">
                    <span class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
                  </template>
                </Multiselect>
              </div>
              <!-- Applied date -->
              <div class="my-5">
                <label for="" class="form-label">{{
                    translate("candidate_list_applied_date")
                  }}</label>
                <v-date-picker
                  v-model="timeRange"
                  is-range
                  :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
                  :masks="{ input: 'DD-MM-YYYY' }"
                >
                  <template v-slot="{ inputValue, inputEvents }">
                    <div class="d-flex justify-center align-items-center">
                      <input
                        :value="inputValue.start"
                        v-on="inputEvents.start"
                        class="form-control form-control-solid"
                        :placeholder="translate('candidate_list_form_date')"
                        style="min-height: 52px;margin-right: 20px;"
                      />
                      <input
                        :value="inputValue.end"
                        v-on="inputEvents.end"
                        class="form-control form-control-solid"
                        :placeholder="translate('candidate_list_to_date')"
                        style="min-height: 52px;"
                      />
                    </div>
                  </template>
                </v-date-picker>
              </div>
            </div>
          </div>


        </div>
        <div class="modal-footer" style="justify-content: end">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            @click="onReset"
          >
            {{ translate("candidate_list_reset") }}
          </button>
          <button type="button" class="btn btn-sm btn-primary" @click="onApply">
            {{ translate("candidate_list_apply") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, toRaw, watch } from "vue";
import Multiselect from "@vueform/multiselect";
import { Modal } from "bootstrap";

import { useTaxonomiesStore, useCandidatesStore } from "@/stores";
import { CandidatesFilterParams, OptionTitle, ValuesType } from "@/models/candidates";
import { translate } from "@/helpers";

interface Props {
  filterData: CandidatesFilterParams;
}

interface Emit {
  (e: "applied", value: CandidatesFilterParams): void;

  (e: "reset");
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

//Define store
const taxonomiesStore = useTaxonomiesStore();
const candidatesStore = useCandidatesStore();

//Define data
const job_id = ref(props.filterData.job_id);
const application_status = ref(props.filterData.application_status);
const location_id = ref(props.filterData.location_id);
const skills_id = ref(props.filterData.skills_id);
const experience_ids = ref(props.filterData.experience_ids);
const timeRange = ref(props.filterData.timeRange);
const matching_status = ref(props.filterData.matching_status);
const procedure_status = ref(props.filterData.procedure_status);

//Function
const onReset = () => {
  // Close modal
  Modal.getOrCreateInstance("#filter-modal").hide();
  // Reset candidatesParams
  emit("reset");
};
const onHide = () => {
  Modal.getOrCreateInstance("#filter-modal").hide();
};

const onApply = () => {
  const filter = {
    job_id: job_id.value,
    application_status: application_status.value,
    location_id: location_id.value,
    skills_id: toRaw(skills_id.value),
    experience_ids: toRaw(experience_ids.value),
    timeRange: toRaw(timeRange.value),
    matching_status: matching_status.value,
    procedure_status: procedure_status.value
  };

  emit("applied", filter);
  Modal.getOrCreateInstance("#filter-modal").hide();
};

//watch
watch(
  () => props.filterData,
  (filterData) => {
    //Set data from props
    job_id.value = filterData.job_id;
    application_status.value = filterData.application_status;
    location_id.value = filterData.location_id;
    skills_id.value = filterData.skills_id;
    timeRange.value = filterData.timeRange;
    matching_status.value = filterData.matching_status;
    procedure_status.value = filterData.procedure_status;
    experience_ids.value = filterData.experience_ids;
  },
  { deep: true }
);
watch(
  () => candidatesStore.candidatesParams.filter.matching_status,
  (status) => {
    matching_status.value = status;
  },
  { deep: true }
);
</script>
