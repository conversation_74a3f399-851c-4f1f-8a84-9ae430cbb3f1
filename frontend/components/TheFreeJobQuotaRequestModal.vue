<template>
  <div
    class="modal fade"
    id="freeJobQuotaRequestModal"
    tabindex="-1"
    aria-labelledby="freeJobQuotaRequestModalLabel"
    aria-hidden="true"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-dialog-centered modal-lg">
      <div class="modal-content">
        <div class="modal-body p-0">
          <div class="row g-0">
            <!-- Left column - Only illustration -->
            <div class="col-md-4 d-none d-md-flex align-items-center justify-content-center">
              <img
                src="@/assets/icons/frame.svg"
                alt="Free Job Quota"
                class="img-fluid p-4"
                style="max-height: 300px;"
              >
            </div>

            <!-- Right column - All content -->
            <div class="col-md-8">
              <div class="p-4">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="modal-title fw-bold mb-0 fs-5" id="freeJobQuotaRequestModalLabel">
                    Bạn đã sử dụng hết lượt đăng tin miễn phí
                  </h5>
                  <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                    :disabled="isSubmitting"
                  ></button>
                </div>

                <p class="text-muted mb-4">Vui lòng gửi yêu cầu để được gia hạn thêm quyền đăng tin.</p>

                <form @submit.prevent="submitRequest">
                  <!-- Sender Name -->
                  <div class="mb-3">
                    <label for="senderName" class="form-label fw-medium mb-1">
                      Tên người gửi <span class="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      id="senderName"
                      name="name"
                      class="form-control"
                      :class="{ 'is-invalid': errors.name }"
                      v-model="formData.name"
                      :disabled="isSubmitting"
                      required
                      :key="'sender-'+formData.name"
                    >
                  </div>

                  <!-- Company Name -->
                  <div class="mb-3">
                    <label for="companyName" class="form-label fw-medium mb-1">
                      Tên công ty <span class="text-danger">*</span>
                    </label>
                    <input
                      type="text"
                      id="companyName"
                      name="company_name"
                      class="form-control"
                      :class="{ 'is-invalid': errors.company_name }"
                      v-model="formData.company_name"
                      :disabled="isSubmitting"
                      required
                      :key="'company-'+formData.company_name"
                    >
                  </div>

                  <!-- Reason -->
                  <div class="mb-4">
                    <label for="reason" class="form-label fw-medium mb-1">
                      Lý do yêu cầu <span class="text-danger">*</span>
                    </label>
                    <textarea
                      class="form-control"
                      id="reason"
                      rows="3"
                      name="reason"
                      v-model="formData.reason"
                      :disabled="isSubmitting"
                      placeholder="Nhập lý do yêu cầu"
                      required
                    ></textarea>
                  </div>

                  <!-- Error Message -->
                  <div v-if="submitError" class="alert alert-danger mb-4">
                    {{ submitError }}
                  </div>

                  <!-- Buttons -->
                  <div class="d-flex justify-content-end gap-2">
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      data-bs-dismiss="modal"
                      :disabled="isSubmitting"
                    >
                      Hủy
                    </button>
                    <button
                      type="submit"
                      class="btn btn-sm btn-primary"
                      :disabled="isSubmitting"
                    >
                      <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-1" role="status"></span>
                      Gửi yêu cầu
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { Modal } from 'bootstrap';
import axios from 'axios';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from '@/stores';
import { fetchCompanyInfo } from '@/api/company';
import { showSuccesToast } from '@/helpers';

interface FormData {
  name: string;
  company_name: string;
  phone: string;
  reason: string;
}

interface FormErrors {
  name?: string;
  company_name?: string;
  phone?: string;
  reason?: string;
  [key: string]: string | undefined;
}

export default defineComponent({
  name: 'TheFreeJobQuotaRequestModal',

  emits: ['submitted'],

  setup(props, { emit }) {
    const { t } = useI18n();
    const authStore = useAuthStore();
    const modal = ref<Modal | null>(null);
    const isSubmitting = ref(false);
    const isSubmitted = ref(false);
    const submitError = ref<string | null>(null);
    const isMounted = ref(false);

    const formData = reactive<FormData>({
      name: '',
      company_name: '',
      phone: '',
      reason: ''
    });

    const errors = reactive<FormErrors>({});

    // Auto-fill form with user and company info
    const fetchUserAndCompanyInfo = async () => {
      try {
        console.log('Fetching user and company info...');
        const user = authStore.user || await authStore.getUserInfo();
        const { data: company } = await fetchCompanyInfo();

        console.log('User info:', user);
        console.log('Company info:', company);

        formData.name = user?.full_name || '';
        formData.company_name = company?.display_name || '';
        formData.phone = user?.phone || '';

        console.log('Form data after update:', formData);
      } catch (error) {
        console.error('Failed to fetch user/company info:', error);
      }
    };

    // Fetch user and company info when component is mounted
    onMounted(async () => {
      console.log('Modal mounted, fetching user data...');
      isMounted.value = true;
      await fetchUserAndCompanyInfo();
    });

    // Also fetch when modal is shown
    const showModal = async () => {
      console.log('Showing modal...');
      await fetchUserAndCompanyInfo();
      if (modal.value) {
        modal.value.show();
      } else {
        const modalElement = document.getElementById('freeJobQuotaRequestModal');
        if (modalElement) {
          modal.value = new Modal(modalElement);
          modal.value.show();
        }
      }
    };

    const validateForm = (): boolean => {
      let isValid = true;
      submitError.value = null;

      // Reset errors
      Object.keys(errors).forEach(key => {
        errors[key] = '';
      });

      // Validate name
      if (!formData.name.trim()) {
        errors.name = 'name_required';
        isValid = false;
      }

      // Validate company name
      if (!formData.company_name.trim()) {
        errors.company_name = 'company_required';
        isValid = false;
      }

      // Validate phone (make it optional since we're auto-filling it)
      if (formData.phone && !/^[0-9]{10,15}$/.test(formData.phone)) {
        errors.phone = 'phone_invalid';
        isValid = false;
      }

      // Validate reason
      if (!formData.reason.trim()) {
        errors.reason = 'reason_required';
        isValid = false;
      }

      return isValid;
    };

    const show = () => {
      isSubmitted.value = false;
      submitError.value = null;
      formData.name = '';
      formData.company_name = '';
      formData.phone = '';
      formData.reason = '';

      // Reset errors
      Object.keys(errors).forEach(key => {
        errors[key] = '';
      });

      if (!modal.value) {
        const modalElement = document.getElementById('freeJobQuotaRequestModal');
        if (modalElement) {
          modal.value = new Modal(modalElement);
          // Reset modal when hidden
          modalElement.addEventListener('hidden.bs.modal', () => {
            isSubmitted.value = false;
            submitError.value = null;
          });
        }
      }

      modal.value?.show();
    };

    const hide = () => {
      modal.value?.hide();
    };

    const closeModal = () => {
      isSubmitted.value = false;
      hide();
      // Reset form when closing
      formData.name = '';
      formData.company_name = '';
      formData.phone = '';
      formData.reason = '';
      // Reset errors
      Object.keys(errors).forEach(key => {
        errors[key] = '';
      });
    };

    const submitRequest = async () => {
      if (isSubmitting.value) return;

      if (!validateForm()) {
        return;
      }

      isSubmitting.value = true;
      submitError.value = null;

      try {
        const response = await axios.post('/api/company/job-postings/request-free-quota', formData);

        if (response.data.success) {
          // Show success message
          showSuccesToast(
            'Thành công',
            'Yêu cầu của bạn đã được gửi đi thành công. Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.'
          );
          
          // Close the modal after a short delay
          setTimeout(() => {
            closeModal();
          }, 1500);
          
          // Emit submitted event
          emit('submitted');
        } else {
          submitError.value = response.data.message || t('job_package.free_job_quota_request.error_message');
        }
      } catch (error: any) {
        console.error('Failed to submit free job quota request:', error);

        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          if (error.response.data && error.response.data.message) {
            submitError.value = error.response.data.message;
          } else if (error.response.status === 422 && error.response.data.errors) {
            // Handle validation errors from Laravel
            const validationErrors = error.response.data.errors;
            Object.keys(validationErrors).forEach(field => {
              if (validationErrors[field] && validationErrors[field][0]) {
                errors[field] = validationErrors[field][0];
              }
            });
          } else {
            submitError.value = t('job_package.free_job_quota_request.error_message');
          }
        } else if (error.request) {
          // The request was made but no response was received
          submitError.value = t('job_package.free_job_quota_request.error_network');
        } else {
          // Something happened in setting up the request that triggered an Error
          submitError.value = error.message || t('job_package.free_job_quota_request.error_message');
        }
      } finally {
        isSubmitting.value = false;
      }
    };

    // Expose methods to parent component
    return {
      isSubmitting,
      isSubmitted,
      formData,
      errors,
      submitError,
      submitRequest,
      show: showModal, // Use the updated showModal function
      hide,
      closeModal,
      fetchUserAndCompanyInfo
    };
  }
});
</script>

<style scoped>
.modal-body {
  max-height: 90vh;
  overflow-y: auto;
  padding: 0;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  min-width: 120px;
}

.btn-danger:hover {
  background-color: #bb2d3b;
  border-color: #b02a37;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.text-muted {
  color: #6c757d !important;
}

.fs-4 {
  font-size: 1.5rem !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.btn-outline-secondary {
  min-width: 80px;
}

/* Make sure the modal takes full height on mobile */
@media (max-width: 767.98px) {
  .modal-body {
    max-height: 100vh;
  }

  .modal-dialog {
    margin: 0;
    height: 100vh;
    max-width: 100%;
  }

  .modal-content {
    height: 100%;
    border: none;
    border-radius: 0;
  }
}
</style>
