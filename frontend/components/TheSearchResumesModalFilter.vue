<template>
  <div
    class="modal fade"
    id="search-resumes-filter-modal"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header modal-header-filter">
          <h4 class="modal-title">
            {{ translate("candidate_list_filter") }}
          </h4>
          <button
            type="button"
            class="btn-close"
            aria-label="Close"
            @click="handleCloseModal"
          ></button>
        </div>
        <div class="modal-body mt-3">
          <!-- Show Saved Candidates Button  -->
          <div
            class="d-flex align-items-center justify-content-between bg-gray p-4"
          >
            <label for="wishList" class="select-none font-bold">
              {{ translate("search_resumes_show_saved_candidate") }}
            </label>
            <label for="wishList" class="checkbox-custom">
              <input
                type="checkbox"
                name="wishList"
                id="wishList"
                v-model="showWishList"
                class="checkbox-input"
              />
              <div class="checkbox-slider"></div>
            </label>
          </div>
          <div class="row mt-2">
            <div class="col">
              <!-- Skills  -->
              <div class="my-5 custom-checkbox-filter checkbox-skills">
                <label class="form-label">
                  {{ translate("candidate_list_skills") }}
                </label>
                <Multiselect
                  :placeholder="translate('candidate_list_all_skills')"
                  :searchable="true"
                  :options="taxonomiesStore.skills"
                  :hideSelected="false"
                  :closeOnSelect="false"
                  mode="tags"
                  v-model="skill"
                >
                  <template v-slot:option="{ option }: { option: { label: string, value: string } }">
                    <div class="custom-option">
                      <input type="checkbox" :checked="skill.includes(option.value)">
                      <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ option.label }}</span>
                    </div>
                  </template>
                </Multiselect>
              </div>
            </div>
            <div class="col">
              <!-- Experience -->
              <div class="my-5 custom-checkbox-filter checkbox-experiences">
                <label class="form-label">
                  {{ translate("search_resumes_experience") }}
                </label>
                <Multiselect
                  :placeholder="translate('candidate_list_all_experiences')"
                  :searchable="true"
                  :options="taxonomiesStore.experiences"
                  :hideSelected="false"
                  :closeOnSelect="false"
                  v-model="experience"
                  mode="multiple"
                >
                  <template v-slot:multiplelabel="{ values }: ValuesType">
                    <div class="multiselect-multiple-label">
                      <span>
                        {{
                          values.length > 2
                            ? translate("search_resumes_experience") + ": "
                            : ""
                        }}
                        {{
                          values.length > 2
                            ? `(+${values.length})`
                            : values.map(({ value, label }) => label).join(", ")
                        }}
                      </span>
                    </div>
                  </template>

                  <template v-slot:option="{ option }: { option: { label: string, value: string } }">
                    <div class="custom-option">
                      <input type="checkbox" :checked="experience.includes(option.value)">
                      <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ option.label }}</span>
                    </div>
                  </template>
                </Multiselect>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <!-- Candidate location  -->
              <div class="mb-5">
                <label class="form-label">
                  {{ translate("search_resumes_candidate_location") }}
                </label>
                <Multiselect
                  :placeholder="translate('candidate_list_all_locations')"
                  :searchable="true"
                  :options="locationOptions"
                  :hideSelected="false"
                  :closeOnSelect="false"
                  v-model="location"
                />
              </div>
            </div>
            <div class="col">
              <!-- Candidate language proficiency -->
              <div class="mb-5">
                <label class="form-label">
                  {{
                    translate("search_resumes_candidate_language_proficiency")
                  }}
                </label>
                <Multiselect
                  :placeholder="translate('candidate_list_all_languages')"
                  :searchable="true"
                  :options="cvLanguagesOption"
                  v-model="candidate_language"
                />
              </div>
            </div>
          </div>

          <!-- CV last updated -->
          <div>
            <v-date-picker
              is-range
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
              :masks="{ input: 'DD-MM-YYYY' }"
              v-model="timeRange"
              :max-date="new Date()"
            >
              <template v-slot="{ inputValue, inputEvents }">
                <div class="d-flex justify-center align-items-center gap-6">
                  <div class="flex-1">
                    <p class="form-label">
                      {{ translate("search_resumes_last_update_from") }}
                    </p>
                    <div class="position-relative">
                      <input
                        :value="inputValue.start"
                        v-on="inputEvents.start"
                        class="form-control form-control-solid form-control-lg"
                        :placeholder="translate('candidate_list_form_date')"
                      />
                      <div
                        class="datepicker-remove"
                        :class="{ 'd-none': !inputValue.start }"
                        @click="removeDateFilter"
                      >
                        <inline-svg
                          src="/assets/icons/close.svg"
                          width="16"
                          height="16"
                        ></inline-svg>
                      </div>
                    </div>
                  </div>
                  <div class="flex-1">
                    <p class="form-label">
                      {{ translate("search_resumes_to") }}
                    </p>
                    <div class="position-relative">
                      <input
                        :value="inputValue.end"
                        v-on="inputEvents.end"
                        class="form-control form-control-solid form-control-lg"
                        :placeholder="translate('candidate_list_to_date')"
                      />
                      <div
                        class="datepicker-remove"
                        :class="{ 'd-none': !inputValue.end }"
                        @click="removeDateFilter"
                      >
                        <inline-svg
                          src="/assets/icons/close.svg"
                          width="16"
                          height="16"
                        ></inline-svg>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </v-date-picker>
          </div>
        </div>
        <div class="model-footer-filter">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            @click="onReset"
          >
            {{ translate("candidate_list_reset") }}
          </button>
          <button type="button" class="btn btn-sm btn-danger" @click="onApply">
            {{ translate("candidate_list_apply") }}
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal -->
</template>

<script lang="ts" setup>
import { translate, updateQueryParam } from "@/helpers";
import { Option } from "@/models/search-resumes";
import {
  TaxonomiesKey,
  useSearchResumesStore,
  useTaxonomiesStore
} from "@/stores";
import Multiselect from "@vueform/multiselect";
import { Modal } from "bootstrap";
import { ref, watch } from "vue";
import { ValuesType } from "@/models/candidates";

interface ModalData {
  skill: Array<string>;
  experience: Array<string>;
  location: string;
  language: string;
  candidate_language: string;
  timeRange: {
    start: string;
    end: string;
  };
  showWishList: boolean;
}

interface Props {
  modalData: ModalData;
  locationsOption: Array<Option>;
  cvLanguagesOption: Array<Option>;
}

interface Emits {
  (e: "applied", value: ModalData): void;

  (e: "reset");
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const skill = ref(props.modalData.skill);
const experience = ref(props.modalData.experience);
const location = ref(props.modalData.location);
const language = ref(props.modalData.language);
const candidate_language = ref(props.modalData.candidate_language);
const timeRange = ref(props.modalData.timeRange);
const showWishList = ref(props.modalData.showWishList);

const taxonomiesStore = useTaxonomiesStore();
const searchResumesStore = useSearchResumesStore();
const locationOptions = ref(props.locationsOption);

const onReset = () => {
  skill.value = [];
  experience.value = [];
  location.value = "";
  language.value = "";
  candidate_language.value = "";
  timeRange.value = { start: "", end: "" };
  showWishList.value = false;
  // emits('reset')
};

const onApply = () => {
  const filterData = {
    skill: skill.value,
    experience: experience.value,
    location: location.value,
    language: language.value,
    candidate_language: candidate_language.value,
    timeRange: timeRange.value,
    showWishList: showWishList.value
  };

  emits("applied", filterData);
  Modal.getOrCreateInstance("#search-resumes-filter-modal").hide();
};

const handleCloseModal = () => {
  skill.value = props.modalData.skill;
  experience.value = props.modalData.experience;
  location.value = props.modalData.location;
  language.value = props.modalData.language;
  candidate_language.value = props.modalData.candidate_language;
  timeRange.value = props.modalData.timeRange;
  showWishList.value = props.modalData.showWishList;
  Modal.getOrCreateInstance("#search-resumes-filter-modal").hide();
};

const getSortMultipleSelectedData = (
  field: TaxonomiesKey,
  arrCompare: string[]
) => {
  return taxonomiesStore[field].sort((a, b) => {
    const aInFilter = arrCompare.includes(a.value);
    const bInFilter = arrCompare.includes(b.value);
    if (aInFilter && bInFilter) {
      return 0;
    } else if (aInFilter) {
      return -1;
    } else if (bInFilter) {
      return 1;
    } else {
      return 0;
    }
  });
};

const getSortSingleSelectedData = (field: TaxonomiesKey, valueCompare) => {
  return taxonomiesStore[field].sort((a, b) => {
    return a.value === valueCompare ? -1 : b.value === valueCompare ? 1 : 0;
  });
};
const getSortSingleSelectedLocationData = (valueCompare) => {
  return props.locationsOption.sort((a, b) => {
    return a.value === valueCompare ? -1 : b.value === valueCompare ? 1 : 0;
  });
};

const removeDateFilter = () => {
  timeRange.value = { start: "", end: "" };
};

// get state from modal data
watch(
  () => props.modalData,
  () => {
    skill.value = props.modalData.skill;
    experience.value = props.modalData.experience;
    location.value = props.modalData.location;
    language.value = props.modalData.language;
    candidate_language.value = props.modalData.candidate_language;
    timeRange.value = props.modalData.timeRange;
    showWishList.value = props.modalData.showWishList;
  },
  { deep: true }
);

watch(
  () => props.locationsOption,
  (locations) => {
    locationOptions.value = locations;
  },
  { deep: true }
);

watch(
  () => skill,
  (skillValues) => {
    taxonomiesStore.setSkillsData(
      getSortMultipleSelectedData("skills", skillValues.value)
    );
  },
  { deep: true }
);

watch(
  () => experience,
  (experienceValue) => {
    taxonomiesStore.setExperiencesData(
      getSortMultipleSelectedData("experiences", experienceValue.value)
    );
  },
  { deep: true }
);

watch(
  () => location,
  (locationValue) => {
    locationOptions.value = getSortSingleSelectedLocationData(
      locationValue.value
    );
  },
  { deep: true }
);
</script>
