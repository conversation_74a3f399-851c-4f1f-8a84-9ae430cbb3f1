<template>
  <!-- Note:  -->
  <div class="d-flex justify-content-end align-items-center w-100 gap-2">
    <p class="text-sm" v-html="translate('search_resumes_note_candidate_info_total')"></p>
  </div>

  <form
    class="d-flex justify-content-between align-items-center w-100 gap-2 flex-wrap"
    :class="[isShowFull ? 'mb-5' : 'mb-2']"
    autocomplete="off"
    v-on:submit="onSearch"
  >
    <!-- Search Input  -->
    <div class="search-keyword-container">
      <div class="search-keyword-list">
        <span
          v-for="(item, index) in keywordArr"
          :key="index"
          class="keyword-item"
        >
          {{ item }}
          <span class="d-inline-block cursor-pointer">
            <inline-svg
              width="16"
              height="16"
              src="/assets/icons/close.svg"
              @click="handleRemoveKeyword(index)"
            />
          </span>
        </span>
      </div>
      <div class="position-relative flex-grow-1">
        <input
          type="text"
          name="keyword"
          id="keyword"
          class="search-keyword-input"
          :placeholder="
            isShowFull
              ? translate('search_resumes_search_resume_placeholder')
              : translate('search_resumes_search_by_keywords')
          "
          v-model.trim="keyword"
          @keyup="handleAppendKeyword"
        />
      </div>
      <!-- search icon -->
      <div
        class="position-absolute translate-middle-y top-1/2 left-2 search-keyword-icon"
      >
        <span class="svg-icon svg-icon-2 text-topdev-3">
          <inline-svg src="/assets/icons/search.svg" />
        </span>
      </div>
    </div>

    <!-- Show Saved Candidates Button  -->
    <button
      v-if="hasSaveCandidate"
      type="button"
      class="btn-topdev-2 btn-md d-flex align-items-center btn-outline"
      :class="{ 'btn-save-candidate-only-active': filterData.showWishList }"
      @click="onFilterByWishList"
    >
      <span class="svg-icon">
        <inline-svg
          v-if="filterData.showWishList"
          src="/assets/icons/bookmark-fill.svg"
        />
        <inline-svg v-else src="/assets/icons/bookmark.svg" />
      </span>
      <span v-if="isShowFull" class="ms-1">{{
        translate("search_resumes_show_saved_candidate")
      }}</span>
    </button>
    <!-- Open Modal Filter Button -->
    <button
      type="button"
      v-show="hasFilter"
      class="svg-icon svg-icon-2 text-topdev-3 position-relative btn-filter"
      :class="{ active: searchResumesStore.isChange }"
      @click="onOpenModalFilter"
    >
      <inline-svg src="/assets/icons/filter.svg" />
    </button>
    <!-- Search Button -->
    <button
      type="submit"
      class="btn btn-danger d-inline-flex align-items-center justify-content-center btn-md btn-search-candidate"
    >
      <span v-if="isShowFull">{{ translate("candidate_list_search") }}</span>
      <i v-else class="fa fa-search fs-6"></i>
    </button>
  </form>

  <TheSearchResumesFilterBar
    :filterData="filterData"
    :isSearchResumeDetail="!isShowFull"
    :locationsOption="locationsOption"
    :cvLanguagesOption="cvLanguagesOption"
    @reset="onFilterReset"
    @openFilterModal="onOpenModalFilter"
  />

  <!-- Tabs -->
  <div class="box-nav line-tabs-candidates">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center nav-tabs flex-1">
        <button
          class="btn-tab"
          :class="{ active: !searchResumesStore.isFilterByResumeUnlocked }"
          type="button"
          @click="() => handleShowResumesUnlocked(false)"
          :title="translate('search_resumes_all_candidates')"
        >
          <span class="line-clamp-1">
            {{ translate("search_resumes_all_candidates") }}
          </span>
        </button>
        <button
          class="btn-tab"
          :class="{ active: searchResumesStore.isFilterByResumeUnlocked }"
          type="button"
          @click="() => handleShowResumesUnlocked(true)"
          :title="translate('search_resumes_unlock_candidates')"
        >
          <span class="line-clamp-1">
            {{ translate("search_resumes_unlock_candidates") }}
          </span>
        </button>
      </div>
      <!-- ! Sẽ cập nhật khi có chức năng sau -->
      <!-- <div class="d-flex align-items-center gap-4">
      <div class="candidate-status status-actively-seeking">
        <div class="candidate-status-icon"></div>
        <p class="candidate-status-title">Đang tích cực tìm việc</p>
      </div>
      <div class="candidate-status status-open-to-work">
        <div class="candidate-status-icon"></div>
        <p class="candidate-status-title">Sẵn sàng cho cơ hội mới</p>
      </div>
    </div> -->
      <!-- Download ds ứng viên đã unlock -->
      <div v-if="searchResumesStore.isFilterByResumeUnlocked && isShowFull" class="d-flex align-items-center gap-4">
        <button
          :disabled="totalResumes === 0"
          type="button"
          class="btn btn-sm btn-success d-flex gap-2 ms-3 fs-6 py-2"
          @click="downloadUnlockedSearchCandidates"
        >
          <span
            v-if="isDownloadBtnLoading"
            class="spinner-border spinner-border-sm align-self-center"
            role="status"
            aria-hidden="true"
          ></span>
          {{ translate("job_list_download") }}
        </button>
      </div>
    </div>
  </div>

  <!-- Total candidates -->

  <!-- Modal Filter -->
  <SearchResumesModalFilter
    :modalData="filterData"
    :locationsOption="locationsOption"
    :cvLanguagesOption="cvLanguagesOption"
    @reset="onFilterReset"
    @applied="onFilterModalApplied"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, watch } from "vue";
import { Modal } from "bootstrap";

import SearchResumesModalFilter from "./TheSearchResumesModalFilter.vue";
import TheSearchResumesFilterBar from "./TheSearchResumesFilterBar.vue";

import { fetchProvince } from "@/api/ams";
import {
  useLayoutStore,
  useSearchResumesStore,
  useTaxonomiesStore,
} from "@/stores";
import { translate, updateQueryParam, getQueryParam, showWarningToast } from "@/helpers";
import { Skeletor } from "vue-skeletor";
import { exportUnlockedSearchResumes } from "@/api/search-resume";

interface Props {
  isShowFull: boolean;
  totalResumes?: number;
  hasFilter: boolean;
  hasSaveCandidate: boolean;
}

defineProps<Props>();

//Define store
const layoutStore = useLayoutStore();
const searchResumesStore = useSearchResumesStore();
const taxonomiesStore = useTaxonomiesStore();

//Define data
const cvLanguagesOption = reactive([
  {
    value: "English",
    label: translate("search_resumes_language_english"),
  },
  {
    value: "Japan",
    label: translate("search_resumes_language_japan"),
  },
  {
    value: "Chinese",
    label: translate("search_resumes_language_chinese"),
  },
  {
    value: "Korean",
    label: translate("search_resumes_language_korean"),
  },
  {
    value: "French",
    label: translate("search_resumes_language_french"),
  },
  {
    value: "German",
    label: translate("search_resumes_language_german"),
  },
]);

const locationsOption = ref([]);

const isDownloadBtnLoading = ref(false);
const keyword = ref("");
const filterData = reactive({
  skill: searchResumesStore.resumesParams.filter.skill,
  experience: searchResumesStore.resumesParams.filter.experience,
  location: searchResumesStore.resumesParams.filter.location,
  language: searchResumesStore.resumesParams.filter.language,
  candidate_language:
    searchResumesStore.resumesParams.filter.candidate_language,
  timeRange: searchResumesStore.resumesParams.filter.timeRange,
  showWishList: searchResumesStore.resumesParams.showWishList,
});
const keywordArr = ref<string[]>([]);
const LOCATION_REMOTE = 9999;
const LOCATION_OVERSEA = 9999999;
const EXCEPT_LOCATIONS = [
  LOCATION_REMOTE.toString(),
  LOCATION_OVERSEA.toString(),
];

const onFilterByWishList = () => {
  filterData.showWishList = !searchResumesStore.isFilterByWishList;
  searchResumesStore.setResumesParams({
    showWishList: filterData.showWishList,
  });
};

/**
 * Resets the filter data if there are changes in the search resumes store.
 *
 * @return {undefined} No return value.
 */
const onFilterReset = () => {
  filterData.skill = [];
  filterData.experience = [];
  filterData.location = "";
  filterData.language = "";
  filterData.candidate_language = "";
  filterData.timeRange = { start: "", end: "" };
  filterData.showWishList = false;
  keywordArr.value = [];
};

/**
 * Updates the filter data based on the given input parameters.
 *
 * @param {object} data - The input parameters for the filter.
 * @param {string} data.skills - The skills to filter by.
 * @param {string} data.experience - The experience level to filter by.
 * @param {string} data.location - The location to filter by.
 * @param {string} data.language - The language to filter by.
 * @param {string} data.candidate_language - The candidate language to filter by.
 * @param {string} data.timeRange - The time range to filter by.
 * @param {boolean} data.showWishList - Indicates whether to show the wishlist or not.
 * @return {void}
 */
const onFilterModalApplied = (data) => {
  filterData.skill = data.skill;
  filterData.experience = data.experience;
  filterData.location = data.location;
  filterData.language = data.language;
  filterData.candidate_language = data.candidate_language;
  filterData.timeRange = data.timeRange;
  filterData.showWishList = data.showWishList;
};

/**
 * Opens the modal for filtering search resumes.
 *
 * @return {void} No return value
 */
const onOpenModalFilter = () => {
  Modal.getOrCreateInstance("#search-resumes-filter-modal").show();
};

/**
 * Handles the appending of a keyword.
 *
 * @param {Event} e - The key event that triggered the function.
 * @return {void} This function does not return a value.
 */
const handleAppendKeyword = (e) => {
  if (keyword.value.trim() && (e.keyCode === 188 || e.keyCode === 13)) {
    if (e.keyCode === 188) {
      keywordArr.value = [...keywordArr.value, keyword.value.slice(0, -1)];
      keyword.value = "";
    } else {
      keywordArr.value = [...keywordArr.value, keyword.value];
      handleSubmit();
    }
  }
};

const handleSubmit = () => {
  if (keyword.value.trim().length > 0) {
    keywordArr.value = [...keywordArr.value, keyword.value];
  }
  searchResumesStore.setResumesParams({ keyword: keywordArr.value, page: 1 });
  keyword.value = "";
};

const onSearch = (event: any) => {
  event.preventDefault();
  handleSubmit();
};

/**
 * Removes a keyword from the keyword array at the specified index.
 *
 * @param {number} index - The index of the keyword to be removed.
 */
const handleRemoveKeyword = (index) => {
  keywordArr.value = keywordArr.value.filter((item, i) => i !== index);
  searchResumesStore.setResumesParams({ keyword: keywordArr.value, page: 1 });
};

/**
 * Download unlocked search candidates list
 *
 *
 */
const downloadUnlockedSearchCandidates = async () => {
  const searchParams = {...filterData, keyword: keywordArr.value};

  isDownloadBtnLoading.value = true;
  try {
    const { data } = await exportUnlockedSearchResumes({ search: searchParams, unlocked_only: true });
    window.location.href = data.download_url;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isDownloadBtnLoading.value = false;
  }
}

onMounted(async () => {
  layoutStore.setPageTitle("layout_search_candidates");
  taxonomiesStore.getTaxonomies();
  keywordArr.value = getQueryParam("keyword")
    ? getQueryParam("keyword").split(",")
    : [];

  //Get provinces option
  const result: any = await fetchProvince().catch((errors) => {
    throw errors;
  });

  locationsOption.value = result
    .filter((item) => !EXCEPT_LOCATIONS.includes(item.id))
    .map((location) => ({
      value: location.id,
      label: location.text,
    }));
});

const handleShowResumesUnlocked = (data: boolean) => {
  searchResumesStore.setResumesParams({ showResumesUnlocked: data, page: 1 });
};

// Listen filter data change -> call api update search-resume
watch(
  () => filterData,
  () => {
    //Library error -> v-calendar don't accept reset value -> just hot fix
    if (filterData.timeRange === null) {
      filterData.timeRange = {
        start: "",
        end: "",
      };
    }
    const { showWishList, ...filter } = filterData;
    searchResumesStore.setResumesParams({ showWishList, filter, page: 1 });
  },
  { deep: true }
);

</script>
