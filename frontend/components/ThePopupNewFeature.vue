<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-notification-new-feature"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div
      class="modal-dialog position-absolute top-50 start-50 translate-middle"
    >
      <div class="modal-content">
        <div class="modal-header cursor-pointer">
          <button
            type="button"
            class="btn-close position-absolute top-0 end-0 translate-middle-x"
            data-bs-dismiss="modal"
            @click="onCloseNewFeatureModal"
            aria-label="Close"
          ></button>
          <div class="d-flex modal-header-content"  @click="checkUserUnlock">
            <img
              alt="New Futures"
              class=""
              src="/assets/images/new-feature-image.png"
            />
            <div class="modal-header-content-right">
              <span class="highligt-new-feature">{{
                translate("layout_popup_new_tag")
              }}</span>
              <p class="modal-title-banner">SEARCH CV</p>
              <p class="text-start modal-description-banner"
              v-html="translate('layout_popup_header_description')"
              >
            </p>
            </div>
          </div>
        </div>
        <div class="modal-body maxw-512px maxh-136px cursor-pointer" @click="checkUserUnlock">
          <div class="d-flex flex-column benefit-list">
            <div>
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_first") }}</span>
            </div>
            <div class="benefit-item">
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_second") }}</span>
            </div>
            <div class="benefit-item">
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_third") }}</span>
            </div>
            <a type="button" class="btn btn-lg btn-search-cv">
              {{ translate("layout_popup_body_button") }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import { Modal } from "bootstrap";
import { useAuthStore } from "@/stores";
import { useRouter } from "vue-router";
import { translate } from "@/helpers";

const router = useRouter();

const onCloseNewFeatureModal = () => {
  Modal.getInstance("#modal-notification-new-feature").hide();
  localStorage.setItem("lastClosedTime", new Date().getTime().toString());
};

const authStore = useAuthStore();

const checkUserUnlock = () => {
  if(authStore.user.is_unlocked){
    router.push(`/search-candidates`).then(() => {
      onCloseNewFeatureModal();
    });
    return;
  }

  onCloseNewFeatureModal();
};

onMounted(() => {
  const modalClosedTime = localStorage.getItem("lastClosedTime");
  const currentTime = new Date().getTime();
  const timeDifference = currentTime - parseInt(modalClosedTime || "0");

  if(authStore.user.is_unlocked){
    if (!modalClosedTime || timeDifference >= 24 * 60 * 60 * 1000) {
    Modal.getOrCreateInstance("#modal-notification-new-feature").show();
  }
  }
});
</script>
