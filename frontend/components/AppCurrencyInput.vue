<template>
  <input
    ref="inputRef"
    type="text"
    class="form-control form-control-solid rounded"
    :class="{ 'is-invalid': isInvalid }"
  />
</template>

<script lang="ts" setup>
import { watch } from "vue";
import { useCurrencyInput } from "vue-currency-input";

interface Props {
  isInvalid?: boolean;
  modelValue: Number;
  options: any;
}
const props = defineProps<Props>();

const { inputRef, setOptions } = useCurrencyInput(props.options);

watch(
  () => props.options,
  (options) => {
    if (options.currency === "VND") {
      setOptions({ ...options, locale: "vi-VN" });
      return;
    }
    setOptions({ ...options, locale: "en-US" });
  }
);
</script>
