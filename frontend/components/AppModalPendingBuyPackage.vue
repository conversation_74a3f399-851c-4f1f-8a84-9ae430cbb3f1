<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-pending-packages-usage-topup"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-5">
          <!--Body modal-->
          <div class="row">
            <div class="col-md-12 text-center">
              <div
                class="h-64 px-10 bg-white flex-col justify-center items-center gap-5 inline-flex overflow-hidden"
              >
                <div class="w-48 h-48 relative overflow-hidden">
                  <div class="w-48 h-48 left-0 top-[-7px] absolute">
                    <div class="w-48 h-48 left-0 top-0 absolute">
                      <svg
                        width="192"
                        height="185"
                        viewBox="0 0 192 185"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                      >
                        <rect
                          y="-7"
                          width="192"
                          height="192"
                          fill="url(#pattern0_7940_15844)"
                        />
                        <defs>
                          <pattern
                            id="pattern0_7940_15844"
                            patternContentUnits="objectBoundingBox"
                            width="1"
                            height="1"
                          >
                            <use
                              xlink:href="#image0_7940_15844"
                              transform="scale(0.00666667)"
                            />
                          </pattern>
                          <image
                            id="image0_7940_15844"
                            width="150"
                            height="150"
                            xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAAXNSR0IArs4c6QAAIABJREFUeF7tfQd8FVX2/7l35rXkpRNSSagJHRSwKwFXkKYoRezorqKrqKjIopSHqD9s2P0vrP5Qdi1LUVaKiiAg0gSRXgMJENIgvb0yc+9/z52Zl0coqY/2e+MHk7w3c+fOud8559xTCQSOAAX8QAHihzEDQwYoAAFgBUDgFwoEgOUXsgYGDQArgAG/UCAALL+QNTBoAFgBDPiFAgFg+YWsgUEDwApgwC8UCADLL2QNDBoAVgADfqFAAFh+IWtg0ACwAhjwCwUCwPILWQODBoAVwIBfKBAAll/IGhg0AKwABvxCgQCw/ELWwKABYAUw4BcKBIDlF7IGBg0AK4ABv1AgACy/kDUwaABYAQz4hQIBYPmFrIFBA8AKYMAvFAgAyy9kDQwaAFYAA36hQABYfiFrYNAAsAIY8AsFAsDyC1kDgwaAFcCAXygQAJZfyBoY9P8GsBwOCg4Hq7ncq9LS5NS0ELPdLNtMjNipiQepKonkjDfjBMII51ZCaRDjYCOEW4ETAoRwSjjjhLuAgZsDr6KElDLOiihAGaVQrBKpyGZm5cBNrvkbwD1i/nxxbwLA/69A7v8UsDiu7bx59I/c7yNTyqQ4ACma0hPNOEAU4SSUc24FwglwcjoAKFDxOX6PMKEMgHMOhGg05KpxjcoJuAgnhYSoxW7WrFBVITc90p57Vafokml9HGwqAN5Ex9gZ7nUZoO+yB5YDgDoAWO6b/YKD3cGt7J7S9gBqM2ayhTGmmBkjkgEOSmn1khIqaCM+0VHAGIJJBwICDM/hioYQ8TvX6ckFh2Kcc7PqVoBKbg+vLDFRuaCcNttn94Slg+Mz1+XMwS57YO379IUQdyW5oXXJvm6Egi3IXcYRHW4qI5uhCB1KJSZAxfVCdNXsRGBGAKrmYQCMMg1YnFMBLuPg2uey6tG4nKQQAKI6SaTMGa84HJK6SXbClvYT3ii7DBjUaY9wWQLrxKcPh1izixIpoW2DWGlP7anN4v+VZg0ksqJxJ4NHoaRjQsbV4xCiUReFZ7nMGJGh6BRAQz2Nc1lxqwCElZrsf1hM5EAxaZUdO/6tChTXlwMnuwyAheKHcBR5vR0O2iHiWPsqxdkpqqK8jUkymaxqGQUBmHMDqx5wqnGqj/g8wyCnAUv/QFY9DHW0UnOQZCJSaa4p7AinfH/rcXN2EUI4jBghwfz5asPndWGvvAyApRGQvzI4AVTWF4gnDgixuZEXEUIZoMirZk2yvrDGgiOfOvU4N9diOo87O5yMb04dx7hOKPmEE7esnSd7JM64SoI4UwBopSqVZLtB/cU2aUP2pcy5LhVg4TzPuFXfPvf54Koi91VXF6dfA4SYgCgyMJW5JbMEwBnjkiau9PW+aIAlSYL2skpQ0VPNKpNxk8DkSqCEVO4J7rZecUlbu41/q+LC8p6G3f2SAJbY2Wlbey+4+PsDLK583lK2Sb04h3bAQQIgHhDrVPuhq1jeE82K9ivTr67mN9pvxudU/wV1MsEIdcXe+L72O/ucwQlhhHPcOCg655QZqMCJLDOPB4AeBh62DrJDj5PZsz31GvsCn3zRA+tMyuyWWbOCup1cej0w6ApEDcW9PnKn+tDyogAWAlaftAFMwVE5ITJD9Qo5blgxA751e9zNW3qOGVNZn2e8kOde9MCqSZxj4661JUaHD2Igd2RoN2LcBES85WK3VXdiepdU03WMJdZXWBHmCICaAKzmWPXC8VmnpW8WT+WUnHCDMcrMrQLhHlmR9wDYl01zzFfQLlf357wwZ14SwHI4gE6d6oDds7MTU/IO30mARjEqMeCSJvfQToQHgkv7+4wAM+xRmiH0IgUW6oMciAYszmSmgJivmxKVqce3hd2wbFmZctxxBhfVhYHQme96kQJLMyHglEcASBNmPUp75B1JAe661WkKDtV0G8MOVceXF/UZXf+XCeUMtAVTgILi8XC3KUhCO5YiBYn7UsXNZCpTK6/EheZm7kKTJ5G5NojB0Rq7mAbHOmUcgnPVXxCmc05ZAcqp2+ypKAGQV0DHvxwkI0fq5ohqejV2Pk11/UUKrOrHQx3r1/ef7Xpd0dY0SkikUw4SX9YbWL4U44QrqhsoJYRJJqJ4PKCYg/jevfub2yKiLZyQisLjWdaQ0JCqDi2al0my5LYSXGHOqKr5C/0KLJ/NAvgACx/B6vFwALVoZ2SbH7uMnb3/YjVJXJTAMhR2/Ol23NaFynwY5RZhNlDAqQEL9G2cFzBn2wzquzrD3sAYbueFC0dhqmk5tDb/vuX39lmWqG4jhg+P/iMjhyTEJ1ivv7Kjcuzo0dzMtSuOZmQeTr8tJfZ4WHi4J748S9fJDD26TpvQWnWsmifU3GXK+u6zEqMrKAWZyaqqer54yrH02GyAi27HeFECC4m8yuGwWuPcHa8q2H07U9ERZ6aKqnKQNUA1FFgIA4oci6ly/ol8y9jFO285ecLduUWfNOnAgQPQrHUnMJlMvHPreNKlSxc+7Oqu5OTJk+rWL2YVHj2WuX5Y1+RD4eHh5TJcAGBxSpxmbcnQsAqcKwdC2nxTFVxxoOeYi8sccdEBC21WU9Fm9Vr/Tkzh/ZlkFjoVHqh8V0cgVNvOz6UXGJZ1tx65wCQTA07oIiU56NNPPh20s8LaoU2bNpCZmQmRkZEQFBQEVVVVUFBQAKWlpdCn35/A4XBAjw6t+Keffure8cXHv912+5B1N1QcUmRZ5pS4NJ3L63U8dTaN42dneDJOCCWEG35N2VVRBFz+kbyyYm9T6UdNMc5FByx8qK0fPBx/RVHWcAY8ilHNx3f60TBgKdzEc3KOh98/f8vtVZU8mSV3Jrm5uQKwqampgL8juJB7Ib6p2QTDhg+HV196FmJiYuDLiY/D90u/3/7h0B7LIiKilPMOrBqEkN1VHCRacETuvKDlS+/mNAUommKMiwZYqE+NBKAfOdJsoVLonzl1R1AKnKpSo156Q2S69VG2B3WFd2a+O2hZaVSP5s2bQ3HBcaioqID27dvDsWPHID4+Ho4cOQJOp1OAjXE3REdHw+OPPQ5PPPEElBSVwKhRd/OI3O17nnn6qe9ucKcriEA0C/gejZp0PVaWSR4GBGQXtxwLiY2aSx76TFNCL/Bx0QALzQoPLXtf7rP1p5FApHZAXRiyyaiq+dQaehjAUmQECZM+2FScvGDRpnvLOvckWVlZXHGVkNjYWLBarXD8+HFISUmBnTt3Cveix+MBkxmjkQlceeWV8MILL8DA/gP5J598ArOeHqu2axe5+X/vvW4lxnPJqP8RlRiBOOcDWOhWUmS3iCtzY1Q1V3aFTez5DSGnh2E3lH4Nva5Ri9bQm/peJ8KF8Xh/gBlKLNe6qXItA7BRlENcxJc3ao64mZKJxAvNdpqTfdx+93fHBhw9diz1xptvIWvWrAGFK9ClSxfIyMgAVVWFONy2bZswoCqqgoq8AFavnlcLHWzBnL/Djp074ZFxf4O9e/fCe33jv+jbt8+hpIo8EaUgQpZRudbNBEYcVlPQ6kxjKDg5Ed9lqAyFK2TWZx25wAbURi1aUxEL3cuuF29uR02mQapFjkBCCXsR7uCaAlhUhnxiIlt++z3uzz8XDFNVNaJj9ythy5YtwAiDHj16wNatW4XIs9lskJWVBYriFoDCDYMIjqAy9O7dG6aNfwp69ewJQ+//C/z+++/QpyQ7c8xjg769NoKWASeMEY9gVucNWEbUhg4s2VRWDKp7AZn0y7GmWp+GjHNRAGv7m88Hd6rYPUyipnYMVPHKGz66xooUNFUAJbTUEso3rFvfYuTynIdQtzJxAjm5uaASKpRyRVHEbjA5OVlwIs5VobxLsgRocg+1h0FkRARMHP8sjLp7FDw1YQL88ssvUHpwHxs0qO+/P74yNB13a26qqThmRbOY+5tjGa4pahhSAUCi8q7fglouue7Zd6oaAoqmuOaCAwtF4eaZ42/qVX6gL66ksY32AquRHAuNoQBUKpbtbO3atUn3rS64r2fPnnJZYTE5cfIE5BUUAupYqKjn5eVBhw4dhOmhorIMw7oEt0KAyUSG62+4AcDjhP/85z8w7qUX4cCB/bBrxWqIiw/KWDmqx/zwsKgqxYQ5EhcQWNqEnQftbVd0fvad35oCJA0Z47wDCxUnVAqMyfJXbm+hgHo3BRIsohV0B3LdjAl1eGROmCxTqVAK5us3ro8fvar0wbmfz5XX/fYbad26NcRENxPirqSkBLZv3w6yLENiYqLgYN9++y38tnmjuAlnBFq1agXNmzeD/v37w6pVqwQA3eXlUFZaWv7vO9p93qVrl5PhaokOLI3X+p9jnUoDxjS/PCWkWLYEfUFemJ9bByo1+SnnHVi+T7Bl1ixT9/zFozlACwBAh6rU1MASOyem0nJrGDuccShiyILMJ//xj39A2i23EEmSwWYxacDhHA4ePAhhYWHCMNquXTthcujbNw1+2/wbUCILJT8ltS089+xzcPTYUfjwww8hc99+KCgsYK91pYvvuuuuHXZXgXhpzDrLPd/A0mLT8CCMELpfenHRV9r+SNsm+b7UTY4mnwEvELBEeguUv/7X9lbPkTsAwKJHgLL6xVTVThpKULwSWiyHsG3bt8eM3aI++Plnn9uS27ZDJzRER0V6LfqoY+G/kJAQwblQqvz44/dw9z13Q0V5BcyYMQNatGgBQ4YMEd898OADsHXXXp6ZkUHGJ7l+eWD0g2uT1WwRcWAo7xckdMqb+wiVuSGWbxOfXphe7aw+P5EQFwZYDgfNDT5ksykhtwUpR1O07HOfuKra8VLnMzAdHjd2xZYQsn7duriH11aOWvnTipCTJaVQXFwEHVJThHEUgfLTTz8JjnX11Vd7udiJE3nQr18/OHDwACCn69a1m7B1YQrhxIkT4Yef1/CDBw+QCS09Kx56aPTGOOdRIcUvCLCMTG0tLk3MIzcoaGdiZNBS8tBnLo1tXcbAEkx50i1tQZKGKbJs9aKkkYr6mdCGLkLcGVaawsiRI5mhN/1r99iJEydKY599DiSJql/8cy4ZPXo0RWChXoVKvJEqaPzs3ftG2L17N9xxxx0wYcIEwbUyMjPgxRdfhMN79orfX2ovLb3/vvv/iHZmiQU1soNOzwKq8zvR8BN9omllxsrAZFkAExcdQa5l2A39HW5z3jmWcDI7HABk4+0AtJuCK+8HQHlXhWOyAkApCSEH9u+NHrg4674hgwaHfPyPT6CoqJAvW7IYrr/+emK32yEuLk6IQN/D7XbCm2++Ca/9z2tw3bXXwSOPPALX33A9vPnGmzD3n3Ph6u5X8B3btpW+fVPsohtvuP5YjDP7wgPL5wFk8XbwTeSlpUsbjtT6X3negYVTPPja2Ogkz+FHZVmysOpiGvWffR2uYNzDZUki5WCH/Xv3RQ1cmn0fU9Wwzdu2QWRkFGzasA7Cw8OFSweV9yuuuAJwt4hWeEnCsg6q4FIf/X2W8BWuWPUznDhxAkpOFIi73xQTySSTvPnjfklrI6OaVcikStDUiHbwxtLXYa7+OIXJKlXdSklOTI9/tBnzeok/7nGmMc8jsAzXDOF73xjbr3Xl4RtkSWJM+Nf8dzDuJrIk83ISQkpLS20d3vtlLKWSecwTT5Jp06bBsiXfeQGFrpxnnnkGLBaLz4RUeOPN1+FQxhGIiIiAz/85F06ePAmYBIhgvCUpPu+2O4Z+OcpyuIQpGERYJXZlFwuwFKqg6YFnSq1Wprzw/371twg0COfXRfWFC8r2aQDksef72SKi7E9Rt9si6iVI/gWWoriJ2SzzSrBCfn6+6db/pI/KOp7X0h4aSlAZ73H11fDdd98Bd3O49957IDwyXJ+2DOvXr4fjx48J4C39YRmcPFkABcUnobioGBJcIqST/PSXDv9qmZR01ExkFVRRAkszwWnljogioU0JuAY0NP8amdmnJnMYse/U+N5LvDrG9J/j3UQ6U+IqT889/H67D9JRiff7cd6AZTyJa8btXZhKhpkZo1o2iubC8dfBuEJkifJKbuNVzgrTjV/uHJF1PLudwgBimjfnffr1J08//RTkZeVBXl4uxMTHQHBwMPyyZr0wiBYWnoTFixfDmrW/gD0kBDDBurCggLdS1cobe1+z8oMbQ3eYTWZV9qjoWKzO18ZoH0zjklDHwdQNPFCV181M3iwK7fH9CSyRpwiVKkjyv8mLy/f5i9a+455XYG2Z9aipS27mnQC0A6UyZZwRSrWEFL8dmPHCORebBCD0nmVHb1i/YVtapSkYFFVlUTHNaUbGYTArFTBp0ktwTZ8+cOjwIUhIaCmc0h1SugsL/AvjnoKk5GTIz0+HygqX68X+Vy7qk5aWHipVqAR3YXqEqijogUkPijDJEZunSvwdysrFI1oV7adxMCOyVQdcTWI02leKc+NAKFMZAdh5t+P7b+drxmgCDvHPL/Q/b8BCUbhtzjNhnY4fGCVREgcqxp0zoOKN9uOh2wzcIhme0Yd/Ptlzw/qNfQtUm0VVGTSLi4EpU6ZA69gwWLnyZ/7gY2MIBvuZrXYRMiNxGd5550OY8uIEuOWWW8DpPKGEhYdtG9TcVGWxWIJDoNwkngCxJUmY8aO6qlwupaSkzOl0lkVKUBgVFVXSMT68DE0Zdj0Z5NzAwrU20tsaRxtRu0LY1URYd97y5Bvn9ntgfKXQtdBKXaMWWOPuVn21fxfVex9OHEDIc44/d7SacgZQQuxOYuIoOazc4+c5aAvkxjhxopLXd3rafPnV8nsKJOCyDLS9C5yJcZDecdj9uwfeeuvInoPuJsA9oBIZVGBgBhlGjx4NRWW5cOedwyDUFMTSevemktWCSjFonNBHAupeUJOeVZOXd8y1Z/dusn/LhtIjR45kRVUU7rniyiuy2gZDlSRT2qI0l1GZUpkrWBKEU4xExZA9atYiWAED+RpxaFZ4LjNRFK5ib3jcog5Pz/KxxDdi7HNc6udFrb4zdzioU8q6SSbZNwBwk1OPZbfWCOlt+sesBhYq1LNzgqM//ODrx5SooLzmzaNzJg+8dnVEVDPXyqyiK0c/+GC/5F69BfdBZRv/w2XJz8+H9Mw9cPToUbjl+jQRYdq+SydoFtXsnMASMXhEz8xStAgWde9WWPfruqrivZvTg+zBezuSstxgq60i2CS7sLSgCHEmnCteJb9mmlsDKMQJAgvHde+OSFjTaezf1+Mo/twhnkdgjbaCK3OwEmTvioSrrjPVVCLe0EZOzSM0RjeLN5/QTeknQrKzs6M7JsedaNWyZSkjNkVRVfrQ2pN3Lfx+WSoQCcNONG1aVSH/5AnYtWsnKO4q6NixA+zfnw6xMTGQ2qmNbqHX7ivVLFODujvnGEiIHINIlArOAVzsCqlToZCeng4n9m3OO3I0M8N6eNvB1m1bH01x5QhfY7hb08UMjtgAOJ16iS4SCVP+kHjlj+BY7cJdur/qQPgdWBgdKjyBf7shHGTLSMVqjccnvjDAAigkUSKT2c5dBI2zLsVk2n/gYMinLOWxf8ydK3MqA5FkANUJe3bvhgOHDkLHjh3BYqLC/HDddTeIsBpGkMGILMXagAUCVIRyVVUoAku4j7gJqERB9hTwsvx8Dns3ls+bNy+rg5q/pn379vnhnjKiKIxQs1mEaOvVnOtZ+MQHWwawuHpUMgctJH+b71dj6XkDVtUrg+Ml5rmXUjlYe9xTOUz938iaEVunjieiVjjhZuGQ5RQoZ4pHIflBcYwDoxk0nK1bvy5ljxx//a233pp4LCuP9OzZk7RLaSdCk3/9ZQ106dwZBgzsLyJFUfyNGfMYmEynunzqOG9v7e3Tz9eew0200gHr/v0P99Ily1Y+3SN6CzFJPLb8pPicyQow9GUyTfeqa10uoxRBdfiOVCIT+IK89F3+2YrZ1fGZznnaeQOW29EvFQgdhTun8wYssRsSdZGxYDF3uz1kW5EabTKbOhRGtex2y5BBYUfMyXTHzh3QucuVzKN4KOpTaGYIswcLC3vmkQxo2TIZOnfuDKqCpiCpIXQX4vDMF54KLHNVvnp87x6yf870Tc2aRf3a2QKVCmcSkxQVC3zLzIpcizQYWERSZKp+QSYuzRAe6Ut7VwjgeaXfTYyRmzEpoWlEYY0yRLqO46ZGsIS2m3JjIjUnfFmJObKw8GT3lCEPt2vduk1MXOs2BN98SXGK6ITi4mLIz8sHl8sFtiAbxMfFQ2VVFaSmpEB4RLgIU0bR1eSHN5ZWjzhlCsdU5307N7Mvv/hy54NhBT/Y7aGu5s5KtLtxofshsOo4kZocC80PhPOl5imLt1zSHMt4fuWV/rerKrnC4FiN17HOAiysjix0Hw1YlSSYpR862KK4Q1rfP/XvnwTtriUY8KCa0VxAOWVOrJsnQizzcvMIpnyZzWaIiIjUxJ6PmU3YR4TJqgkZfQ1gATJ09Aq5yiAnJxtWjh20+dprrlvegopC8oxRw4ZfN2SdJgqJxADYr6aXlvx8WQDL/Ur/+zmDtrK+jTZ2Ow2NV6ouY6QR2Ei+wLh5dLoq3MYxyGqO0iwqLCxs+J1jnm1utlgwNkugQuzRdN6poaVuC9XkZ9Wch34DVefsBXv+qJw9a/ayF+Iq9mLgPZM9At1m3UyDlrZzHWfiWEDYdsukJYsuD2BNH/A4B4iTubak/gKWiHFXPJSaQtUjRzIjNnfoe+eo++5L9NhjsPwPIUTTkS4VYEnlJ2HJ/PlHOm9bsCgxIaFU0YuQNAZYhLPD5ilL5uqdgOpRXrPur9V5e089L9/6LAOIkAFZceOBVfMRvXl1BFuMgLSCJ1sqyisG3jx+esfQ0FCQmUiLqhZtdVVS6k7Lep6p29u8K6BPSC/6jDUH8WCSBBmZmfzAR39b07Jlq7VtyjIEEAxVorZ4r9M4Fhp9Gc8xT1ny90seWLicysu3TmDAbU0lCgVxdZ1H234bxWhV6nZ7+JyjcN1jjz3ah/a4WXjyJOyl5KsbNQGwfJt/1RNV3hqoQrvTeKj2owawVHTrMAa73h/vLCkq+ei6oIoKYdeSJIKfNwhYnJ00T176wSUPLKSXZ3r/ydhpS0bLdhNxLAEsBJWozucWHbhO2BJJZmZmC8+gx4ak9e4daexC67/wZ77CABP+9D283eX01fLuIoUZ7ezSphpY+mgGsIygA010c6gqIk8++eS2aW3pEskkK3bmxqgNUpuOeiaOxRkrsk5Z+u5lASzXy/2nYQcGCpohqLE6lvaOU28ZawWduEDhCETSyoryYYlPzGjfrFkzMHSqpgQWjiWs8yiuaoDGy8WohFkctd62VmChRwjvxSph5bKliv3bdz5tl9LuhB2wfBFBe/45daQzA4uXWKcsmXlJA8vIfHZNu9Uh6l1xzRjUFMDCcZwi8INxq2RDUwGZsMfT/bnnnx2Y1OFKCpKspzvVur51PuFsnEqwFR1khFLwuD3AmAqyJDfUqKqLRuTKBJiqelwul7xw7Mh1nTp3Xt3JnaUD69y17c/CscqsU5a+dVkAy/Ny/ylMvHuGLmTEGzXOCe2WCGGMgxlMcPjQ4bBdve4aO/yh0QS4VdOpjLAWY9EbYIPy1aWEiDOZoLS4CEyySRhTTzv+m8x6+NBhKCsrg27dutYZtGc8UeeK2JJVlAJYNPvY5i2bv+0TqZRSfKnYuTtynBFYnJdbJy95s3ETO/fV52VXqCnv/ScyQq1NZW7wPhZhgmOtIommwsLC24fP+GdHIap0elPCcUVExRhMMm2IcfOUDr0YsaAyGHLbbaKGA2ZFV1ZWgCyb0MwBCfEJEBEZAUWFRXAwPR26du0ikjPEfQ1Q16eBhs4JGdVKAeT9vsr5zcIFSx8Nzd+jN+9sgCgUOtZ7l7xLR98VjmOEhDc5sEClGKXwbXFISv/+/YeE9LlXeHMRWBiIh93aKioqIchma5hLRgCCgsflgvz8PNi3dx8Ul5TA/PnzISI8HEbeNRJKikugbbu2Ii0MC+ViQitWA/z555+he7fugGWTKKqW9QSUL0/AwEM8XIe28u8Wf7ducNkfq8ySxKhkOidzOI1jEWTx/Lh58uLZlzywcJ3dL9/6CCcksamBVSLbaFFhUdC6NoMGjhw5sr1s17JsTFxzfaDvDwP02rZtK/5uCMcCKsGRw4ehsKhQhCvv2LETUlNTxFgYQnPo0CHBxdBehv+SWyYLfQujItqntoeY2BitL7loD9w4eyRX3XzhwgUZV+1fsiA0NLTK7iqrE7CqewLhRlLdb5my9F+XtCjknBNcAPcr/e9rKpeOiFbAWlqEk1IpmB85mtFRuWf6iF7XXM08YBLKmwnzBTgX0Z/ffPMtjBnzaINAZYhBBOjKFStF9b+y8jIICgqGo0ePwP59+wV4r7v+OlENsKCgEHr16inuhWljGFdvgLBxdi8dBtwDi75dVNnx9y8/bRbdvChUDwg8G0gMjuUFllY/a7tl0uJvLm2Xjqh5Rbhz+qDbCGE9zHoUAtMr32HfrXMd3nQp/TzDrkm5i2Pk53zawWK1WEff8fTE5qKfIco/cWBpXAabNv4OixcvgVdemaZxLGqYAOpmIfUFw9//PksUCAkNDYHjx7OFboUO644dOgolvqqyCn788Ue4fejt4l6Yf7jwm4Xwl0ceEX/jfOrNMb3tF/VNjkRg5/ZtaukcxxfJLVtmxjoxrOrsRzWw9DQztI9xtvbVyT/+7K/oUUFnf7JD37E9rw24nqnkFi3wDguSGVWj6wcswamYAmZJJZi+9T+HzLe8OHHitVKrzhTMZgYYpYlBJbr958UXXhTK89Spk7XpECOGvB7AkmTgqgKfffY59Ot3CwTZggSXio2PE2YFPAoLCmDzli2wfds2eGnyJAEi1LMmvTQJ3njrTSwqqNm16isKawCLgypKLW14buRP7VLbbYh3a4GAtXOsamBxBovMU77/w59rf96AVfba7R0B2HCZ6ZFyNRv11XxK/XujdKQV8cAJd1IrYLZNicUu7dy5IybfaheUAAAZg0lEQVTovpfvvyktzSK4gUSBKR5hDuAKg4rKSuja+Qp4dMyj8LcXxwPH0o/15FhaT3ltJ4gF2JKSkiEqKhIOH84QGdQoDgcPGgzXXIOdg7WykhFRkV7u9OijY+D9998Dq83WSI6lE4iqYpx/PzkqvUPHDl91qjhUy67QuE7vQcQx7ob8i0xennHpA4sDqXr7ziTF5blP5rIW59EQYGF8FaZFEYlkMyrn5GT/6arXFvQStRb05t2iLBRyLJXD9h074JY/3QqTJ02GJ54cIwAHzMgwrwfH0pXuX35ZC/Pm/RuGDRsGN914kzB8ijqlPmaMmqaJ8eNfEKn73bt3FyaPegcL1uxErHFcvuj50ZUJiQnvXOHW6nGdnWP5AItQQlVWDBb6NZnwE3axaNxO4hz39QfHEkFzUVFRfe+7777P2rdLidNkLnqBQVIlt7inSdVFoEG4syREG9VKvd5+LXaSGNtvLIqMAXmqxymU6gH9+kPnrt0Ex5o6dSrMmj0Lpr88Hf7yyGgdWHo6Vi33NWjW2N3chx98LMTmc8+Pa1gUag1gMSqyfviaWTP5yYKCmXdImeesjCzyFQnjTmJDcyqRwJlpsgUtJM98W+LbY7upuZc/gCXmOG7cuAMzZ85sJ3QdcQhgEU61yE5iAKuuSRWGgdlbYhP9Z+gwQgYlYtqBedww49XX4LnnngOXywMDBgwUpoDXZ8yAB0ffCyChnlM/YDWW4P/+ej4sWbIE/vnVF8AVTyOUd20mCCzkfHm/LCOLlyz5+M/RpedUsmQ9khZVCKzhQHnl7+bo1suI6Bbmv+p+fgPW2LFjN7z11lvXmFGh1qph6ITRfp7WWfRsMzE+N/Cp/1S9kgxLvBAtYQI4HNq3XxgybbYgUY+9uLAA3n77Lbj33ruEzw240ZRUH8DPpSOW/bAc3nj9dVj58wrx3PWuKHCaKMRKFBzg4B/k7bdnznmmDT97owBsnycITahZIQwoVw+Hxf3U7qlPtFLQWqxO43xqZ3nz/AUskpSUNHz+/PnzrrrqKh1RQp7rvY6bDlhYsgb/QwkhxBZj8NOKFaIJwGuvvSZ2Yu++8w6Munv4BQHW6jVr4Zlx42D1mp8hxB7SZMAiR/fAm6++9uW4tnDorFwVgYVlojglZkZU4ODKDGu2sM3Tnx10gIM6wH89d/wFLNqxY0e5V69eK+bMmXOj8eD1tuE0UA5t3/oHvPfuTFHpeM/eHfDMM0/Dg6MfAFmIwhr6alNzLGMXKWpSEfj+h+Uw9skn4bvv/gOp7VNBIjU5Zi0PWdOOZYTJFB6DaZMmz5/aunKfKJ9EhUWdYdQIJRJjHAv5oLIuc7fLDWazDW18J5Y0Hzhn8JgxQi+75FLsHQ4HnTp1Ko+Li7v1p59++q5z587Yc0lY4P1uO+MAGYcOwe+bf4OhdwyFrVt/gwULF8Cjj/wZ2qamAnd7tD6EetQDNDWwdKcxJ5K4z+RJU+HzuZ/D3LmfQVpaWuOBhZILPeu5GfDq9OkLXmpRcgAFAUjAQcWaD2IChFFJAEtmZlTsqOjbAHQrTF70H3+Dyv+LDCB//PHHvz7yyCNX1ywa20BmVPtljIPH7YYVK5eLstrhYTaorKwUpbYR2Df36QPBQVoytgBXUwMLGzspKlCzBbKPH4cht98Bx7Oy4P0P3oNhw+4ESfdhGiHIdTZRV3MuwXLLcjPJuKefWXxHl9T9YeFhLruseLCCoJ148AUmFtUltjt2pZyqqkJc5hBFMtOv45+ctd+fu0GvdKp9pRp1Bp04ceKTDz300Lvo9RdP3IB4qHrPgHHYseMPiI2Ng2ZRdnE5gg3tWocPpouy2paQUOAuJ5ZUqPfw57pA6HmSDIqiwtdffQXjJ0zERFN4++034P77HwCi6nY0r7mjjrc3zscNCFN51v7t5OqrerM4J5ahBCkMoDA8DErj7FAYHW0/mdAsrDjUHlbZLja0ICIiwuWUgstffmXBu/MBzkvjpvrqWDX3aKdRJS0tTV69ejUGD4X8twthZOvWrfvMmDFj0vDhwzV71jmQ5Y3APAP4jPZutQJTx4niccLHH38MT419UoQQq0wRHKvwZD4sWLAQOnRoD73T0qo3iVwV3582fj05mmEwRR8nFnT76MNZovTkzHfegLtGjQJoNLBM4nl+WLkahg4dCp4arY0lPcEEoztwLugCMplMLmKyHHC5qrYpTE0HAicIkfIJU3Nk2ZpjtVqLiouLsbwNKoDGm6Zts739UupnTK0vsM70epG0tDRp48aNiR6Ppysh5AbOeRIhJAIAmlFKk4cOHRr56aefcrvdLjJm6vKOYjF/bIK0f/9+IcoGDx4sogdqPVA5Fwq0Cr/+uhbiY2OgZcuWgsDYOwdUjyi1vW/fPjiefRzSevcFTGQVG29VayV3CrjqCSwjmK+yygXPPvss/HPuVxAZGQFzPvsE+vbti1mP2iM0lGPhRKkEb7/3geiMofr0X8BhfYGlqVvaC4NpZAg0DIsU9gcOKiFwgnOaSQjJ55wjsHYzxn6xWq0HysvLa3q3a2UqvmtTp0U+w2KSESNG0MWLFycrijJakqQ/AwACCZcHTeqGlUmkKGFn0tWrV4vWInU9sJbCunXroFOnTmKxsYTQyJEjRTxUbYfmE0Tx54JXp0+DyZOneHUpySwD82gK/Gdz5kB0dAykpKRCSmqKFhhRMwGiNmDVsDOJxrCcw/fLfxSlvXNy8qBlckv4cfn3kBAfD1gtsCHA8kZ5cFmMf9/Dj4hNiQRGAq5ujtLnq+iGZIblvjB6VncnmfRNqZHEYcxXA6HwkKNTESe5hxDygdls/qmsrOykUVu1Bu2Fl+WM3Ka2Rar5vdVqTfZ4PN0ppX8jhFyl3xBTjH0RbfzOxNvCGH388cf5zJkzTxOFNTmE8TeWEkIwYegvFvLHoLmuXbuKaMw6HTpANm/aAAkJCRATG411qoDpBlJsLL5v717o33+AcCgfPHgAWrRIgo7tsbWPz1FPYHkUBVauWAETXpwIBw4cApvNCk+NHQuTp0wSg9bb3KBPxVvrgpiEi+iGvv1g+47tQGvkIRqbEQNYChYaJcJ8iBZ7KmNrcuHP1wY+l2Yi7I7asYAQ8mFQUNDu0tLSQl/qNAZYXlRKkjRMkqSXCCFddM5UXYX1LKtt6EZYGmjDhg0ibNf3OBuwsH/g2rVroVevXoLjYZzTtddeC1FRUXXClWFQLjiRJ7jl0DtuE28u3o9xDvPnfQt33DEUgkPsQgSico8V9g4e3Ac9evSE6OZRAtA4DjqZKYa94IGeKVSg0Wzk8YidpaEbYsfWOf/7GcyZMweys3OhdeuW8MDo0SK6IjwiBFRFqb+BtAawmEph165dcMeoe0RzdOMw9iAG0IznR6uo/vJjMZTTaOcLLCM6o+ZJ+nUVAPA95/xVj8eDTbPPKRrrJQolScqVZTlGdwPULTwA31JJEg2Qnn/+eXj11VfrBAwEJC40ikOMP8LITbTi16q8e0fXRIPLWQlLlyyBxMR4Uacdozn37NkDxUXlcOONN6H30nh1ARQPFJcUiYXD85OSkk4zR2AjDWMOoi2KXoht65bf4fnx42HH9p0iseKFF8YLvTAxOUl0sMA4MCFqhQu5/jtRX1E4e/ZsmDB5qojlN6Ilzgas0/IW60T9U999/S+UlJQxtlNRFEw9Oqc7qF7AMplMhZRS1KUEN62LIm5wJPyJzSVXrlwpejDXJUzXd5dY511hDWDhm7vq558hJztLxL2/9/57QjS+PmNmdQy6ka0suJIHVLcLvv9+KQwYMOA0VxomBWFaF/7Dg0qS0NWw4+qNN90Etw0ZCu3bp4LVatZKAEiaBR5FFIYp42rU6eU4S1ZXUUGp6Do258uvxbjVEbZ6fS0jfrb+2K0VcroOlul2u1v77BbPeF1dgOVFpiRJqKg/SwjBFCtNTlQr6medmAEQ3NW9//778MADD1Qnd9YwLRjn+gLPN329Fr3AZw56UQ3FLXSSJUsXw+DBg0Q06dq1v4KzSoH+AwaKrbsRMozpW87KMtFZddGihdC5cxfRGxpFIfr5sEMY9obG3tFY7Q/jsRITEiEmNhaaNYsSyamYea0lTWg7TOQYOH8qo8dF8alyU8s6ngVYa9esEyUrDx/POV/AEmus7xrXc87/5vF4MPr0rIq7WKNaYVpjALPZ3Ilz3pNSijvB6/XrfW0e+JEBOOO98crje+65B959911cJNT30c9T13nUYapnPgXDir/6ai4MGjxYpIH9um4dpB/MEHrTww89KoL3OnfpJHyJqSmtRIx8ixYJotJfi6Q4MJvMEBRkEwkUwTabAOcpuYJ1EW211HA408zFy4UAkzB+H0So85NPjBPz9XBNtzt7aIJXU6nJu84FCO936ILT7Vp4i69VVf3WZrOtO4MZosEc67QL0Qi6d+9eS1FRUTIADOGcPwsAuF3DXaBoKulzCK1CBzHBjvGff/453HTTTXUTCQ2GkyGwtfoKHDzCEt67d2/YuWsX3Ny3n9hMuJwqZGYegVWrV8BLL02C7KxMyM3NgbvvGQXz5s2D4cOHev2KQonHpH0fINVJrDXmGXB3q+c2YgTrkMFDxWgKOgfxubADVE3HuiakT7mrLsZ8jZ/ie5/5GxhFJR8XcNN/TUjvSZK0NiEh4UR6ejoG0tVZwNaFY52VLCNGjJDmzZvH+vTpI23atCnO7XZ3/++EBlBK0VAaQghBi2Y0tpLxeVI+efJkPn78eIoZLn4/8FVHHyxzApo7UM/DnoQtktoIwI17+nmhaGdlHQUsIvLdfxYK8CmqG35avhzGPvVXoXx7lWQRE6DtLrWFqY3Wdd7jnEIKQ3fCFla4+AcOH4EBAwdA9vETGmzMZuEqMg7DB4nFfIXI8JpJNLVeB4vvPdCpWMw5xwFdnHO0VW1gjC232Wz7y8rKDLMCw6ACh6N+ITaNApY+y1NYKz5Az549kaNFezye5oyxlpTSJM55LOcc3TxJHTt2vOrXX3+NDQ0NrZOO1hjwoRgUlWHADeWlpTD9lenw+htYtgBdIxwcU6cLI+ywYUNh48ZNsGvHVmHp7969i8gRHDi4H9isVqF4I5gkFN/YpVwHl7+BRSQTbN26FSa8NAXWrFkDnGFzTgKqbgBFLorH2YDFOXERQk4wxtDCnsM5z9IBlcs5PypJUobZbD753HPPFRng0SNRfN054h4+n9e6JE0BrFpvgif06NHDlJOTY8rOzg4OCwtL/uijj36+9957EWi+rzw2CBGZ30bUklHxABO6kEG4dL2wdvt79bR8b7Dk++XQp08fsFlNQsHcs+8gbNy4UewA0Ub2ww/LBOey2mRITU2FILNJAMrEFORaTNYK4WIxDk0UaT2QqpVy/WboQhIcTURQoDJvlDzCmDDcMBAgmLBEmbCrYcg26nxubhW7SVlVhQjO2LWD//Wvj5OMzTvBo2huDbwFBhqjVLYAEBMBd6gFSqgEx39xmzZzTjI44XmUUgRULqW0RJblMkmS3DabzZWYmOj+/fffdRdAnZav3iedN2DVnNm111776apVqx62WCzam2E4j/UZOTU7pAjKRuGjmycRWOJoKLDWbdgMb771prDg4wKijQ1/ok0KQ3vQiYZVZBzTJuEGgxuVEcxcRe2Ry1jaRmMTBA2rRmF+b5CED7AMdwkCh+liSyZaRwqiF6/F7plCSTeJnADwQBDxeBQgbhf75ptv+MzpjiJJknMSLHJFSGhIWWiQqSrEHloRExJSEWQLcrVJjqqKiAirMqklh5o1t877dE+YCznPGUSfYSitN0gacsH5AJbvPbzMIyIiIuntt9/endK+sw0rzqKzBXNRI6rcIpukpWu/8DOKtqXaZkCLFNR0B6MnjfeZDRfFmYiAC2scBdgUIPMIXHEFqoOIWtxdYQ13SevmpShC9PzvZ3NFYN41fQcStHuFtmkHUmgogC1UsCqVWEUMkCGSDEs3AhOBgi3z8Oehw1mikea2HbsgJMQOPXv2Ej2nY+Kbi57UCcGaY13iTuBVVVCyf72nsLCgOGPZwkwA+K13YlSxSNLlblXr2qplN1HqxKBRQj0VmF6dl8M7zI93zK6RWOG/ZInawHY+gOU7B6/ZwXijdGvDaRrwtg8ndJI4/EklLAy41tlIi2vRK9hx0ynXnDMjQA8lMZJfRflI/TN9lcTforGuLIuvRC2qsjIpt8wTZpKkGCWiWYzdbm/mCYlMTkxICGrfpSe32YIImAyepteArwGsu0Y9AMuXLwePPm1MVQsPj4D4FvEQFxcLrSLCsZCImhIXecJisx4jeXuPMqbmXWOXCogk8URnIeB8GbgwvF+lgocSSqUqDDVm1FNVCET5ESb9esDHUayb+L07C2T4tZcXrA0t9fj+ggHrbHNE/Up8N28e3Zu5uVuHqp1DFKxvDipFAyTlWKUPtz01+vg1bPN1dlLpWrEoH8tV4lQlqqoeckKlltLS0ohsN4llihrJw2OT7CEhUZGJrczoxI5IaAlSRASANVTM8/O/fwwvv/wOUI/2XHZNrPMYKxREhEF2z6SQoykpqdndmtvLTRZLZbhdE6pa1pFPCo3eOIBxC1GYCmYiK4qisozI1G86jX1rL4LKCBiqx/r77dTzDaxaH8QgjkM3xEydPqAVMHYPo7gehJ43YOkzZSKQjoEbOQUhvNwcJFL9i2mQUK4rLCHE5XJJx06W2vLz8635Fe4gl9NlqwKTBR31HhTwhHuCKTjDw0KdSaHW8tjY2LLmVrPCiUqisFoMJzxSKRYeDsoqtZYmXBKdvryc2ACW0TBAAdVqCf4EJnyNGc2GEQRZem32j1rXoClOuOiAVfOhEGg73nysU1fPwX6gsmCF2IVYNMRaUxDhXGOIhEhOuJF7IdZXKH1il8dBQr1HAUaEaOYMZKEbUl1EMkXjrFgkTfF4uIx6Em5L0JstgvYogMJIpcy4LEtUViXORBiGJuoVcX61udNcWcFAMhcdim297F9PfHy4RsWYc7pZ/E0r3/EvbmD91zAHDgfDJuU98ve1BZD/pJDgZtXA0s1gRj8/f1CuRgdYWRWZVpghqycyqgJIjEjY+UJEE2HdLmHgRGVN69aoFWEijFPMr+W4AzR4kXBLc6dF+G04Aks8hmjISbgGLBxNE41mpzMfgqwrYWKPA4Q4cIcgOKk/Hr0xY17cwPJ5Mj5vnrS7aEVCq5wjA80mU6yo7M1UlUKQCJliUhVBMwBloiQjN1KvDQu20QC1IcQyGhUIzmGwroYM5HONAStjXrKC5b2JNm8K3C1pEXzUg+KQUyvWOfEoGVmWDj8kv/hO7rRp2rbH4acu9I18vDo5oRt7j0Zfj/oWsvwRANK8OaNN7qy8wRSUDpTKJuAWUTmYSS58e6nYNTUxsBr9AGcY4IzA8qlF4JYIw0oX4FZUKpmombDtoNiWEsf8RnYf98fTnD7mJcOxfKe+ZdasoG75q64Crl4p86JQVHYVKZgxBJZXxPiKmvNDzIbcpdpMouly2M0Lg1LNvASTTLlCpTLg6uas6+//rVWfh4xqdQ251Xm95pIEFlKIOxzUpexuZzEVXoP+R7cUbBZ2HqEU43GRA0vXC2tuQrQ2cYRaocQDQPcD8I0Q3TZbqw5z6RyXLLAMEq+fOc5mNVV26lJ+uDdT1XAzl3HHraL9CU3qQsdC8/rFcOhlMtGMYDQQN9payrxSV/ItaEkv3xXWaXXnmKAdZKTjkhB9Ncl7yQPLsHvx1wZEu7nS38xJInDVxKjVhMq9przj9v5cTp8LgDoEGTqoqMRwP0A9ZYzKslut4ukSpSunOX4s9mfxWX8/8SUPLI1AnIwYQei8eZznvP1QCuVS9yDnsVYmWbaYGfV23L3g4ELxpx1UmCEwBEJ2o9miqhBi06lk2v1RVdJB3Orhf+DHMkMBYNWDAg7Hf3ePDmD5H/3VHlqelaCqznZm1ZQKQLQCDsKqdDrnEgZNvS9greYEQ5yJ0eoSk+zzAIa9jWO+gCgmWqGSyn0Ws7Qvp7LV8XjH7Erxmoj4QR2DF6GNqi5LcplwrLM/6h/vOMI5Ket6RcmeXoxDKFUIJtoxsDIZFJUxKjgadrTXjZY+YtMLBD2arjaKImhFWC+W0cPcdk4pGlg5JU4TeqcJt5bbMVLv5KbYhE0e2bz/xr/OKKpt2Evx+8seWF4dbN4ICfaXtQVV7gIKjwOLG3eRwQzD5IQTklIsoaFltYrYHI27YVC574G56saB3+HfhogzuKH2t+ZMVjHchXkqJVMJ4fykrSJsG5kxP/1SBEt95nzZA0sQIy1NhtWr9ZpknGS+Oy2s3KzEEcUd27psV6RZMkW4FSVMIpKNcZC94rJaJxKOYU2YYjF3XUPypbSI6kMWxT2ckypOSRFlrHRvyBWFksxyj6nNjw986qnS+izOpXzuZQ4s5DhaxoOXc+lhOXpDXQKO0Rawu+zF5UX2IEmyEULDGCFRBCCEEWIjjGEknpkAMeNoKEj1Ud3AuYcTqKQcyjghpRInFUyGMrMilQLl5WBPLIenPsD649zwHlzKYKnP3C9zYNWHFIFzm5ICAWA1JTUDY3kpEABWAAx+oUAAWH4ha2DQALACGPALBQLA8gtZA4MGgBXAgF8oEACWX8gaGDQArAAG/EKBALD8QtbAoAFgBTDgFwoEgOUXsgYGDQArgAG/UCAALL+QNTBoAFgBDPiFAgFg+YWsgUEDwApgwC8UCADLL2QNDPr/AbU6OR2Ul03fAAAAAElFTkSuQmCC"
                          />
                        </defs>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="flex-col justify-start items-center gap-2 flex">
                  <div
                    class="text-center text-[#3d3d3d] text-xl font-bold font-['Roboto'] leading-7 tracking-tight"
                  >
                    TopDev is going to contact you soon
                  </div>
                  <div
                    class="text-center text-[#5d5d5d] text-base font-normal font-['Roboto'] leading-snug tracking-tight"
                  >
                    Our staff will call you in minutes and bring you valuable
                    deal
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--End body modal-->
          <hr class="topdev-background-2" />
          <!--Footer modal-->
          <div class="flex-wrap">
            <div class="text-center">
              <button
                type="submit"
                target="_blank"
                class="btn btn-sm btn-primary ms-2 text-uppercase"
                @click="hideModal"
              >
                OK
              </button>
            </div>
          </div>
          <!--End footer modal-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Modal } from "bootstrap";
import { useRouter } from "vue-router";

const router = useRouter();

const hideModal = () => {
  router.push({ name: "jobs" });

  Modal.getOrCreateInstance("#modal-pending-packages-usage-topup").hide();
};
</script>
