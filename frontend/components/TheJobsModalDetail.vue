<template>
  <div class="modal fade" tabindex="-1" id="jobDetailModal">
    <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
      <div class="modal-content" v-if="!!job">
        <div class="modal-header">
          <div>
            <div class="d-flex align-items-center gap-3">
              <h4 class="modal-title" id="jobDetailModal">{{ job.title }}</h4>
              <span
                class="btn-topdev-filter"
                :class="{
                  open: job.status === 'Open',
                  review: job.status === 'Review',
                  closed: job.status === 'Closed',
                }"
              >
                {{ job.status }}
              </span>
            </div>
            <span class="fs-8">
              {{
                `${translate("job_list_created_at")} ${
                  job.created_at
                } ${translate("job_list_by")} ${job.created_by}`
              }}</span
            >
          </div>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <div class="job-info-block">
            <b>{{ translate("job_list_location") }}</b>
            <p v-for="(address, index) in job.addresses" :key="index">
              {{ address.full_address }}
            </p>
            <p>
              <b>{{ `${translate("job_list_salary")} ` }}</b>
              {{ formatSalary(job.salary) }}
            </p>
            <p>
              <b>{{ `${translate("job_list_level")} ` }}</b> {{ levels }}
            </p>
            <p>
              <b>{{ `${translate("job_list_year_of_experience")} ` }}</b>
              {{ `${translate("job_form_from")} ${job.experiences[0]}` }}
            </p>
            <p>
              <b>{{ `${translate("job_list_type")} ` }}</b> {{ types }}
            </p>
            <p>
              <b>{{ `${translate("job_form_contract_type")}: ` }}</b>
              {{ contractTypes }}
            </p>
            <p>
              <b>{{ `${translate("job_list_skills")}: ` }}</b> {{ skills }}
            </p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_description") }}</b>
            <p v-html="job.content"></p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_role") }}</b>
            <p v-html="job.responsibilities"></p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_skill") }}</b>
            <p v-html="job.requirements"></p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_benefits") }}</b>
            <p
              class="d-flex align-items-center gap-3"
            >
              <span v-html="job.benefits"></span>
            </p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_recruitment") }}</b>
            <p
              v-for="(recruitment, index) in job.recruiment_process"
              :key="index"
            >
              <span>
                {{
                  `${translate("job_list_round")} ${Number(index) + 1} :`
                }}</span
              >
              <span> {{ recruitment.name }} </span>
            </p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_email") }}</b>
            <p>{{ email }}</p>
          </div>
          <div class="job-info-block">
            <b>{{ translate("job_list_note") }}</b>
            <p>{{ job.note }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { JobForm } from "@/models/jobs";
import { formatSalary, translate } from "@/helpers";

interface Props {
  job: JobForm;
}
const props = defineProps<Props>();
const email = computed(() => props.job.emails_cc.join(", "));
const types = computed(() => props.job.types.join(", "));
const levels = computed(() => props.job.levels.join(", "));
const skills = computed(() => props.job.skills.join(", "));
const contractTypes = computed(() => props.job.contract_type.join(", "));
</script>
