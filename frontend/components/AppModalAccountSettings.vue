<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-account-settings"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <div>
            <h2 class="modal-title">
              {{ translate("contact_information_settings") }}
            </h2>
            <span class="modal-sub-title">{{
              translate("contact_information_manage_your_account_information")
            }}</span>
          </div>
        </div>
        <div class="modal-body">
          <ul class="nav nav-tabs nav-line-tabs">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link active"
                id="home-tab"
                data-bs-toggle="tab"
                data-bs-target="#home-tab-pane"
                type="button"
                @click="isChangePasswordTab = false"
              >
                {{ translate("contact_information_account") }}
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="profile-tab"
                data-bs-toggle="tab"
                data-bs-target="#profile-tab-pane"
                type="button"
                @click="isChangePasswordTab = true"
              >
                {{ translate("contact_information_change_password") }}
              </button>
            </li>
          </ul>

          <div class="tab-content">
            <!-- Start Account Setting  -->
            <div
              class="tab-pane fade show active"
              id="home-tab-pane"
              role="tabpanel"
              aria-labelledby="home-tab"
              tabindex="0"
            >
              <!-- Display Name  -->
              <div class="form-group">
                <label for="txt-display-name" class="form-label">{{
                  translate("contact_information_display_name")
                }}</label>
                <input
                  type="text"
                  id="txt-display-name"
                  class="form-control"
                  :class="{
                    'is-invalid': nameError,
                  }"
                  v-model="name"
                />
                <span class="invalid-feedback" v-if="nameError">{{
                  translate(nameError)
                }}</span>
              </div>

              <!-- Position  -->
              <div class="form-group">
                <label for="txt-position" class="form-label">{{
                  translate("contact_information_position")
                }}</label>
                <input
                  type="text"
                  id="txt-position"
                  class="form-control"
                  :class="{
                    'is-invalid': positionError,
                  }"
                  v-model="position"
                />
                <span class="invalid-feedback" v-if="positionError">{{
                  translate(positionError)
                }}</span>
              </div>

              <!-- Email -->
              <div class="form-group">
                <label
                  for="txt-email"
                  class="form-label d-flex align-items-center gap-1"
                  >Email
                  <button
                    type="button"
                    class="px-0 px-0 border-0 bg-white"
                    id="btn-helper-email"
                    :title="translate('contact_information_contact_message')"
                  >
                    <inline-svg
                      src="/assets/icons/question.svg"
                      width="15px"
                      height="15px"
                    />
                  </button>
                </label>
                <input
                  type="text"
                  id="txt-email"
                  class="form-control"
                  disabled
                  :value="authStore.user.email"
                />
              </div>

              <!-- Phone  -->
              <div class="form-group">
                <label for="txt-phone" class="form-label">{{
                  translate("contact_information_phone")
                }}</label>
                <input
                  type="text"
                  id="txt-phone"
                  class="form-control"
                  :class="{
                    'is-invalid': phoneError,
                  }"
                  v-model="phone"
                />
                <span class="invalid-feedback" v-if="phoneError">{{
                  translate(phoneError)
                }}</span>
              </div>
            </div>
            <!-- End Account Setting  -->

            <!-- Start Change Password -->
            <div
              class="tab-pane fade"
              id="profile-tab-pane"
              role="tabpanel"
              aria-labelledby="profile-tab"
              tabindex="0"
            >
              <!-- Current password  -->
              <div class="mb-5">
                <label class="form-label required">{{
                  translate("contact_information_current_password")
                }}</label>
                <div
                  class="input-with-icon mb-2"
                  :class="{
                    'is-invalid': currentPasswordError,
                  }"
                >
                  <input
                    :type="isShowCurrentPassword ? 'text' : 'password'"
                    class="form-control ps-3 pe-75"
                    :placeholder="
                      translate('contact_information_current_password')
                    "
                    v-model="currentPassword"
                  />
                  <span class="right-icon">
                    <button
                      type="button"
                      class="btn text-topdev-2 btn-link"
                      :class="{ 'text-primary': isShowCurrentPassword }"
                      @click="isShowCurrentPassword = !isShowCurrentPassword"
                    >
                      {{
                        isShowCurrentPassword
                          ? translate("contact_information_hide")
                          : translate("contact_information_show")
                      }}
                    </button>
                  </span>
                </div>
                <span class="invalid-feedback" v-if="currentPasswordError">{{
                  translate(currentPasswordError)
                }}</span>
              </div>
              <!-- End current password  -->

              <!-- New password  -->
              <div class="mb-5">
                <label class="form-label required">{{
                  translate("contact_information_new_password")
                }}</label>
                <div
                  class="input-with-icon mb-2"
                  :class="{ 'is-invalid': newPasswordError }"
                >
                  <input
                    :type="isShowNewPassword ? 'text' : 'password'"
                    class="form-control ps-3 pe-75"
                    :placeholder="translate('contact_information_new_password')"
                    v-model="newPassword"
                  />
                  <span class="right-icon">
                    <button
                      type="button"
                      class="btn text-topdev-2 btn-link"
                      :class="{ 'text-primary': isShowNewPassword }"
                      @click="isShowNewPassword = !isShowNewPassword"
                    >
                      {{
                        isShowNewPassword
                          ? translate("contact_information_hide")
                          : translate("contact_information_show")
                      }}
                    </button>
                  </span>
                </div>
                <span class="invalid-feedback" v-if="newPasswordError">{{
                  translate(newPasswordError)
                }}</span>
              </div>
              <!-- End new password  -->

              <!-- Confirm new password  -->
              <div class="mb-5">
                <label class="form-label required">{{
                  translate("contact_information_confirm_new_password")
                }}</label>
                <div
                  class="input-with-icon mb-2"
                  :class="{ 'is-invalid': confirmPasswordError }"
                >
                  <input
                    :type="isShowConfirmPassword ? 'text' : 'password'"
                    class="form-control ps-3 pe-75"
                    :placeholder="
                      translate('contact_information_confirm_new_password')
                    "
                    v-model="confirmPassword"
                  />
                  <span class="right-icon">
                    <button
                      type="button"
                      class="btn text-topdev-2 btn-link"
                      :class="{ 'text-primary': isShowConfirmPassword }"
                      @click="isShowConfirmPassword = !isShowConfirmPassword"
                    >
                      {{
                        isShowConfirmPassword
                          ? translate("contact_information_hide")
                          : translate("contact_information_show")
                      }}
                    </button>
                  </span>
                </div>
                <span class="invalid-feedback" v-if="confirmPasswordError">{{
                  translate(confirmPasswordError)
                }}</span>
              </div>
              <!-- End confirm new password  -->
            </div>
            <!-- End change Password  -->
          </div>
        </div>

        <!-- Dialog Action  -->
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            data-bs-dismiss="modal"
            @click="onCloseDialog"
          >
            {{ translate("contact_information_cancel") }}
          </button>
          <div>
            <!-- Change Password Submit Button  -->
            <button
              v-if="isChangePasswordTab"
              type="button"
              class="btn btn-sm btn-primary"
              @click="handleChangePassword"
              :disabled="isDisableChangePasswordSubmitBtn"
            >
              {{ translate("contact_information_done") }}
            </button>

            <!-- Account Submit Button  -->
            <button
              v-else
              type="button"
              class="btn btn-sm btn-primary"
              @click="handleChangeAccount"
              :disabled="isDisableAccountSubmitBtn"
            >
              {{ translate("contact_information_done") }}
            </button>
          </div>
        </div>
        <!-- End dialog action  -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useField, useForm } from "vee-validate";
import { Modal, Popover } from "bootstrap";

import { useAuthStore, useLayoutStore } from "@/stores";
import { updateEmployerAccount } from "@/api/employer";
import { changePassword } from "@/api/auth";

import {
  accountFormSchema,
  changePasswordFormSchema,
  initialChangePasswordFormValues,
} from "@/schemas/auth-form";
import { showSuccesToast, showWarningToast, translate } from "@/helpers";
import { Employer } from "@/models/employer";

/**
 * Store init
 */
const authStore = useAuthStore();
const layoutStore = useLayoutStore();

//Define data
const isChangePasswordTab = ref(false);
const isShowCurrentPassword = ref(false);
const isShowNewPassword = ref(false);
const isShowConfirmPassword = ref(false);

/**
 * Init Account Form
 */
const {
  meta: accountFormMeta,
  handleSubmit: handleAccountFormSubmit,
  resetForm: resetAccountForm,
} = useForm({
  validationSchema: accountFormSchema,
  initialValues: {
    full_name: authStore.user.full_name,
    position: authStore.user.position,
    phone: authStore.user.phone,
  },
});

// No need to define rules for fields
const {
  value: name,
  errorMessage: nameError,
  setErrors: setNameError,
} = useField("full_name");
const {
  value: position,
  errorMessage: positionError,
  setErrors: setPositionErrors,
} = useField("position");
const {
  value: phone,
  errorMessage: phoneError,
  setErrors: setPhoneErrors,
} = useField("phone");

/**
 * Init Change Password Form
 */
const {
  meta: changePasswordFormMeta,
  handleSubmit: handleChangePasswordFormSubmit,
  resetForm: resetChangePasswordForm,
} = useForm({
  initialValues: initialChangePasswordFormValues,
  validationSchema: changePasswordFormSchema,
});

const {
  value: currentPassword,
  errorMessage: currentPasswordError,
  setErrors: setCurrentPasswordErrors,
} = useField("current_password");
const {
  value: newPassword,
  errorMessage: newPasswordError,
  setErrors: setNewPasswordErrors,
} = useField("new_password");
const { value: confirmPassword, errorMessage: confirmPasswordError } =
  useField("confirm_password");

//Computed
const isDisableAccountSubmitBtn = computed(
  () => !accountFormMeta.value.dirty || !accountFormMeta.value.valid
);
const isDisableChangePasswordSubmitBtn = computed(
  () =>
    !changePasswordFormMeta.value.dirty || !changePasswordFormMeta.value.valid
);

/**
 * Handle form submit
 */
const handleChangeAccount = handleAccountFormSubmit((values) => {
  // Block UI
  layoutStore.blockPage();

  //set value in auth store
  authStore.user.phone = values.phone;
  authStore.user.position = values.position;
  authStore.user.full_name = values.full_name;

  updateEmployerAccount(authStore.user.id as number, values as Employer)
    .then(() => {
      // Manually close the modal hehe
      Modal.getInstance("#modal-account-settings")?.hide();

      // Unblock page
      layoutStore.unBlockPage();

      // Show 1 cái thông báo nhỏ xinh nào
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_save_successfully")
      );
    })
    .catch((error) => {
      // Unblock page
      layoutStore.unBlockPage();

      // Show 1 cái thông báo nhẹ đi đã
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );

      // Hiển thị errors
      const { name, phone, position } = error.response.data.errors;
      if (name) setNameError(error.response.data.errors.name);
      if (phone) setPhoneErrors(phone);
      if (position) setPositionErrors(position);
    });
});

const handleChangePassword = handleChangePasswordFormSubmit(async (values) => {
  layoutStore.blockPage();
  try {
    await changePassword({
      old_password: values.current_password,
      new_password: values.new_password,
    });

    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );
    Modal.getInstance("#modal-account-settings")?.hide();
    resetChangePasswordForm();
  } catch (err) {
    const errors = err.response.data.errors;

    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );

    if (!!errors["old_password"])
      setCurrentPasswordErrors(errors["old_password"].join(", "));
    if (!!errors["new_password"])
      setNewPasswordErrors(errors["new_password"].join(", "));
  } finally {
    layoutStore.unBlockPage();
  }
});

const onCloseDialog = () => {
  resetAccountForm();
  resetChangePasswordForm();
};

onMounted(() => {
  Popover.getOrCreateInstance(document.getElementById("btn-helper-email"), {
    trigger: "click hover",
    html: true,
  });
});
</script>
