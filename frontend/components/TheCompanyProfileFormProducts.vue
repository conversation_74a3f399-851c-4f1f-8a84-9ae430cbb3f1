<template>
  <section id="company-products-container" class="w-100">
    <!--begin::Section header-->
    <div class="d-flex justify-content-between">
      <div class="section-header">
        <h2 class="mb-0">{{ translate("about_company_company_product") }}</h2>
        <span class="heading">{{
          translate("about_company_add_company_product")
        }}</span>
      </div>

      <button
        class="btn svg-icon svg-icon-2 text-primary fw-bolder d-flex align-items-center px-0 py-0"
        type="button"
        data-bs-toggle="modal"
        data-bs-target="#modal-products"
        @click="openAddProductDialog"
      >
        <inline-svg src="/assets/icons/plus.svg" class="me-1" />
        {{ translate("about_company_add") }}
      </button>
    </div>
    <!--end::Section header-->

    <!--begin::Section body-->
    <div class="section-body w-100">
      <ul
        class="list-unstyled d-flex flex-row flex-wrap gap-5 justify-content-between company-product-list"
      >
        <li
          v-for="(product, index) in productFields"
          :key="index"
          class="d-flex justify-content-between gap-2 mb-5"
          :class="[productFields.length > 1 ? 'w-48' : 'w-100']"
        >
          <div class="company-product w-100">
            <div class="d-flex justify-content-between flex-grow-1">
              <div class="mw-100 flex-grow-1">
                <h4 class="text-break fs-6">{{ product.value.name }}</h4>
                <p
                  class="text-break fs-7"
                  v-html="product.value.description"
                ></p>
              </div>
              <div
                class="text-end mw-25"
                v-if="product.value.image && product.value.image.url"
              >
                <img
                  :src="product.value.image.url"
                  :alt="product.value.name"
                  class="w-100"
                />
              </div>
            </div>
            <div class="d-flex link-container" v-if="product.value.link">
              <span class="svg svg-icon-2">
                <inline-svg src="/assets/icons/link.svg" />
              </span>
              <p class="text-break">
                {{ product.value.link }}
              </p>
            </div>
          </div>
          <div class="d-flex justify-content-end align-items-start">
            <button
              type="button"
              class="btn hover-svg-primary px-0 py-0"
              @click="openEditProductDialog(product.value, index)"
              data-bs-toggle="modal"
              data-bs-target="#modal-products"
            >
              <inline-svg src="/assets/icons/pen.svg" />
            </button>
            <button
              type="button"
              class="btn hover-svg-primary ms-2 px-0 py-0"
              @click="onDeleteProduct(index)"
            >
              <inline-svg src="/assets/icons/trash.svg" />
            </button>
          </div>
        </li>
      </ul>
    </div>
    <!--end::Section body-->

    <!--begin::Modal -->
    <TheCompanyProfileFormModalProducts
      :product="edittingProduct"
      @update:product="(value) => updateProduct(value)"
    />
    <!--end::Modal -->
  </section>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useFieldArray } from "vee-validate";
import { Modal } from "bootstrap";

import TheCompanyProfileFormModalProducts from "@/components/TheCompanyProfileFormModalProducts.vue";

import { translate } from "@/helpers";
import { Products } from "@/models/employer";
/**
 * Define data
 */
const {
  fields: productFields,
  push: addProduct,
  remove: removeProduct,
  update: editProduct,
} = useFieldArray<Products>("products");

const edittingProductIndex = ref(-1);
const edittingProduct = ref<Products>();
const isAddProduct = ref(false);

/**
 * Define Function
 */
const onDeleteProduct = (index: number) => {
  removeProduct(index);
};
const openAddProductDialog = () => {
  isAddProduct.value = true;
  edittingProduct.value = {
    id: null,
    name: "",
    link: "",
    description: "",
    image: {
      id: null,
      url: "",
    },
  };
  isAddProduct.value = true;

  Modal.getOrCreateInstance("#modal-products").show();
};
const openEditProductDialog = (selectedProduct: any, index: number) => {
  isAddProduct.value = false;
  edittingProduct.value = { ...selectedProduct };
  edittingProductIndex.value = index;

  Modal.getOrCreateInstance("#modal-products").show();
};

const updateProduct = (value: any) => {
  if (isAddProduct.value) {
    addProduct({ ...value });
  } else {
    editProduct(edittingProductIndex.value, { ...value });
  }
};
</script>
