<template>
  <div class="modal fade" tabindex="-1" id="modal-addresses">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header border-bottom mb-3">
          <div>
            <h2 class="modal-title mb-0 required">
              {{ translate("about_company_addresses") }}
            </h2>
            <span class="heading">{{
              translate("about_company_add_addresses")
            }}</span>
          </div>
        </div>

        <div class="modal-body d-flex flex-column gap-4">
          <div>
            <label for="">{{ translate("about_company_city_province") }}</label>
            <Multiselect
              :placeholder="translate('about_company_city_province')"
              v-model="province_id"
              :options="provinces"
              :searchable="true"
              mode="single"
              name="province_id"
              @change="
                (provinceId) => {
                  district_id = null;
                  ward_id = null;
                  getDistrictData(provinceId);
                }
              "
            />
            <p
              v-if="provinceError && provinceMeta.touched"
              class="text-danger ms-1 mt-1"
            >
              {{ translate(provinceError) }}
            </p>
          </div>

          <div>
            <label for="">{{ translate("about_company_district") }}</label>
            <Multiselect
              :placeholder="translate('about_company_district')"
              v-model="district_id"
              :options="districts"
              :searchable="true"
              mode="single"
              name="district_id"
              @change="
                (districtId) => {
                  ward_id = null;
                  getWardData(districtId);
                }
              "
            />
            <p
              v-if="districtError && districtMeta.touched"
              class="text-danger ms-1 mt-1"
            >
              {{ translate(districtError) }}
            </p>
          </div>

          <div>
            <label for="">{{ translate("about_company_ward") }}</label>
            <Multiselect
              :placeholder="translate('about_company_ward')"
              v-model="ward_id"
              :options="wards"
              mode="single"
              name="ward_id"
            />
            <p
              v-if="wardError && wardMeta.touched"
              class="text-danger ms-1 mt-1"
            >
              {{ translate(wardError) }}
            </p>
          </div>

          <div>
            <label for="">{{ translate("about_company_street") }}</label>
            <input
              :placeholder="translate('about_company_street')"
              type="text"
              class="form-control"
              name="street"
              v-model="street"
            />
            <p
              v-if="streetError && streetMeta.touched"
              class="text-danger ms-1 mt-1"
            >
              {{ translate(streetError) }}
            </p>
          </div>
        </div>

        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            data-bs-dismiss="modal"
          >
            {{ translate("about_company_cancel") }}
          </button>
          <button
            type="button"
            id="done-btn"
            class="btn btn-sm btn-primary"
            @click="onHandleAddressForm"
            :disabled="!formMeta?.valid"
          >
            {{ translate("about_company_done") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import Multiselect from "@vueform/multiselect";
import { useField, useForm } from "vee-validate";
import { Modal } from "bootstrap";

import { fetchDistrict, fetchProvince, fetchWard } from "@/api/ams";
import { formAddressesSchema } from "@/schemas/company-profile-form";
import { useLayoutStore } from "@/stores";
import { translate } from "@/helpers";

/**
 * Define props
 */
interface Props {
  address: any;
}
const props = defineProps<Props>();

/**
 * Define emit
 */
interface Emits {
  (e: "update:address", value: any): void;
}
const emit = defineEmits<Emits>();

/**
 * Define data
 */
const provinces = ref([]);
const districts = ref([]);
const wards = ref([]);

const address_id = ref();
const full_address = ref("");
const isAddAddress = ref(true);

const { meta: formMeta, setValues: setFormValues } = useForm({
  validationSchema: formAddressesSchema,
});

const {
  value: province_id,
  meta: provinceMeta,
  errorMessage: provinceError,
} = useField("province_id");
const {
  value: district_id,
  meta: districtMeta,
  errorMessage: districtError,
} = useField("district_id");
const {
  value: ward_id,
  meta: wardMeta,
  errorMessage: wardError,
} = useField("ward_id");
const {
  value: street,
  meta: streetMeta,
  errorMessage: streetError,
} = useField("street");

/**
 * Define store
 */
const layoutStore = useLayoutStore();

/**
 * Define function
 */
const getProvinceData = () => {
  return fetchProvince().then((data: any) => {
    provinces.value = data.map((province: any) => ({
      value: province.id,
      label: province.text,
    }));
  });
};

const getDistrictData = (provinceId: any) => {
  return fetchDistrict(provinceId).then((data: any) => {
    districts.value = data.map((district: any) => ({
      value: district.id,
      label: district.text,
    }));
  });
};

const getWardData = (districtId: any) => {
  return fetchWard(districtId).then((data: any) => {
    wards.value = data.map((ward: any) => ({
      value: ward.id,
      label: ward.text,
    }));
  });
};

const onHandleAddressForm = () => {
  const addressData = {
    id: null,
    province_id: province_id.value,
    district_id: district_id.value,
    ward_id: ward_id.value,
    street: street.value,
    full_address: "",
  };

  if (props.address.id) {
    addressData.id = props.address.id;
  }

  // Build full address
  const ward: any = wards.value.find(
    (item: any) => item.value == ward_id.value
  );
  const district: any = districts.value.find(
    (item: any) => item.value == district_id.value
  );
  const province: any = provinces.value.find(
    (item: any) => item.value == province_id.value
  );

  //Set full address
  addressData.full_address = `${street.value ? `${street.value}, ` : ""} ${
    ward ? `${ward.label}, ` : ""
  } ${district ? `${district.label}, ` : ""} ${province.label}`;

  // Finally emit data
  emit("update:address", addressData);

  // Good, close modal
  Modal.getInstance("#modal-addresses")?.hide();
};

/**
 * Life cycle
 */
onMounted(() => {
  getProvinceData();
});

/**
 * Watch data
 */
watch(
  () => props.address,
  async (address: any) => {
    layoutStore.blockPage();

    // Reset form first
    setFormValues({ ...address });

    // Load district & ward
    if (address.province_id) {
      await getDistrictData(address.province_id);
    }

    if (address.district_id) {
      await getWardData(address.district_id);
    }

    layoutStore.unBlockPage();
  },
  { deep: true }
);
</script>
