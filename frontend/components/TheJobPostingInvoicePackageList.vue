<template>
  <div class="flex-1 bg-white rounded-3 job-packages">
    <table class="table table-sm package-table m-0">
      <thead>
        <tr class="p-2">
          <th style="width: 12%">
            {{ translate("usage_history_job_posting_table_invoice_header") }}
          </th>
          <th style="width: 12%">
            {{ translate("usage_history_job_posting_table_paid_package_number_header") }}
          </th>
          <th style="width: 10%">
            {{ translate("usage_history_job_posting_table_paid_at_header") }}
          </th>
          <th style="width: 20%">
            {{ translate("usage_history_job_posting_table_paid_package_header") }}
          </th>
          <th style="width: 12%">
            {{ translate("usage_history_job_posting_table_paid_each_package_header") }}
          </th>
          <th style="width: 12%">
            {{ translate("usage_history_job_posting_table_used_each_package_header") }}
          </th>
          <th style="width: 12%">
            {{ translate("usage_history_job_posting_table_remain_each_package_header") }}
          </th>
          <th style="width: 10%">
            <TableColumnHeaderSorting title="usage_history_job_posting_table_expired_at_header" sort="use_expired_at"
              @on-sort="(sortParam: SortType) => emit('onSort', sortParam)" />
          </th>
        </tr>
      </thead>
      <tbody ref="tableBodyRef" :class="{ 'table-data-empty': !invoices.data || invoices.data.length == 0 }">
        <template v-if="invoices.data && invoices.data.length" v-for="(item, idx) in invoices.data" :key="item.invoice">
          <tr v-for="(jobPackage, index) in item.search_packages" :class="(idx as number) % 2 == 0 ? 'odd' : ''"
            :key="jobPackage.id">
            <td style="width: 12%" class="align-self-center">
              {{ index == 0 ? item.invoice : '' }}
            </td>
            <td style="width: 12%" class="font-bold align-self-center">
              {{ index == 0 ? item.total : '' }}
            </td>
            <td style="width: 10%" class="align-self-center">
              {{ index == 0 ? item.paid_at : '' }}
            </td>
            <td style="width: 20%" class="font-bold align-self-center">
              {{ jobPackage.name }} {{ jobPackage.free_package ? translate('usage_history_job_posting_package_gift_title') : '' }}
            </td>
            <td style="width: 12%" class="align-self-center">
              {{ jobPackage.qty }}
            </td>
            <td style="width: 12%" class="align-self-center">
              {{ jobPackage.used }}
            </td>
            <td style="width: 12%" class="align-self-center">
              {{ jobPackage.remain }}
            </td>
            <td style="width: 10%" class="align-self-center">
              {{ jobPackage.expired_at }}
            </td>
          </tr>
        </template>
        <template v-else>
          <!-- If invoices is empty -->
          <tr id="table-body-empty">
            <td colspan="3" class="text-center">
              <inline-svg src="/assets/icons/empty-file.svg" class="empty-icon"></inline-svg>
              <p style="font-size: 16px">
                {{
                  translate("usage_history_job_posting_not_found_invoices")
                }}
              </p>
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { translate } from "@/helpers";
import TableColumnHeaderSorting from "@/components/TableColumnHeaderSorting.vue";
import { InvoiceResponse } from "@/models/usage-history";
import { SortType } from "@/models/usage-history";

const tableBodyRef = ref<HTMLDivElement>();
const isLoadingData = ref<boolean>(false);

type Invoice = {
  invoices: InvoiceResponse,
}
const props = defineProps<Invoice>();

type Emit = {
  (e: 'loadMore', value: number)
  (e: 'onSort', value: SortType)
}

const emit = defineEmits<Emit>();

onMounted(() => {
  handleLoadMorePackage();
});

/**
 * Watch data
 */
watch(
  () => props.invoices,
  () => {
    isLoadingData.value = false;
  },
  { deep: true }
);

const handleLoadMorePackage = () => {
  if (tableBodyRef.value) {
    const element = tableBodyRef.value;
    tableBodyRef.value.addEventListener("scroll", async () => {
      if (
        element.scrollHeight - element.scrollTop >= element.clientHeight &&
        props.invoices.current_page < props.invoices.last_page &&
        !isLoadingData.value
      ) {
        // is scroll to bottom && not last page && not loading
        isLoadingData.value = true;
        emit('loadMore', props.invoices.current_page + 1);
      }
    });
  }
};

</script>
