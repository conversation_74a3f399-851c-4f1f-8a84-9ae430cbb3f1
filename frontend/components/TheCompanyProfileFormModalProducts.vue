<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-products"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header border-bottom mb-3">
          <div>
            <h2 class="modal-title mb-0">
              {{ translate("about_company_company_product") }}
            </h2>
            <span class="heading">{{
              translate("about_company_add_company_product")
            }}</span>
          </div>
        </div>

        <div class="modal-body d-flex flex-column gap-4">
          <div class="row d-flex align-items-start">
            <div class="col-4">
              <label class="form-label fs-6">{{
                translate("about_company_product_photo")
              }}</label>
              <p class="heading text-nowrap">
                {{ translate("about_company_types_image") }}
              </p>
              <div
                class="d-flex align-items-center justify-content-center border-dashed rounded"
                style="height: 110px"
              >
                <AppImageUpload
                  @uploaded="changeImage"
                  id="product-photo"
                  type="product-photo"
                  :url="image ? image.url : null"
                />
              </div>
            </div>
            <div class="col">
              <div>
                <label class="form-label required">{{
                  translate("about_company_product_name")
                }}</label>
                <input
                  type="text"
                  name="name"
                  class="form-control form-control-solid"
                  :class="{ 'is-invalid': nameError && nameMeta.touched }"
                  @blur="nameBlur"
                  v-model="name"
                />
                <AppErrorMessage name="name" />
              </div>
              <div class="mt-3">
                <label class="form-label">{{
                  translate("about_company_link")
                }}</label>
                <input
                  type="text"
                  name="link"
                  class="form-control form-control-solid"
                  v-model="link"
                />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <label class="form-label required">{{
                translate("about_company_description")
              }}</label>
              <textarea
                name="description"
                class="form-control form-control-solid"
                rows="4"
                :class="{
                  'is-invalid': descriptionError && descriptionMeta.touched,
                }"
                v-model="description"
                @blur="descriptionBlur"
              >
              </textarea>
              <AppErrorMessage name="description" />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            data-bs-dismiss="modal"
          >
            {{ translate("about_company_cancel") }}
          </button>
          <button
            type="button"
            class="btn btn-sm btn-primary"
            data-bs-dismiss="modal"
            @click="handleProductAction"
            :disabled="!formMeta.valid"
          >
            {{ translate("about_company_done") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch } from "vue";
import { useField, useForm } from "vee-validate";
import { Modal } from "bootstrap";

import AppImageUpload from "@/components/AppImageUpload.vue";
import AppErrorMessage from "@/components/AppErrorMessage.vue";

import { formProductSchema } from "@/schemas/company-profile-form";
import { translate } from "@/helpers";
import { Products, Image } from "@/models/employer";

const props = defineProps<{
  product: Products;
}>();

const emit = defineEmits<{
  (e: "update:product", value: Products): void;
}>();

const { meta: formMeta, setValues } = useForm({
  validationSchema: formProductSchema,
});

const {
  value: name,
  errorMessage: nameError,
  meta: nameMeta,
  handleBlur: nameBlur,
} = useField<string>("name");
const { value: link } = useField<string>("link");
const {
  value: description,
  errorMessage: descriptionError,
  meta: descriptionMeta,
  handleBlur: descriptionBlur,
} = useField<string>("description");
const { value: image } = useField<Image>("image");

/**
 * Watch
 */
watch(
  () => props.product,
  (product) => {
    setValues({ ...product });
  },
  { deep: true }
);

/**
 * Function
 */
const changeImage = (url: string) => {
  image.value = {
    id: null,
    url: url,
  };
};

const handleProductAction = () => {
  emit("update:product", {
    id: props.product.id,
    name: name.value,
    link: link.value,
    description: description.value,
    image: image.value,
  });

  Modal.getOrCreateInstance("#modal-products").hide();
};
</script>
