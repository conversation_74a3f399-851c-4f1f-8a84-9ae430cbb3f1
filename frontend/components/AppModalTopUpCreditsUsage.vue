<template>
  <div class="modal fade" tabindex="-1" id="modal-credits-usage-topup" data-bs-backdrop="static"
    data-bs-keyboard="false">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header row text-right p-0 d-flex justify-content-end mx-1">
          <button type="button" class="btn btn-icon" @click="onCloseDialog">
            <span class="svg-icon svg-icon-1 me-n1">
              <inline-svg src="/assets/icons/close.svg" />
            </span></button>
        </div>
        <div class="modal-body p-5">
          <!--Body modal-->
          <div class="row">
            <div class="col-md-6">
              <inline-svg class="w-100" src="/assets/icons/banner-topup.svg" />
            </div>
            <div class="col-md-6">
              <h2 class="modal-title">
                {{ translate("layout_credits_usage_want_to_topup") }}
              </h2>
              <div class="number-cre my-4">
                <span v-html="`${translate(
            'layout_account_current_balance'
          )} : <span class='color-credits'>${translate(
            'layout_credits_number',
            [companyStore.total_credit.toLocaleString('en')]
          )} </span>`
            "></span>
              </div>
              <div class="my-4">
                <span v-html="translate('layout_click_buy_now_button')
            "></span>
              </div>
              <div>
                <span v-html="translate('layout_account_credits_usage_information_topup')
            "></span>
              </div>
            </div>
          </div>
          <!--End body modal-->
          <hr class="topdev-background-2" />
          <!--Footer modal-->
          <div class="d-flex justify-content-end flex-wrap">
            <div class="d-flex w-75 justify-content-between gap-4">
              <!-- Cancel Button  -->
              <button type="button" class="btn btn-sm btn-secondary flex-1" @click="onSendMeInformation"
                :disabled="loadSendInfo">
                {{ translate("layout_credits_usage_topup_send") }}
              </button>
              <!-- Buy now Button  -->
              <a href="https://topdev.vn/products" type="button" target="_blank"
                class="btn btn-sm btn-primary ms-2 flex-1 text-uppercase">
                {{ translate("credit_management_buy_now") }}
              </a>
            </div>
          </div>
          <!--End footer modal-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Modal } from "bootstrap";
import { useSearchResumesStore, useCompanyStore } from "@/stores";
import { translate, showWarningToast, showSuccesToast } from "@/helpers";
import { ref } from "vue";
import { fetchSendMeNotification } from "@/api/search-resume";
/**
 * Define init
 */
const loadSendInfo = ref(false);

const searchResumesStore = useSearchResumesStore();
const companyStore = useCompanyStore();
const closeModal = () => {
  Modal.getOrCreateInstance("#modal-credits-usage-topup").hide();
};
const onCloseDialog = () => {
  closeModal();
};
const onSendMeInformation = async () => {
  loadSendInfo.value = true;
  try {
    await fetchSendMeNotification();
    closeModal();
    showSuccesToast(
      translate("toast_submission_successful"),
      translate("toast_send_me_in_formation")
    );
  } catch (error) {
    if (error.response.data.code == "LIMIT_QUOTA") {
      showWarningToast(
        translate("toast_oops"),
        translate("toast_send_information_already_request")
      );
    } else {
      showWarningToast(
        translate("toast_something_went_wrong"),
        translate("toast_please_try_again_message")
      );
    }
  } finally {
    loadSendInfo.value = false;
  }
};
</script>
