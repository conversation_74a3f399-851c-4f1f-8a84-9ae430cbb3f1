<template>
  <div
    class="mt-3"
    :class="{
      'procedue-status-detail mt-5 pt-2 d-flex justify-content-between align-items-start border-top':
        isDetailCandidates,
    }"
  >
    <div
      class="procedure-status-box"
      @click="handleOpenDropList(candidate.id, $event)"
    >
      <span class="title-procedure-status">{{
        translate("candidate_list_select_procedure")
      }}</span>
      <span class="arrow-procedure-status"></span>
      <div class="sub-procedure-status" v-if="idChange == candidate.id">
        <div
          class="item-groups-procedure-status open border-bottom border-white"
          v-if="getDataStatus[candidate.id] != null"
        >
          <span
            class="title-groups"
            @click="handleChangeStatus(candidate.id, null)"
          >
            &nbsp;
          </span>
        </div>
        <div
          class="item-groups-procedure-status open"
          :class="[
            {
              active:
                getDataStatus[candidate.id] ===
                  statusCandidate['not_matching'] ||
                getDataStatus[candidate.id] ===
                  statusCandidate['matched'],
            },
          ]"
        >
          <span class="title-groups no-hover-title-groups">
            {{ translate("candidate_list_received_cv") }}
          </span>
          <div class="box-child-groups">
            <ul class="child-groups">
              <li
                :class="{
                  active:
                    getDataStatus[candidate.id] ===
                    statusCandidate['matched'],
                }"
                @click="
                  handleChangeStatus(
                    candidate.id,
                    statusCandidate['matched']
                  )
                "
              >
                {{ translate("candidate_list_matched") }}
              </li>
              <li
                :class="{
                  active:
                    getDataStatus[candidate.id] ===
                    statusCandidate['not_matching'],
                }"
                @click="
                  handleChangeStatus(
                    candidate.id,
                    statusCandidate['not_matching']
                  )
                "
              >
                {{ translate("candidate_list_not_matching") }}
              </li>
            </ul>
          </div>
        </div>
        <div
          class="item-groups-procedure-status open"
          :class="[
            {
              active:
                getDataStatus[candidate.id] ===
                  statusCandidate['interview_appointment'] 
            },
          ]"
        >
          <span class="title-groups" @click="handleChangeStatus(candidate.id, statusCandidate['interview_appointment'])">
            {{ translate("candidate_list_interviewed") }}
          </span>
        </div>
        <div
          class="item-groups-procedure-status open"
          :class="[
            {
              active: getDataStatus[candidate.id] === statusCandidate['offer'],
            },
          ]"
        >
          <span
            class="title-groups"
            @click="handleChangeStatus(candidate.id, statusCandidate['offer'])"
          >
            {{ translate("candidate_list_offer") }}
          </span>
        </div>
        <div
          class="item-groups-procedure-status open"
          :class="[
            {
              active: getDataStatus[candidate.id] === statusCandidate['hired'],
            },
          ]"
        >
          <span
            class="title-groups"
            @click="handleChangeStatus(candidate.id, statusCandidate['hired'])"
          >
            {{ translate("candidate_list_hired") }}
          </span>
        </div>
        <div
          class="item-groups-procedure-status open"
          :class="[
            {
              active: getDataStatus[candidate.id] === statusCandidate['failed'],
            },
          ]"
        >
          <span
            class="title-groups"
            @click="handleChangeStatus(candidate.id, statusCandidate['failed'])"
          >
            {{ translate("candidate_list_failed") }}
          </span>
        </div>
      </div>
    </div>
    <!-- Experience process -->
    <div
      v-if="getDataStatus[candidate.id]"
      :class="{
        'process-procedue-status-detail': isDetailCandidates,
      }"
    >
      <h3 v-if="isDetailCandidates">
        {{ translate("candidate_list_procedure_status") }}
      </h3>
      <ul class="process-list-container mt-2 text-lg-start">
        <li
          v-if="
            checkValueActiveProcessBar(
              dataCheckString.receivedCv,
              getDataStatus[candidate.id]
            )
          "
        >
          <p class="fw-bold">
            {{ translate("candidate_list_received_cv") }}
          </p>
          <p
            v-if="
            checkValueActiveProcessBar(
              dataCheckString.receivedCv,
              getDataStatus[candidate.id]
            ) && getDataStatus[candidate.id] !== statusCandidate['not_matching']
          "
          >
            {{ translate("candidate_list_proceed_interview") }}
          </p>
          <p
            v-if="
              getDataStatus[candidate.id] === statusCandidate['not_matching']
            "
          >
            {{ translate("candidate_list_not_matching") }}
          </p>
        </li>
        <li
          v-if="
            checkValueActiveProcessBar(
              dataCheckString.interviewed,
              getDataStatus[candidate.id]
            )
          "
        >
          <p class="fw-bold">
            {{ translate("candidate_list_interviewed") }}
          </p>
        </li>
        <li
          v-if="
            checkValueActiveProcessBar(
              dataCheckString.offer,
              getDataStatus[candidate.id]
            )
          "
        >
          <p class="fw-bold">
            {{ translate("candidate_list_offer") }}
          </p>
        </li>
        <li v-if="getDataStatus[candidate.id] === statusCandidate['hired']">
          <p class="fw-bold">
            {{ translate("candidate_list_hired") }}
          </p>
        </li>
        <li v-if="getDataStatus[candidate.id] === statusCandidate['failed']">
          <p class="fw-bold">
            {{ translate("candidate_list_failed") }}
          </p>
        </li>
      </ul>
    </div>
    <div v-else></div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, watch } from "vue";

import { updateCandidate } from "@/api/candidate";
import { statusCandidate } from "@/config/status-candidate";
import {
  showWarningToast,
  translate
} from "@/helpers";
import { Candidate } from "@/models/candidates";
import { useCandidatesStore } from "@/stores";

//Interface
interface Props {
  candidate: Candidate;
  isDetailCandidates: any;
  idChange: number;
  getDataStatus: any;
}

const props = defineProps<Props>();

//Define store
const candidatesStore = useCandidatesStore();

//Define data
const dataCheckString = reactive({
  receivedCv:
    "interview_appointment,not_matching,matched,offer,hired,failed",
  interviewed: "interview_appointment,offer,hired,failed",
  offer: "offer,hired,failed",
});

//Define emit
interface Emit {
  (e: "openDropList", candidateID: number, event: any): void;
  (e: "changeCandidateProcedureStatus", candidateID: number, status: string|null): void;
}
const emit = defineEmits<Emit>();

//Function
const handleOpenDropList = (candidateID: number, event: any) => {
  event.stopPropagation();
  emit("openDropList", candidateID, event);
};

const checkValueActiveProcessBar = (stringData: string, value: string) => {
  return stringData.includes(value);
};

const handleChangeStatus = async (candidateID: number, value: string) => {
  if (value === props.getDataStatus[candidateID]) {
    return;
  }

  try {
    await updateCandidate(candidateID, { procedure_status: value });
  } catch (err) {
    const { response } = err;
    if (!response.data.errors) return;
    showWarningToast(
      translate("toast_sorry"),
      translate(response.data.errors.procedure_status[0])
    );
  }
  handleUpdateDataStatus(candidateID, value);
  emit('changeCandidateProcedureStatus', candidateID, value);
};

const handleUpdateDataStatus = (candidateID: number, value: string | null) => {
  candidatesStore.setValueStatusCandidates({
    [candidateID]: value
  });
};
//Life cycle
onMounted(async () => {
  //Define option status
  handleUpdateDataStatus(props.candidate.id, props.candidate?.procedure_status);
});

watch(
  () => props.candidate,
  (candidate) => {
    handleUpdateDataStatus(candidate.id, candidate?.procedure_status);
  },
  { deep: true }
);
</script>
