<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-free-plan-post-job"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div
          class="modal-header row text-right p-0 d-flex justify-content-end mx-1"
        >
          <button
            type="button"
            class="btn btn-icon"
            @click="backToPreviousPage"
          >
            <span class="svg-icon svg-icon-1 me-n1">
              <inline-svg src="/assets/icons/close.svg" />
            </span>
          </button>
        </div>
        <div class="modal-body p-5">
          <!--Body modal-->
          <div class="row">
            <div class="col-md-3 align-self-center">
              <inline-svg
                class="w-100"
                src="/assets/icons/banner-package-topup.svg"
              />
            </div>
            <div class="col-md-9">
              <h2 class="modal-title">{{ translate('customer_free_plan_modal_title') }}</h2>
              <div class="text-justify text-[#6d6d6d] text-lg pt-1">
                {{ translate('customer_free_plan_modal_message_1') }}
              </div>
              <div class="text-justify pt-1 pb-1">
                <span class="text-[#5d5d5d]" v-html="translate('customer_free_plan_modal_message_2')"></span>
              </div>
              <form @submit="onSubmit" id="upgrade-premium-form">
                <div class="form-group">
                  <label for="fullname" class="form-label mb-0"
                    >{{ translate('customer_free_plan_form_fullname') }}</label
                  >
                  <input
                    type="text"
                    id="fullname"
                    class="form-control form-control-solid"
                    :class="{
                      'is-invalid': fullnameMeta.touched && fullnameError,
                    }"
                    name="fullname"
                    v-model="fullnameValue"
                    @blur="handleFullnameBlur"
                  />
                  <AppErrorMessage name="fullname" />
                </div>

                <div class="form-group">
                  <label for="company_name" class="form-label mb-0"
                    >{{ translate('customer_free_plan_form_company_name') }}</label
                  >
                  <input
                    type="text"
                    id="company_name"
                    class="form-control form-control-solid"
                    :class="{
                      'is-invalid': companyNameMeta.touched && companyNameError,
                    }"
                    v-model="companyNameValue"
                    @blur="handleCompanyNameBlur"
                  />
                  <AppErrorMessage name="company_name" />
                </div>

                <div class="form-group">
                  <label for="phone_numer" class="form-label mb-0"
                    >{{ translate('customer_free_plan_form_phone_number') }}</label
                  >
                  <input
                    type="text"
                    id="phone_numer"
                    class="form-control form-control-solid"
                    :class="{
                      'is-invalid': phoneNumberMeta.touched && phoneNumberError,
                    }"
                    v-model="phoneNumberValue"
                    @blur="handlePhoneNumberBlur"
                  />
                  <AppErrorMessage name="phone_number" />
                </div>
              </form>

              <div class="text-justify" v-html="translate('customer_free_plan_form_hint')"></div>
            </div>
          </div>
          <!--End body modal-->
          <hr class="topdev-background-2" />
          <!--Footer modal-->
          <div class="d-flex justify-content-end flex-wrap">
            <div class="d-flex w-75 justify-content-end gap-4">
              <!-- Buy now Button  -->
              <button
                type="submit"
                target="_blank"
                class="btn btn-sm btn-primary ms-2 text-uppercase"
                :class="isRequesting ? 'disabled' : ''"
                @click="onSubmit"
              >
                {{ translate('customer_free_plan_form_request_button') }}
                <i v-show="isRequesting" class="fa fa-spinner fa-spin"></i>
              </button>
            </div>
          </div>
          <!--End footer modal-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  defaultUpgradePremiumFormValue,
  upgradePremiumFormObject,
} from "@/schemas/upgrade-premium-form";
import { Modal } from "bootstrap";
import { useField, useForm } from "vee-validate";
import AppErrorMessage from "@/components/AppErrorMessage.vue";
import { showSuccesToast, translate } from "@/helpers";
import { useAuthStore } from "@/stores";
import { onMounted, ref } from "vue";
import { fetchCompanyInfo, sendBuyPackageNotification } from "@/api/company";

const authStore = useAuthStore();
const isRequesting = ref(false);

onMounted(async () => {
  const user = await authStore.getUserInfo();

  await fetchCompanyInfo().then(({ data }) => {
    resetForm({
      values: {
        fullname: user.full_name ?? '',
        company_name: data.display_name ?? '',
        phone_number: user.phone ?? '',
      },
    });
  });

});

// Define form
const {
  values: formValue,
  meta,
  errors,
  resetForm,
  handleSubmit
} = useForm({
  validationSchema: upgradePremiumFormObject,
  initialValues: defaultUpgradePremiumFormValue,
  validateOnMount: false,
});

// Function
const onSubmit = handleSubmit(
  async (values) => {

    if (isRequesting.value) {
      return;
    }

    isRequesting.value = true;
    await sendBuyPackageNotification(values);
    isRequesting.value = false;
    history.back();
    Modal.getOrCreateInstance("#modal-free-plan-post-job").hide();
    showSuccesToast(
      translate("toast_congrats"),
      translate('customer_free_plan_form_requested')
    );
  }
);

// Field
const {
  value: fullnameValue,
  handleBlur: handleFullnameBlur,
  meta: fullnameMeta,
  errorMessage: fullnameError,
} = useField("fullname");

const {
  value: companyNameValue,
  handleBlur: handleCompanyNameBlur,
  meta: companyNameMeta,
  errorMessage: companyNameError,
} = useField("company_name");

const {
  value: phoneNumberValue,
  handleBlur: handlePhoneNumberBlur,
  meta: phoneNumberMeta,
  errorMessage: phoneNumberError,
} = useField("phone_number");

const backToPreviousPage = () => {
  Modal.getOrCreateInstance("#modal-free-plan-post-job").hide();
};
</script>
