<template>
  <div
    class="d-flex justify-content-start align-items-center py-5 border-top px-4"
  >
    <span class="fw-bold me-3 fs-8">{{
      translate("pagination_total", [meta?.total])
    }}</span>
    <ul class="pagination pagination-outline">
      <li
        class="page-item m-1 cursor"
        @click="changeCurrentPage({ to: currentPage - 1 })"
      >
        <a class="page-link fs-8">&lt;</a>
      </li>
      <li
        v-for="(item, index) in links"
        :key="index"
        class="page-item m-1 cursor"
        :class="{ active: item.active }"
        @click="changeCurrentPage(item)"
      >
        <a class="page-link fs-8">{{ item.label }}</a>
      </li>
      <li
        class="page-item m-1 cursor"
        @click="changeCurrentPage({ to: currentPage + 1 })"
      >
        <a class="page-link fs-8">&gt;</a>
      </li>
    </ul>

    <select
      class="form-control form-control-solid select-pagination rounded fs-8"
      v-model="pageSize"
    >
      <option
        v-for="(size, index) in pageSizeOption"
        :key="index"
        :value="size"
      >
        {{ `${size} ${translate("pagination_perpage")}` }}
      </option>
    </select>

    <div class="d-flex justify-content-center align-items-center ms-5">
      <span class="fs-8">{{ translate("pagination_goto") }}</span>
      <select
        class="form-control form-control-solid select-pagination rounded fs-8"
        v-model="currentPage"
      >
        <option
          v-for="(page, index) in [...Array(meta?.last_page).keys()]"
          :key="index"
          :value="page + 1"
        >
          {{ `${translate("pagination_page")} ${page + 1}` }}
        </option>
      </select>
    </div>
  </div>
</template>
<script setup lang="ts">
import { watch, ref, unref, onMounted } from "vue";

import { translate } from "@/helpers";
import { Meta } from "@/models/jobs";

//Define props
interface Props {
  meta: Meta;
}
const props = defineProps<Props>();

//Define emit
interface Emit {
  (e: "setPagination", value: object): void;
}
const emit = defineEmits<Emit>();

//Define data
const pageSizeOption = [5, 10, 15, 20, 30, 50];
const pageSize = ref(+props.meta?.per_page);

const currentPage = ref(props.meta?.current_page);
const links = ref([]);

//Define function
const changeCurrentPage = (item) => {
  if (!item.to || item.to == 0 || item.to == props.meta.last_page + 1) {
    return;
  }
  currentPage.value = Number(item.to);
};

const createLinks = () => {
  const totalPage = props.meta.last_page;
  links.value = [];

  if (totalPage <= 8) {
    for (let i = 1; i <= totalPage; i++) {
      links.value.push({
        label: i,
        to: i,
        active: i === currentPage.value,
      });
    }
  } else {
    const emptyLink = { label: "...", to: null, active: false };
    for (let i = 1; i <= 5; i++) {
      links.value.push({
        label: i,
        to: i,
        active: i === currentPage.value,
      });
    }
    links.value.push(emptyLink);
    for (let i = totalPage - 2; i <= totalPage; i++) {
      links.value.push({
        label: i,
        to: i,
        active: i === currentPage.value,
      });
    }
  }
};

onMounted(() => {
  if (!props.meta.links) {
    createLinks();
    return;
  }
  links.value = props.meta.links;
});

//listen change
watch(
  () => [pageSize.value, currentPage.value],
  ([pageSizeValue, currentPageValue], [prePageSize]) => {
    if (pageSizeValue !== prePageSize) currentPage.value = 1;

    //Emit event
    const data = {
      page_size: unref(pageSizeValue.toString()),
      page: unref(currentPageValue),
    };

    emit("setPagination", data);
  }
);

watch(
  () => props.meta,
  () => {
    if (!props.meta.links) {
      createLinks();
      return;
    }
    links.value = props.meta.links;
    // Trigger changeCurrentPage when meta changes
    changeCurrentPage({ to: props.meta.current_page }); // Thay đổi trang hiện tại về 1
  },
  { deep: true }
);
</script>
