<template>
  <div
    class="d-flex justify-content-between align-items-end border-bottom border-gray-500 bg-topdev-4 topbar-container container"
    :class="{ 'show-announcement-bar': layoutStore.isShowAnnouncementBar }"
  >
    <AppTabs :navItems="CompanyProfileMenuConfig" />
    <slot name="topbar"></slot>
  </div>

  <div class="company-profile-container container bg-white pt-4">
    <!--begin::Main content-->
    <slot></slot>
    <!--end::Main content-->
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";

import AppTabs from "@/components/AppTabs.vue";

import { useLayoutStore } from "@/stores";
import { CompanyProfileMenuConfig } from "@/config/menu";

const layoutStore = useLayoutStore();

onMounted(() => {
  layoutStore.setPageTitle("top_bar_company_profile");
});
</script>
