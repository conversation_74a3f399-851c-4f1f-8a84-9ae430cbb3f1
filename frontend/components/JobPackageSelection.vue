<template>
  <div class="job-package-selection">
    <h2 class="mb-3">
      <span class="text-danger">*</span> {{ $t("job_package.title") }}
    </h2>

    <div v-if="isLoading" class="text-center p-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{{ t("common.loading") }}</span>
      </div>
      <p class="mt-2 mb-0">{{ t("job_package.loading") }}</p>
    </div>

    <div v-else-if="showSimpleFreeJobView" class="alert alert-info">
      <i class="bi bi-info-circle me-2"></i>
      {{ t("job_form_free_job_message") }}
    </div>

    <div v-else class="package-container">
      <!-- Package Header -->
      <div class="package-header">
        <div class="package-name">{{ t("job_package.package") }}</div>
        <div class="package-invoice">{{ t("job_package.po_number") }}</div>
        <div class="package-expiry">{{ t("job_package.expiry_date") }}</div>
      </div>

      <div class="package-table" style="max-height: 265px; overflow-y: auto">
        <!-- Package List -->
        <div class="package-list">
          <!-- Paid Packages -->
          <div
            v-for="pkg in availablePackages"
            :key="pkg.value"
            class="package-row"
            :class="{
              selected: selectedPackage === pkg.value,
              disabled: isPackageDisabled(pkg),
            }"
            @click="!isPackageDisabled(pkg) && selectPackage(pkg)"
          >
            <div class="package-radio">
              <input
                :id="'package-' + pkg.value"
                v-model="selectedPackage"
                :value="pkg.value"
                type="radio"
                class="form-check-input"
                :disabled="isPackageDisabled(pkg)"
                @click.stop
              />
              <label
                :for="'package-' + pkg.value"
                class="d-flex align-items-center"
              >
                <img
                  v-if="getPackageIcon(pkg.label)"
                  :src="getPackageIcon(pkg.label)"
                  class="me-2"
                  style="width: 16px; height: 16px"
                  alt=""
                  onerror="this.style.display='none'"
                />
                <span
                  :class="{
                    'text-danger':
                      selectedPackage === pkg.value || pkg.label != 'Basic Job',
                    'fw-bold': selectedPackage === pkg.value,
                  }"
                >
                  {{ pkg.label }}
                </span>
              </label>
            </div>

            <div class="package-invoice">
              {{ pkg.po_number || t("job_package.free_posting") }}
            </div>

            <div class="package-expiry">
              <span
                v-if="pkg.expired_at"
                class="expiry-badge"
                :class="{ active: selectedPackage === pkg.value }"
              >
                {{ pkg.expired_at }}
              </span>
              <button v-else class="btn btn-sm btn-outline-secondary">
                {{ t("job_package.renew") }}
              </button>
            </div>
          </div>
        </div>

        <!-- Free Job Option -->
        <div
          v-if="!hideFreeJobOption"
          class="package-row free-job-option"
          :class="{
            selected: selectedPackage === 'free',
            disabled: disabled || isFreeJobDisabled,
          }"
          @click="selectFreeJob"
        >
          <div class="package-radio">
            <input
              id="package-free"
              v-model="selectedPackage"
              value="free"
              type="radio"
              class="form-check-input"
              :disabled="
                disabled ||
                isFreeJobDisabled ||
                freeJobQuota?.remaining_quota === 0
              "
              @click.stop
            />
            <label for="package-free" class="d-flex align-items-center">
              <span class="text-dark fw-bold">Free Job</span>
            </label>
          </div>
          <div class="package-invoice">
            <span class="small">
              {{ $t("job_package.remaining") }}:
              <span
                class="remaining_quota fw-bold"
                :class="{ 'free-request': freeJobQuota?.remaining_quota === 0 }"
                >{{ freeJobQuota.remaining_quota }}</span
              >
            </span>
          </div>
          <div class="package-expiry">
            <span
              class="expiry-badge"
              :class="{ 'free-request': freeJobQuota?.remaining_quota === 0 }"
              @click="requestFreeQuota"
            >
              {{ $t("job_package.renew") }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <TheFreeJobQuotaRequestModal
    ref="freeJobQuotaRequestModal"
    @submitted="onQuotaRequestSubmitted"
  />
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  watch,
  defineAsyncComponent,
} from "vue";
import { useI18n } from "vue-i18n";
import axios from "axios";
import type { ComponentPublicInstance } from "vue";
import TheFreeJobQuotaRequestModal from "@/components/TheFreeJobQuotaRequestModal.vue";

// Define the shape of the free job quota request modal component
export interface FreeJobQuotaRequestModalType extends ComponentPublicInstance {
  show: () => void;
  hide: () => void;
}

export interface JobPackage {
  value: string;
  label: string;
  invoice_number?: string;
  expired_at?: string;
  is_free_package: boolean;
}

export default defineComponent({
  name: "JobPackageSelection",
  components: { TheFreeJobQuotaRequestModal },

  props: {
    isFreeJobDisabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: String,
      default: "",
    },
    availablePackages: {
      type: Array as () => JobPackage[],
      default: () => [] as JobPackage[],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    disabledPackages: {
      type: Array as () => string[],
      default: () => [] as string[],
    },
    // Add prop to control free job visibility
    hideFreeJobOption: {
      type: Boolean,
      default: false,
    },
    // Add prop to show simple view for free jobs
    showSimpleFreeJobView: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["update:modelValue", "package-change"],

  setup(props, { emit }) {
    const { t } = useI18n();
    const selectedPackage = ref<string>(props.modelValue);
    const isLoading = ref<boolean>(false);
    const freeJobQuota = ref<{
      total_quota: number;
      used_quota: number;
      remaining_quota: number;
    } | null>(null);

    // Add ref for the free job quota request modal
    const freeJobQuotaRequestModal = ref<InstanceType<
      typeof TheFreeJobQuotaRequestModal
    > | null>(null);

    const fetchFreeJobQuota = async () => {
      try {
        const response = await axios.get(
          "/api/company/job-postings/free-job-quota"
        );
        freeJobQuota.value = response.data;
      } catch (error) {
        console.error("Failed to fetch free job quota:", error);
      }
    };

    onMounted(() => {
      fetchFreeJobQuota();
    });

    // Watch for changes to the modelValue prop
    watch(
      () => props.modelValue,
      (newVal) => {
        selectedPackage.value = newVal;
      }
    );

    // Emit update when selected package changes
    watch(selectedPackage, (newVal) => {
      emit("update:modelValue", newVal);
      // Emit package change event with whether it's a free package or not
      emit("package-change", newVal === "free");
    });

    const formatDate = (dateString: string): string => {
      if (!dateString) return "";
      return new Date(dateString).toLocaleDateString("vi-VN");
    };

    const isExpired = (dateString: string): boolean => {
      if (!dateString) return false;
      return new Date(dateString) < new Date();
    };

    const getPackageIcon = (label: string): string => {
      if (!label) return "";

      if (label.includes("Top Job")) {
        return "/assets/icons/orange-vuong-mien.svg";
      } else if (label.includes("Distinction")) {
        return "/assets/icons/orange-set.svg";
      } else if (label.includes("Basic Plus Job")) {
        return "/assets/icons/orange.svg";
      }
      return "";
    };

    const selectPackage = (pkg: JobPackage): void => {
      if (isPackageDisabled(pkg)) {
        return;
      }
      selectedPackage.value = pkg.value;
      // Emit package change event with whether it's a free package or not
      emit("package-change", pkg.value === "free");
    };

    const isPackageDisabled = (pkg: JobPackage): boolean => {
      // If the component is disabled, all packages are disabled
      if (props.disabled) return true;

      // If it's the free job option and free job is disabled
      if (pkg.value === "free" && props.isFreeJobDisabled) return true;

      // Check if package is in disabled packages list
      return props.disabledPackages.includes(pkg.value);
    };

    // Method to handle free job quota request
    const requestFreeQuota = () => {
      if (freeJobQuota.value?.remaining_quota === 0) {
        freeJobQuotaRequestModal.value?.show();
      }
    };

    // Handle successful quota request submission
    const onQuotaRequestSubmitted = () => {
      // Refresh the free job quota
      fetchFreeJobQuota();
    };

    const selectFreeJob = (): void => {
      if (props.disabled || props.isFreeJobDisabled) return;

      // If no free quota left, show the request modal
      if (freeJobQuota.value?.remaining_quota === 0) {
        requestFreeQuota();
        return;
      }

      selectedPackage.value = "free";
      // Emit package change event for free job
      emit("package-change", true);
    };

    // Return template bindings
    return {
      t,
      isLoading,
      selectedPackage,
      freeJobQuota,
      selectFreeJob,
      selectPackage,
      isPackageDisabled,
      getPackageIcon,
      freeJobQuotaRequestModal,
      requestFreeQuota,
      onQuotaRequestSubmitted,
      availablePackages: props.availablePackages,
      isFreeJobDisabled: props.isFreeJobDisabled,
      formatDate,
      isExpired,
    };
  },
});
</script>

<style scoped>
.job-package-selection {
  width: 100%;
}

.package-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.package-header {
  display: flex;
  background-color: #fff3f3;
  padding: 12px 16px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.package-name {
  flex: 2;
  padding-right: 16px;
}

.package-invoice {
  flex: 1.5;
}

.package-expiry {
  flex: 1;
}

.package-list {
  background-color: #fff;
}

.remaining_quota {
  background-color: #fed2ca;
  border-radius: 50%;
  padding: 5px 10px;

  &.free-request {
    background-color: #454545;
    color: #fff;
  }
}

.package-row {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
  cursor: pointer;

  &.disabled {
    opacity: 0.6;
    background-color: #f9f9f9;
    cursor: not-allowed;

    &:hover {
      background-color: #f9f9f9;
    }
  }

  &:hover:not(.disabled) {
    border-color: #0d6efd;
    background-color: #f8f9fa;
  }

  &.selected {
    border-color: #0d6efd;
    background-color: #f0f7ff;
  }

  &.free-job-option {
    background-color: #fff;

    .text-dark {
      color: #000 !important;
    }

    &:not(.disabled):hover {
      background-color: #f1f1f1;
    }

    &.selected {
      background-color: #fff5f5;

      .expiry-badge {
        background-color: #dd3f24;
        color: #fff;
      }
    }
  }
}

.package-row:hover {
  background-color: #f9f9f9;
}

.package-row.selected {
  background-color: #fff5f5;
}

.package-radio {
  flex: 2;
  display: flex;
  align-items: center;
}

.package-radio label {
  display: flex;
  align-items: center;
  margin: 0;
  cursor: pointer;
  width: 100%;
}

.expiry-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  background-color: #fed2ca;
  color: #454545;
  font-size: 14px;
}

.expiry-badge.active {
  background-color: #dd3f24;
  color: #fff;
}

.expiry-badge.free-request {
  background-color: #d1d1d1;
  color: #d32f2f;
  font-weight: 500;
}

.btn-outline-secondary {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 16px;
  color: #666;
  border-color: #e0e0e0;
  background-color: #fff;
}

.btn-outline-secondary:hover {
  background-color: #f5f5f5;
  border-color: #bdbdbd;
  color: #333;
}

.form-check-input {
  margin-right: 12px;
  margin-top: 0;
}

.form-check-input:checked {
  background-color: #d32f2f;
  border-color: #d32f2f;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(211, 47, 47, 0.25);
  border-color: #d32f2f;
}

.text-danger {
  color: #d32f2f !important;
}

@media (max-width: 768px) {
  .package-header {
    display: none;
  }

  .package-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 16px;
  }

  .package-radio,
  .package-invoice,
  .package-expiry {
    width: 100%;
    padding: 4px 0;
  }

  .package-expiry {
    text-align: left;
    margin-top: 8px;
  }
}
</style>
