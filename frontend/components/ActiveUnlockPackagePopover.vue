<template>
  <div class="position-relative the-search-resume-popover-unlock" ref="popoverRef">
    <button type="button" class="btn-topdev-2 btn-xs d-flex align-items-center"
      :class="item.status == 'Inactive' ? 'btn-outline-active btn-outline' : ''" :disabled="item.status != 'Inactive'"
      @click.prevent="togglePopover($event)"
      :title="translate('credit_management_active_button')">
      <span>
        {{ translate("credit_management_active_button") }}
      </span>
    </button>
    <div class="popover-active-unlock-package" :class="{ 'd-none': !isOpenPopup }">
      <div class="relative popover-active-unlock-package-content" ref="popoverBodyRef">
        <div>
          <div class="popover-caret text-gray-300">
            <svg width="14" height="7" viewBox="0 0 14 7" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 0L0 7H14L7 0Z" fill="currentColor" />
              <path d="M7 1.5L1.5 7H12.5L7 1.5Z" fill="white" />
            </svg>
          </div>
          <div>
            <p class="mb-7 text-gray-500"
              v-html="translate('credit_management_active_unlock_package_confirm')"
            >
            </p>
          </div>

          <div class="d-flex align-items-center gap-2 mt-4">
            <button type="button" class="btn btn-block flex-1 btn-reset-custom btn-sm btn-cancel btn-send-information"
              @click="isOpenPopup = false" :title="translate('search_resumes_cancel_unlock')">
              {{ translate("swal_confirm_button_cancel") }}
            </button>
            <button type="button" class="btn btn-reset-custom text-uppercase btn-block flex-1 btn-sm btn-danger btn-send-information"
              @click.prevent="() => handleActiveUnlockPackage()" :title="translate('search_resumes_confirm_unlock')">
              {{ translate("swal_confirm_activate_button") }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { translate } from "@/helpers";
import {
  defineProps,
  onMounted,
  onUnmounted,
  ref,
  defineEmits
} from "vue";

interface PropsInter {
  item: { id: number; status: "Expired" | "Active" | "Inactive" };
}

const emit = defineEmits(["activeUnlockPackage"]);
const props = defineProps<PropsInter>();
const isOpenPopup = ref<boolean>(false);
const popoverRef = ref<HTMLDivElement>();
const popoverBodyRef = ref<HTMLDListElement>();
const isLoading = ref<boolean>(false);

onMounted(() => {
  window.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  window.removeEventListener("click", handleClickOutside);
});

const handleClickOutside = (e: globalThis.MouseEvent) => {
  if (popoverRef.value && !popoverRef.value.contains(e.target as Node)) {
    isOpenPopup.value = false;
    isLoading.value = false;
  }
};

const togglePopover = ($event) => {
  popoverBodyRef.value.setAttribute('style', `top: ${$event.clientY + 25}px !important`);
  isOpenPopup.value = !isOpenPopup.value;
};

const handleActiveUnlockPackage = async () => {
  const { item } = props;
  try {
    emit("activeUnlockPackage", item);
    isOpenPopup.value = false;
  } catch (error) {
  }
};
</script>
