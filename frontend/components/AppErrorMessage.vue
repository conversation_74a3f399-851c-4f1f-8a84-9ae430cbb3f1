<template>
  <ErrorMessage :name="name" v-slot="{ message }: { message: any }">
    <span class="invalid-feedback">{{
      !!message.label
        ? translate(message.key, [message.label])
        : translate(message)
    }}</span>
  </ErrorMessage>
</template>

<script lang="ts" setup>
import { ErrorMessage } from "vee-validate";
import { translate } from "@/helpers";

interface Props {
  name: string;
}
defineProps<Props>();
</script>
