<template>
  <section id="company-image-container">
    <!--begin::Section header-->
    <div class="d-flex justify-content-between">
      <div class="section-header">
        <h2>{{ translate("about_company_company_image") }}</h2>
      </div>
    </div>
    <!--end::Section header-->

    <!--begin::Section body-->
    <div class="section-body">
      <div class="row">
        <div class="col-3">
          <!--begin::Cover upload-->
          <label class="form-label d-block mb-0">{{
            translate("about_company_cover_photo")
          }}</label>
          <p class="heading lh-sm mb-2">
            {{ translate("about_company_types_image") }}
          </p>
          <div
            id="cover-photo-container"
            :class="{ 'is-invalid': coverMeta.touched && coverError }"
          >
            <AppImageUpload
              @uploaded="changeCoverPhoto"
              type="cover"
              :url="cover"
              id="cover-image"
            />
          </div>
          <AppErrorMessage name="image_cover" />
          <!--end::Cover upload-->
        </div>

        <div class="col">
          <label class="form-label mb-0">{{
            translate("about_company_galleries")
          }}</label>
          <p class="heading lh-sm mb-2">
            {{ translate("about_company_types_image") }}
          </p>
          <div class="border gallery-upload-container">
            <div class="gallery-image-container d-flex">
              <div
                class="upload-drop-zone"
                :class="{ 'w-100 me-0': gallery.length === 0 }"
              >
                <AppImageUpload
                  @uploaded="addGalleryPhoto"
                  type="gallery"
                  id="gallery-image"
                />
              </div>
              <swiper
                :modules="[Scrollbar]"
                slides-per-view="auto"
                :space-between="5"
                :scrollbar="{ draggable: true }"
              >
                <swiper-slide
                  v-for="(item, index) in gallery"
                  :key="index"
                  class="w-auto position-relative"
                >
                  <button
                    class="btn btn-sm p-0 position-absolute top-0 end-0"
                    type="button"
                    @click="removeGallery(index)"
                  >
                    <inline-svg src="/assets/icons/remove-img.svg" />
                  </button>
                  <img
                    class="img-fluid rounded"
                    style="height: 250px"
                    :src="item.value.url"
                  />
                </swiper-slide>
              </swiper>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--end::Section body-->
  </section>
</template>

<script lang="ts" setup>
import { useField, useFieldArray } from "vee-validate";
import { Scrollbar } from "swiper";
import { Swiper, SwiperSlide } from "swiper/vue";

import AppImageUpload from "@/components/AppImageUpload.vue";
import AppErrorMessage from "@/components/AppErrorMessage.vue";

import { translate } from "@/helpers";
import { Image } from "@/models/employer";

/**
 * Define data
 */
const {
  value: cover,
  meta: coverMeta,
  errorMessage: coverError,
} = useField<string>("image_cover");
const {
  fields: gallery,
  prepend: prependGallery,
  remove: removeGallery,
} = useFieldArray<Image>("image_galleries");

/**
 * Define function
 */
const changeCoverPhoto = (url: string) => {
  cover.value = url;
};

const addGalleryPhoto = (url: string) => {
  prependGallery({
    id: null,
    url: url,
  });
};
</script>
