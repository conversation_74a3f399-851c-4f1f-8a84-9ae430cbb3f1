<template>
  <!--begin::Contact information container-->
  <section id="contact-information-container">
    <div class="mb-5">
      <h2 class="mb-0">
        {{ translate("contact_information_contact_information") }}
      </h2>
      <span class="heading">{{
        translate("contact_information_information_should_be_updated")
      }}</span>
    </div>

    <div class="d-flex flex-column gap-3" v-if="!isLoadingContact">
      <div class="form-group">
        <label for="txt-email" class="form-label">{{
          translate("contact_information_email_address")
        }}</label>
        <div class="input-with-icon">
          <span class="icon" :class="{ 'invalid-icon': emailError }">
            <inline-svg src="/assets/icons/email.svg" />
          </span>
          <input
            type="text"
            id="txt-email"
            class="form-control"
            :placeholder="translate('contact_information_email_placeholder')"
            :class="{ 'is-invalid': emailError }"
            :disabled="!editingEmail"
            v-model="email"
          />
          <span
            class="right-icon"
            :class="{ 'invalid-icon': emailError }"
            v-if="!emailError"
          >
            <button
              type="button"
              class="btn btn-link btn-active"
              @click="enableEditEmail()"
              v-if="!editingEmail"
            >
              <span class="active-icon">
                <inline-svg
                  src="/assets/icons/pen.svg"
                  height="17px"
                  width="17px"
                />
              </span>
              {{ translate("contact_information_edit") }}
            </button>
            <button
              type="button"
              class="btn btn-link btn-active"
              @click="saveEmail()"
              v-else
            >
              <span>
                <inline-svg
                  src="/assets/icons/check.svg"
                  height="23px"
                  width="23px"
                />
              </span>
              {{ translate("contact_information_save_change") }}
            </button>
          </span>
          <AppErrorMessage name="email" />
        </div>
      </div>
      <div class="form-group">
        <label for="txt-phone" class="form-label">{{
          translate("contact_information_phone")
        }}</label>
        <div class="input-with-icon">
          <span class="icon" :class="{ 'invalid-icon': phoneError }">
            <inline-svg src="/assets/icons/phone.svg" />
          </span>
          <input
            type="text"
            id="txt-phone"
            class="form-control"
            :class="{ 'is-invalid': phoneError }"
            :placeholder="translate('contact_information_phone_placeholder')"
            :disabled="!editingPhone"
            v-model="phone"
          />
          <span
            class="right-icon"
            :class="{ 'invalid-icon': phoneError }"
            v-if="!phoneError"
          >
            <button
              type="button"
              class="btn btn-link btn-active"
              @click="enableEditPhone()"
              v-if="!editingPhone"
            >
              <span class="active-icon">
                <inline-svg
                  src="/assets/icons/pen.svg"
                  height="17px"
                  width="17px"
                />
              </span>
              {{ translate("contact_information_edit") }}
            </button>
            <button
              type="button"
              class="btn btn-link btn-active"
              @click="savePhone()"
              v-else
            >
              <span>
                <inline-svg
                  src="/assets/icons/check.svg"
                  height="23px"
                  width="23px"
                />
              </span>
              {{ translate("contact_information_save_change") }}
            </button>
          </span>
          <AppErrorMessage name="phone" />
        </div>
      </div>
    </div>

    <!--begin::Skeleton-->
    <div class="d-flex flex-column gap-3" v-else>
      <div class="form-group">
        <div class="form-label">
          <Skeletor width="150px" height="23px" />
        </div>
        <Skeletor height="39px" />
      </div>
      <div class="form-group">
        <div class="form-label">
          <Skeletor width="150px" height="23px" />
        </div>
        <Skeletor height="39px" />
      </div>
    </div>
    <!--end::Skeleton-->
  </section>
  <!--end::Contact information container-->
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useField, useForm } from "vee-validate";

import AppErrorMessage from "@/components/AppErrorMessage.vue";

import { useLayoutStore } from "@/stores";
import {
  getContactInformation,
  updateContactInformation,
} from "@/api/employer";
import { showSuccesToast, showWarningToast, translate } from "@/helpers";
import { changeEmailPhoneAccountFormSchema } from "@/schemas/auth-form";
import { Contact } from "@/models/employer";

/**
 * Define data
 */
const contact = ref<Contact>();
const isLoadingContact = ref<boolean>(true);

/**
 * Store init
 */
const layoutStore = useLayoutStore();

/**
 * Form init
 */
const { resetForm } = useForm({
  validationSchema: changeEmailPhoneAccountFormSchema,
});

// No need to define rules for fields
const {
  value: email,
  meta: emailMeta,
  errorMessage: emailError,
} = useField("email");
const {
  value: phone,
  meta: phoneMeta,
  errorMessage: phoneError,
} = useField("phone");

const editingEmail = ref(false);
const editingPhone = ref(false);

/**
 * Event handler
 */
const enableEditEmail = () => {
  editingEmail.value = true;
};

const enableEditPhone = () => {
  editingPhone.value = true;
};

const saveEmail = () => {
  if (!emailMeta.dirty) {
    editingEmail.value = false;
    return;
  }

  if (!emailMeta.valid) {
    showWarningToast(
      translate("toast_warning"),
      translate("toast_email_invalid")
    );
    return;
  }

  // Start update email
  layoutStore.blockPage();

  updateContactInformation({
    email: email.value as string,
  }).then(() => {
    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );

    editingEmail.value = false;
    layoutStore.unBlockPage();
  });
};

const savePhone = () => {
  if (!phoneMeta.dirty) {
    editingPhone.value = false;
    return;
  }

  if (!phoneMeta.valid) {
    showWarningToast("Warning", "Phone is not valid");
    return;
  }

  // Start updating
  layoutStore.blockPage();

  updateContactInformation({
    phone: phone.value as string,
  }).then(() => {
    showSuccesToast(
      translate("toast_congrats"),
      translate("toast_save_successfully")
    );

    editingPhone.value = false;
    layoutStore.unBlockPage();
  });
};

/**
 * Life cycle
 */
onMounted(() => {
  isLoadingContact.value = true;

  /**
   * Get data contact
   */
  getContactInformation()
    .then((response) => {
      contact.value = response.data as Contact;

      resetForm({
        values: {
          email: contact.value.email,
          phone: contact.value.phone,
        },
      });

      // Done loading
      isLoadingContact.value = false;
    })
    .catch(() => {
      // TODO: handle error
    });
});
</script>
