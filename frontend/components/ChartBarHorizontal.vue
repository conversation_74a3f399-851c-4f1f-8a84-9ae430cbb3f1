<template>
  <div>
    <!-- Check if data is empty -->
    <div v-if="!data || data.length === 0" class="flex items-center justify-center h-full text-[#454545] text-sm font-semibold">
      You don't have any data to show
    </div>
    <!-- Render chart if data exists -->
    <div v-else>
      <div v-for="(item, index) in data" :key="index" class="flex items-center gap-6">
        <div class="flex flex-col">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-1 w-[182px]">
              <span class="block w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></span>
              <span class="text-[#454545] text-sm">{{ item?.label ? translate(item.label) : '' }}</span>
              <span class="px-2 rounded-full text-xs" :style="{ backgroundColor: item.bgColor, color: item.textColor }">
                {{ item.count }}
              </span>
            </div>
            <div class="flex items-center gap-2 w-[160px] xl-plus:w-[330px] h-6">
              <div class="h-6" :style="{ backgroundColor: item.color, width: `${item.percent || 0.5}%` }">
              </div>
              <span class="text-sm font-semibold" :style="{ color: item.color }">
                {{ item.percent }}%
              </span>
            </div>
          </div>
          <div v-if="index !== data.length - 1" class="h-8 w-[1px] ml-[6px] bg-[#B0B0B0]"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { translate } from "@/helpers";
import { ChartDataItem } from "@/models/dashboard";

const props = defineProps<{
  data: ChartDataItem[]
}>();
</script>