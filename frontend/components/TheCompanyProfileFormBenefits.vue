<template v-if="!!benefitsList">
  <section id="company-benefits-container">
    <!--begin::Section header-->
    <div class="justify-content-between">
      <div class="section-header">
        <h2 class="mb-0 required">
          {{ translate("about_company_company_benefits") }}
        </h2>
        <span class="heading">{{
          translate("about_company_add_company_benefits")
        }}</span>
      </div>
      <AppTextEditor
        name="benefits"
        :initValue="benefits"
        :class="{ 'is-invalid': benefitMeta.dirty && benefitError }"
        :height="280"
        @update:initValue="(value) => changeBenefits(value)"
      />
    </div>
    <!--end::Section header-->
  </section>
</template>

<script lang="ts" setup>
import { useField} from "vee-validate";

import { translate } from "@/helpers";
import AppTextEditor from "@/components/AppTextEditor.vue";

const {
  value: benefits,
  meta: benefitMeta,
  errorMessage: benefitError,
  handleChange: changeBenefits,
} = useField<string>("benefits");
</script>
