<template>
  <div class="media-upload-container">
    <div
      v-if="url"
      class="d-flex align-items-center justify-content-center mh-100 overflow-hidden position-relative img-container"
    >
      <div
        class="position-absolute h-100 w-100 d-flex align-items-center justify-content-center opacity-0 change-backdrop"
      >
        <label :for="id" class="btn btn-sm btn-secondary rounded-pill">{{
          translate("about_company_image_placeholder")
        }}</label>
      </div>
      <img :src="url" class="img-fluid mh-100" />
    </div>
    <label
      v-else
      :for="id"
      class="d-flex flex-column align-items-center justify-content-center gap-3 w-100 h-100 cursor-pointer px-1 py-2"
    >
      <inline-svg
        src="/assets/icons/img-placeholder.svg"
        width="58px"
        height="58px"
      />
      <p
        v-show="!isHideDescription"
        class="text-topdev-2 mb-0 fs-7"
        v-html="translate('about_company_image_description')"
      ></p>
    </label>
    <input
      type="file"
      :id="id"
      class="d-none"
      @change="uploadImage($event.target.files)"
    />
  </div>
</template>

<script lang="ts" setup>
import { uploadMedia } from "@/api/files";
import { showWarningToast, translate } from "@/helpers";

// Define props
interface Props {
  type: string;
  url?: string;
  id: string;
  isHideDescription?: boolean;
}
const props = defineProps<Props>();

// Define emit
interface Emit {
  (e: "uploaded", value: string): void;
  (e: "uploading"): void;
  (e: "error", value: any): void;
}
const emit = defineEmits<Emit>();

// Function upload logo
const uploadImage = async (files: FileList) => {
  if (files.length == 0) {
    return;
  }

  if (files[0].size > 1024 * 1024 * 5) {
    showWarningToast(
      translate("toast_upload_failed"),
      translate("toast_image_not_exceed_5mb")
    );
    return;
  }

  if (files[0].type !== "image/png" && files[0].type !== "image/jpeg") {
    showWarningToast(
      translate("toast_warning"),
      translate("toast_just_accept_png_jpeg_files")
    );
    return;
  }

  emit("uploading");

  const response: any = await uploadMedia(props.type, files[0]);

  emit("uploaded", response.url);
};
</script>
