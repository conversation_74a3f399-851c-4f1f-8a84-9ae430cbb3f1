<template>
  <div class="the-search-resume-candidate-information-container">
    <div class="d-flex align-items-end gap-4">
      <!-- ! <PERSON><PERSON> thiếu trạng thái tìm việc -->
      <div
        class="candidate-avatar"
        :class="{
          // 'status-actively-seeking': true,
          // 'status-open-to-work': false,
        }"
      >
        <img
          :src="resume.avatar_url ?? '/assets/images/img-avatar.png'"
          alt="avatar"
          class="img-avatar"
        />
      </div>
      <div>
        <p
          class="m-0 cursor candidate-fullname font-bold"
          @click="toSearchResumeDetail(resume.id)"
          title="Go to detail"
        >
          {{ resume.fullname }}
        </p>
        <div class="d-flex align-items-center flex-wrap">
          <p v-show="resume.current_job" class="m-0 candidate-current-job">
            {{ resume.current_job }}
          </p>
          <p v-show="resume.years_of_exp !== null" class="m-0 years-of-exp">
            <span class="inline-block mx-2" v-show="resume.current_job">
              &#8226;
            </span>
            {{ resume.years_of_exp ?? 0 }}
            {{ translate("search_resumes_candidate_yoe") }}
          </p>
        </div>
      </div>
    </div>
    <ul class="m-0 p-0 mt-4 list-reset-custom candidate-list-info">
      <li v-show="resume.province">
        <span class="font-bold">
          {{ translate("search_resumes_candidate_address") }}:
        </span>
        <span class="text-gray-500">{{ resume.province }}</span>
      </li>
      <li v-show="resume.email">
        <span class="font-bold">Mail: </span>
        <span class="text-gray-500">{{ resume.email }}</span>
      </li>
      <li v-show="resume.phone">
        <span class="font-bold">Phone: </span>
        <span class="text-gray-500">{{ resume.phone }}</span>
      </li>
    </ul>
    <ul class="m-0 p-0 mt-4 list-reset-custom candidate-list-info">
      <li v-if="resume.viewed_count > 0">
        <span class="font-bold">
          {{ `${translate("search_resumes_views")} ` }}
        </span>
        <span class="text-gray-500">
          {{ resume.viewed_count }}
        </span>
      </li>
      <li v-show="resume.unlocked_count">
        <span class="font-bold">
          {{ `${translate("search_resumes_unlocks")} ` }}
        </span>
        <span class="text-gray-500">
          {{ resume.unlocked_count }}
        </span>
      </li>
      <li v-show="resume.last_updated_at">
        <span class="font-bold">
          {{ `${translate("search_resumes_last_updated")} ` }}
        </span>
        <span class="text-gray-500">
          {{ resume.last_updated_at }}
        </span>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { showWarningToast, translate } from "@/helpers";
import { SearchCandidate } from "@/models/search-resumes";
import { defineProps, ref } from "vue";
import { checkMaxAllowClickCandidate } from "@/api/search-resume";

const { resume, isResumeDetailPage } = defineProps<{
  resume: SearchCandidate;
  isResumeDetailPage?: boolean;
}>();

const toSearchResumeDetail = async (id: number) => {
  try {
    let {
      data
    }: { data: { is_allow: boolean } } = await checkMaxAllowClickCandidate();

    if (!data.is_allow) {
      showWarningToast(
        translate("toast_something_went_wrong"),
        translate("search_resumes_max_allow_click_before")
      );
    }

    if (isResumeDetailPage) {
      return;
    }
    window.open(`/search-candidates/${id}${document.location.search}`, "_blank");
  } catch (error) {
    showWarningToast(
      "",
      translate("toast_something_went_wrong"),
    );
  }
};
</script>
