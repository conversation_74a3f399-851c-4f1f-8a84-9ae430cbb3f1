<template>
  <div
    class="modal fade"
    id="side-banner-modal"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body mt-4 px-4 pb-4">
          <!-- Show content banner  -->
          <div class="justify-content-between d-flex flex-wrap">
            <div id="image-banner-modal" class="image-side-banner-modal">
              <img
                class="maxw-245px"
                :src="dataModal?.image"
                :alt="dataModal?.title"
              />
            </div>
            <div class="content-side-banner-modal">
              <h3 class="title-side-banner-modal text-uppercase">
                {{ dataModal?.title }}

                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </h3>
              <div
                v-html="dataModal?.description"
                class="desc-side-banner-modal"
              ></div>
            </div>
          </div>
        </div>
        <div class="modal-footer pt-4 pb-4">
          <div class="d-flex align-items-center contact-now">
            <img
              src="/assets/images/phone-icon.png"
              alt="Phone number"
              class="img-fluid contact-img"
            />
            <div>
              <p class="contact-title">{{ translate("layout_contact_now") }}</p>
              <p class="contact-number">028 6273 3496</p>
            </div>
          </div>
          <a
            href="https://topdev.vn/recruit"
            target="_blank"
            class="btn btn-consultation"
            @click="onSendMeInformation"
            :title="translate('layout_sign_up_for_consultation')"
          >
            {{ translate("layout_sign_up_for_consultation") }}
          </a>
        </div>
      </div>
    </div>
  </div>
  <!-- End modal -->
</template>

<script lang="ts" setup>
import { watch, ref } from "vue";
import { Modal } from "bootstrap";
import { translate } from "@/helpers";
import { useLayoutStore } from "@/stores";

//Define data
const dataModal = ref({
  title: "",
  image: "",
  description: "",
});

const layoutStore = useLayoutStore();

const onSendMeInformation = () => {
  const tawkbutton = document.querySelector(
    ".tawk-button-large"
  ) as HTMLElement;
  if (tawkbutton) {
    tawkbutton.click();
  }

  onCloseModal();
};

const onCloseModal = () => {
  Modal.getOrCreateInstance("#side-banner-modal").hide();
};

//Life cycle
watch(
  () => layoutStore.dataBanner,
  (dataBanner) => {
    const dataBannerNew = {
      ...dataBanner,
      ...{ image: dataBanner.image },
    };
    dataModal.value = dataBannerNew;
  },
  { deep: true }
);
</script>
