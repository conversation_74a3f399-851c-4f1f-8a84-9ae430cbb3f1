<template>
  <!-- Candidate skills -->
  <div class="d-flex flex-wrap align-items-center">
    <p class="m-0 font-bold container-title">Skill(s):</p>
    <div class="flex-1 overflow-hidden">
      <swiper
        :spaceBetween="5"
        :slidesPerView="'auto'"
        class="tag-skill-container"
        v-if="checkTypeSkills(resume.skills)"
      >
        <swiper-slide
          class="tag"
          v-for="(skill, index) in resume.skills"
          :key="index"
        >
          {{ skill.skill_name }}
        </swiper-slide>
      </swiper>
    </div>
  </div>

  <!-- Candidate experience and candidate education -->
  <div
    class="space-y-2 box_search_resumes_work_experience_education"
    :key="resume.id"
  >
    <!-- Candidate experience -->
    <div
      class="flex align-items-start"
      v-show="
        resume.experiences &&
        resume.experiences.length > 0 &&
        resume.experiences.filter((item) => item.position)
          .length > 0
      "
    >
      <p class="m-0 font-bold container-title">
        {{ translate("search_resumes_work_experience") }}
      </p>
      <ul class="flex-1 candidate-experience-education-list">
        <li
          v-for="(experience, index) in resume.experiences"
          :key="index"
        >
          <p class="flex-wrap mb-1 line-clamp-1">
            <span
              class="experience-time text-gray-400"
              v-show="experience.from || experience.to"
            >
              {{ experience.from }} - {{ experience.to ?? translate("search_resumes_present") }}
            </span>
            <span
              class="space-line"
              v-show="
                (experience.position || experience.company) &&
                (experience.from || experience.to)
              "
            ></span>
            <span
              class="text-gray-600 font-bold"
              v-show="
                experience.position || experience.company
              "
            >
              {{ experience.position }} -
              {{ experience.company }}
            </span>
          </p>
        </li>
      </ul>
    </div>

    <!-- Candidate education -->
    <div
      class="flex align-items-start mt-2"
      v-show="
        resume.educations && resume.educations.length > 0
      "
    >
      <p class="m-0 font-bold container-title">
        {{ translate("search_resumes_education") }}
      </p>
      <ul class="candidate-experience-education-list">
        <li
          v-for="(education, index) in resume.educations"
          :key="index"
        >
          <p class="flex-wrap mb-1 line-clamp-1">
            <span
              class="experience-time text-gray-400"
              v-show="education.from"
            >
              {{ education.from }} - {{ education.to ?? translate("search_resumes_present") }}
            </span>
            <span
              class="space-line"
              v-show="
                (education.from || education.to) &&
                education.school_name
              "
            ></span>
            <span
              class="text-gray-600 font-bold"
              v-show="education.school_name"
            >
              {{ education.school_name }}
            </span>
          </p>
        </li>
      </ul>
    </div>

    <!-- See more information -->
    <button
      @click="onSeeMore(resume.id)"
      :class="`btn btn-sm btn-see-more btn-${resume.id}`"
      :title="translate('search_resumes_see_more')"
    >
      {{ translate("search_resumes_see_more") }}
      <inline-svg
        src="assets/icons/arrow-down.svg"
        width="16"
        height="16"
        alt="arrow down"
      />
    </button>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { translate } from "@/helpers";
import { SearchCandidate } from "@/models/search-resumes";

const { resume } = defineProps<{
  resume: SearchCandidate;
}>();

const checkTypeSkills = (skills: Array<object>) => {
  if (!skills) return;
  return skills.filter((skill) => typeof skill === "object")?.length > 0;
};

const onSeeMore = (id: number) => {
  let btn = document.querySelector(`.btn-${id}`);
  btn.classList.add("d-none");
  btn.parentElement.classList.add("no-fix");
};
</script>
