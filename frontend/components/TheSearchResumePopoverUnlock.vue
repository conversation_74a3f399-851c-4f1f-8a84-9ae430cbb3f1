<template>
  <div
    class="position-relative the-search-resume-popover-unlock"
    ref="popoverRef"
  >
    <button
      type="button"
      class="btn-reset-custom text-center btn-unlock-info"
      :class="[btnSize === 'md' ? 'btn-md' : 'btn-sm']"
      @click.prevent="togglePopover"
      :disabled="isLoading"
      :title="translate('search_resumes_unlock_label')"
    >
      <span class="text-base">
        <i class="fa fa-lock" aria-hidden="true"></i>
      </span>
      <span>
        {{ translate("search_resumes_unlock_label") }}
      </span>
    </button>
    <div class="candidate-popover-unlock" :class="{ 'd-none': !isOpenPopup }">
      <div class="relative candidate-popover-unlock-content">
        <div v-if="isLoading">Loading...</div>
        <div v-else>
          <div class="popover-caret text-gray-300">
            <svg
              width="14"
              height="7"
              viewBox="0 0 14 7"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M7 0L0 7H14L7 0Z" fill="currentColor" />
              <path d="M7 1.5L1.5 7H12.5L7 1.5Z" fill="white" />
            </svg>
          </div>
          <div class="d-flex flex-row justify-content-between gap-2">
            <div class="flex-1">
              <template v-if="isEnough">
                <p
                  class="mb-2 text-gray-500"
                  v-html="
                    translate(
                      'search_resumes_click_confirm_to_unlock_and_view_candidate_detail'
                    )
                  "
                ></p>
                <p class="mb-7 font-bold text-gray-500">
                  <span>
                    {{
                      translate("search_resumes_current_number_of_opening") + " "
                    }}
                  </span>
                  <span class="text-danger">
                    {{ searchResumesStore.credits }}
                  </span>
                </p>
              </template>
              <!-- there are some inactive unlock packages -->
              <template v-else-if="inactivePackage > 0">
                <div
                  v-html="translate('search_resumes_having_inactive_unlock_package', [inactivePackage])"
                ></div>
              </template>
              <!-- else -->
              <template v-else>
                <div class="mb-2"
                  v-html="translate('search_resumes_do_not_having_inactive_unlock_package')"
                ></div>
                <div
                  v-html="translate('search_resumes_contact_topdev_team_to_be_supported')"
                ></div>
              </template>
            </div>

            <div class="candidate-popover-unlock-close-icon">
              <span @click="isOpenPopup = false" class="svg-icon me-n1 text-gray-600 text-md">
                <inline-svg src="/assets/icons/close.svg" />
              </span>
            </div>
          </div>

          <div class="d-flex align-items-center gap-2 mt-4 justify-content-end">
            <template
              v-if="isEnough">
              <button
                type="button"
                class="btn btn-block flex-1 btn-reset-custom btn-sm btn-cancel btn-send-information"
                @click="isOpenPopup = false"
                :title="translate('search_resumes_cancel_unlock')"
              >
                {{ translate("search_resumes_cancel_unlock") }}
              </button>
              <button
                type="button"
                class="btn btn-reset-custom btn-block flex-1 btn-sm btn-danger btn-send-information"
                @click.prevent="() => handleUnlockResume()"
                :title="translate('search_resumes_confirm_unlock')"
              >
                {{ translate("search_resumes_confirm_unlock") }}
              </button>
            </template>
            <!-- there are some inactive unlock packages -->
            <template v-else-if="inactivePackage > 0">
              <div class="d-flex w-50 justify-content-end">
                <a
                  type="button"
                  target="_blank"
                  href="/unlock-candidate-management"
                  class="btn btn-reset-custom btn-block flex-1 btn-sm btn-danger btn-send-information"
                  :title="translate('search_resumes_activate_button')"
                >
                  {{ translate("search_resumes_activate_button") }}
                </a>
              </div>
            </template>
            <template v-else>
              <button
                type="button"
                class="btn btn-reset-custom btn-block flex-1 btn-sm btn-secondary btn-send-information"
                @click.prevent="
                  () => handleSendNotificationNotEnoughCredit(resume.id)
                "
                :title="translate('search_resumes_send_information')"
              >
                {{ translate("search_resumes_send_information") }}
              </button>
              <a
                type="button"
                target="_blank"
                class="btn btn-reset-custom btn-block flex-1 btn-sm btn-danger"
                href="https://topdev.vn/products"
                :title="translate('search_resumes_buy_unlock_package')"
              >
                {{ translate("search_resumes_buy_unlock_package") }}
              </a>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  checkCreditBeforeUnlockCandidate,
  checkMaxAllowBeforeUnlockCandidate,
  fetchSendMeNotification
} from "@/api/search-resume";
import { showSuccesToast, showWarningToast, translate } from "@/helpers";
import { onCreateUnlockResumeDraft } from "@/helpers/searchCandidate";
import { useSearchResumesStore } from "@/stores";
import {
  defineProps,
  onMounted,
  onUnmounted,
  ref,
  watch,
  defineEmits
} from "vue";

interface PropsInter {
  resume: { id: number; credit: number };
  btnSize?: "md" | "sm";
}

const emit = defineEmits(["unlockResume"]);
const props = defineProps<PropsInter>();
const isOpenPopup = ref<boolean>(false);
const popoverRef = ref<HTMLDivElement>();
const searchResumesStore = useSearchResumesStore();
const isLoading = ref<boolean>(false);
const isReloadPage = ref<boolean>(false);
const isEnough = ref<boolean>(false);
const isAllowUnlock = ref<boolean>(false);
const inactivePackage = ref<number>(0);

onMounted(() => {
  window.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  window.removeEventListener("click", handleClickOutside);
});

watch(
  () => isReloadPage.value,
  async () => {
    if (isReloadPage.value) {
      searchResumesStore.setResumesParams(searchResumesStore.resumesParams);
      isReloadPage.value = false;
    }
  },
  { deep: true }
);

const handleClickOutside = (e: globalThis.MouseEvent) => {
  if (popoverRef.value && !popoverRef.value.contains(e.target as Node)) {
    isOpenPopup.value = false;
    isLoading.value = false;
  }
};

const togglePopover = async () => {
  isOpenPopup.value = !isOpenPopup.value;

  if (isOpenPopup.value) {
    try {
      const { resume } = props;
      isLoading.value = true;
      await checkUnlockResume(resume.id, resume.credit);

      let {
        data
      }: { data: { is_allow: boolean } } = await checkMaxAllowBeforeUnlockCandidate();

      isAllowUnlock.value= data.is_allow;
    } catch (error) {
      showWarningToast(
        "",
        translate("toast_something_went_wrong"),
      );
    } finally {
      isLoading.value = false;

      if (!isAllowUnlock.value) {
        showWarningToast(
          translate("toast_something_went_wrong"),
          translate("search_resumes_max_allow_unlock_before")
        );
      }
    }
  }
};

const checkUnlockResume = async (id: number, credit: number) => {
  try {
    let {
      data
    }: { data: {
      available: number;
      credit: number;
      is_unlocked: boolean;
      inactive_packages: number
  } } =
      await checkCreditBeforeUnlockCandidate(id);
    if (!data) throw Error("Error checking credit before unlock candidate");

    if (data.available) {
      searchResumesStore.updateCredit(data.available);
    }

    inactivePackage.value = data.inactive_packages;

    if (data.is_unlocked) {
      showWarningToast(
        translate("toast_something_went_wrong"),
        translate("search_resumes_resume_unlock_before")
      );

      // reload list candidate here
      isReloadPage.value = true;
    } else if (data.available && data.available >= credit) {
      isEnough.value = true;
    } else {
      isEnough.value = false;
    }
  } catch (error) {
    showWarningToast(
        "",
        translate("toast_something_went_wrong"),
      );
  }
};

const handleSendNotificationNotEnoughCredit = async (id: number) => {
  if (!isEnough.value) {
    let resData: any = await fetchSendMeNotification(id);
    if (resData.error) {
      showWarningToast(
        translate("toast_something_went_wrong"),
        translate("toast_save_failed_message")
      );
    } else {
      showSuccesToast(
        translate("toast_congrats"),
        translate("toast_send_me_in_formation")
      );
    }
    isReloadPage.value = true;
  }
};

const handleUnlockResume = async () => {
  if (!isEnough.value && !isLoading.value) {
    return;
  }
  const { resume } = props;
  try {
    let key = await onCreateUnlockResumeDraft(resume.id, resume.credit);

    if (key) {
      emit("unlockResume", resume.id, key);
      isOpenPopup.value = false;
    }
  } catch (error) {
  }
};
</script>
