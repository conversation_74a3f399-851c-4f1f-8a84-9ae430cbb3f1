<template>
  <div>
    <!-- Tabs  -->
    <ul class="nav nav-tabs nav-line-tabs">
      <li
        v-for="(item, index) in navItems"
        :key="index"
        class="nav-item"
        @click="onClick(item)"
      >
        <router-link
          :to="{ name: item.name }"
          class="nav-link"
          :class="{ active: item.name === $route.name }"
          >{{ translate(item.title) }}</router-link
        >
      </li>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { translate } from "@/helpers";
import { useJobStore } from "@/stores";

interface TabItem {
  name: string;
  title: string;
}

interface Props {
  navItems: Array<TabItem>;
}

defineProps<Props>();

const jobStore = useJobStore();

const onClick = (item: TabItem) => {
  if (item.name === "post-job") {
    jobStore.setIDAndStatus(undefined, "add");
  }
};
</script>
