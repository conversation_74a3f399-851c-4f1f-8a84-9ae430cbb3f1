<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-notification-new-feature-not-approve"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div
      class="modal-dialog position-absolute top-50 start-50 translate-middle"
    >
      <div class="modal-content">
        <!-- modal header  -->
        <div class="modal-header">
          <div
            @click="handleCloseModalNewFeaure"
            class="position-absolute end-0 cursor-pointer btn-close-modal"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.8499 16.4399C17.9445 16.5337 17.9978 16.6615 17.9978 16.7949C17.9978 16.9282 17.9445 17.056 17.8499 17.1499L17.1499 17.8499C17.056 17.9445 16.9282 17.9978 16.7949 17.9978C16.6615 17.9978 16.5337 17.9445 16.4399 17.8499L11.9999 13.4099L7.55985 17.8499C7.46597 17.9445 7.33817 17.9978 7.20485 17.9978C7.07153 17.9978 6.94374 17.9445 6.84985 17.8499L6.14985 17.1499C6.0552 17.056 6.00195 16.9282 6.00195 16.7949C6.00195 16.6615 6.0552 16.5337 6.14985 16.4399L10.5899 11.9999L6.14985 7.55985C6.0552 7.46597 6.00195 7.33817 6.00195 7.20485C6.00195 7.07153 6.0552 6.94374 6.14985 6.84985L6.84985 6.14985C6.94374 6.0552 7.07153 6.00195 7.20485 6.00195C7.33817 6.00195 7.46597 6.0552 7.55985 6.14985L11.9999 10.5899L16.4399 6.14985C16.5337 6.0552 16.6615 6.00195 16.7949 6.00195C16.9282 6.00195 17.056 6.0552 17.1499 6.14985L17.8499 6.84985C17.9445 6.94374 17.9978 7.07153 17.9978 7.20485C17.9978 7.33817 17.9445 7.46597 17.8499 7.55985L13.4099 11.9999L17.8499 16.4399Z"
                fill="#424242"
              />
            </svg>
          </div>
          <div class="d-flex modal-header-content">
            <img
              alt="New Futures"
              class=""
              src="/assets/images/new-feature-image.png"
            />
            <div class="modal-header-content-right">
              <span class="highligt-new-feature">{{
                translate("layout_popup_new_tag")
              }}</span>
              <div class="d-flex flex-column">
                <span class="modal-title-banner">SEARCH CV</span>
                <span
                  class="text-start modal-description-banner"
                  v-html="translate('layout_popup_header_description')"
                ></span>
              </div>
            </div>
          </div>
        </div>
        <!-- the end modal header  -->

        <!-- modal body  -->
        <div class="modal-body max-w-512px max-h-136px">
          <div class="d-flex flex-column benefit-list">
            <div>
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_first") }}</span>
            </div>
            <div class="benefit-item">
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_second") }}</span>
            </div>
            <div class="benefit-item">
              <span class="svg-icon">
                <inline-svg src="/assets/icons/candidates/Icon_benefit.svg" />
              </span>
              <span>{{ translate("layout_popup_body_benefit_third") }}</span>
            </div>
            <a
              type="button"
              href="https://topdev.vn/products"
              @click="handleCloseModalNewFeaure"
              class="btn btn-lg cursor-pointer btn-search-cv"
            >
              {{ translate("layout_popup_body_button") }}
            </a>
          </div>
        </div>
        <!-- the end modal body    -->

        <!-- modal footer -->
        <div class="modal-footer max-w-512px max-h-136px">
          <div class="d-flex flex-column modal-footer-content">
            <span
              class="modal-footer-content--text"
              v-html="translate('layout_popup_footer_first')"
            ></span>
            <span class="modal-footer-content--text">{{
              translate("layout_popup_footer_second")
            }}</span>
            <div class="d-flex gap-6">
              <span class="my-0 text-primary"
                ><b class="modal-footer-content--text">Hotline:</b> +8428 6656
                7848</span
              >
              <span class="text-primary">
                <b class="text-black modal-footer-content--text">Email:</b>
                <EMAIL>
              </span>
            </div>
          </div>
        </div>
        <!-- The end modal footer  -->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import { translate } from "@/helpers";
import { Modal } from "bootstrap";
import { useAuthStore } from "@/stores";
import { useRouter } from "vue-router";
const authStore = useAuthStore();

const router = useRouter();

const handleCloseModalNewFeaure = () => {
  Modal.getInstance("#modal-notification-new-feature-not-approve").hide();
  localStorage.setItem(
    "lastClosedTimeNewFuature",
    new Date().getTime().toString()
  );
};

onMounted(() => {
  const modalClosedTime = localStorage.getItem("lastClosedTimeNewFuature");
  const currentTime = new Date().getTime();
  const timeDifference = currentTime - parseInt(modalClosedTime || "0");

  if (!authStore.user.is_unlocked) {
    if (!modalClosedTime || timeDifference >= 24 * 60 * 60 * 1000) {
      Modal.getOrCreateInstance(
        "#modal-notification-new-feature-not-approve"
      ).show();
    }
  }
});
</script>
