<template>
  <div class="debug-panel" :class="{ 'collapsed': isCollapsed }">
    <div class="debug-panel-header" @click="toggleCollapse">
      <span>Debug Panel =))</span>
      <i :class="['fas', isCollapsed ? 'fa-chevron-left' : 'fa-chevron-right']"></i>
    </div>
    <div v-if="!isCollapsed" class="debug-panel-content">
      <pre>{{ data }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DebugPanel',
  props: {
    data: {
      type: Object,
      required: false
    }
  },
  data() {
    return {
      isCollapsed: true
    };
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    }
  }
};
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 20px;
  left: 60px;
  width: 300px;
  max-height: 500px;
  z-index: 99999999;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.debug-panel.collapsed {
  width: 40px;
  height: 40px;
  overflow: hidden;
}

.debug-panel-header {
  background-color: #dc6c6c;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  user-select: none;
}

.debug-panel.collapsed .debug-panel-header {
  justify-content: center;
}

.debug-panel.collapsed .debug-panel-header span {
  display: none;
}

.debug-panel-header i {
  transition: transform 0.3s ease;
}

.debug-panel.collapsed .debug-panel-header i {
  transform: rotate(180deg);
}

.debug-panel-content {
  padding: 15px;
  max-height: 450px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}

debug-panel pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', Courier, monospace;
}

/* Scrollbar styling */
.debug-panel-content::-webkit-scrollbar {
  width: 6px;
}

.debug-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.debug-panel-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.debug-panel-content::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
