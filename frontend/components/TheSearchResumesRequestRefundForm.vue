<template>
  <div class="modal fade" tabindex="-1" id="modal-view-request-refund-confirm">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header border-bottom mb-3 bg-light-dark gap-1">
          <div class="d-flex flex-column justify-content-between">
            <h4 class="modal-title">{{ translate('search_resumes_request_refund') }} </h4>
            <p class="fw-normal">{{ translate('search_resumes_request_refund_for_search_resume_profile') }} {{
              resumeDetail.fullname }} </p>
          </div>
          <button type="button" @click="onReset" class="btn-close" aria-label="Close" data-bs-dismiss="modal"></button>
        </div>

        <div class="h-50 modal-body d-flex flex-column gap-4 overflow-scroll">
          <div id="request-refund" class="w-100 d-flex justify-content-between align-items-start flex-column gap-4">
            <!-- Reason -->
            <div id="form-box" class="w-100">
              <textarea type="text" name="reason" rows="5" maxlength="200"
                class="form-control form-control-solid padding-textarea-note"
                :placeholder="translate('search_resumes_request_refund_reason_placeholder')"
                :value="inputValue"
                @keypress="onChangeHandler($event.target.value)"
                @change="onChangeHandler($event.target.value)"></textarea>
            </div>

            <!-- Notes -->
            <div id="note-box" class="d-flex flex-row justify-content-between align-items-start gap-4 rounded px-4 py-2">
              <div>
                <inline-svg src="/assets/icons/notes.svg"></inline-svg>
              </div>
              <div>
                <h5>{{ translate('search_resumes_request_refund_notes') }}</h5>
                <ul>
                  <li>{{ translate('search_resumes_request_refund_note_items_1') }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer d-flex justify-content-end align-items-center border-0">
          <button type="button" class="btn btn-sm btn-secondary" @click="onReset">
            {{ translate("swal_confirm_request_refund_cancel") }}
          </button>
          <button type="button" class="btn btn-sm btn-danger" @click="onRequestRefundHandler"
            :class="inputValue === '' ? 'disabled' : ''">
            {{ translate("search_resumes_request_refund_confrm") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { translate } from "@/helpers";

interface Props {
  resumeDetail: any;
  inputValue: string;
  onChangeHandler?: (value: string) => void;
  onReset: () => void;
  onRequestRefundHandler: () => void;
}

const props = defineProps<Props>();

</script>
