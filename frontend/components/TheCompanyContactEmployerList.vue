<template>
  <!--begin::Employer account container-->
  <section id="employer-account-container">
    <h2 class="mb-0">
      {{ translate("contact_information_employer_account") }}
    </h2>
    <span class="heading">
      {{
        `${translate("contact_information_total")} ${
          employerAccounts ? employerAccounts.length : 0
        }`
      }}</span
    >

    <table class="table table-row-bordered mt-3">
      <thead class="fw-bold">
        <tr>
          <th>{{ translate("contact_information_no") }}</th>
          <th>{{ translate("contact_information_display_name") }}</th>
          <th>{{ translate("contact_information_position") }}</th>
          <th>Email</th>
          <th>{{ translate("contact_information_phone") }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody
        v-if="
          !isLoadingEmployerAccounts &&
          employerAccounts &&
          employerAccounts.length > 0
        "
      >
        <tr v-for="(employer, index) in employerAccounts" :key="index">
          <td>{{ index + 1 }}</td>
          <td>{{ employer.full_name }}</td>
          <td>{{ employer.position }}</td>
          <td>{{ employer.email }}</td>
          <td>{{ employer.phone }}</td>
          <td>
            <div class="btn-group" v-if="employer.id == authStore.user.id">
              <button
                type="button"
                class="btn py-0"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <inline-svg src="/assets/icons/three-dots.svg" />
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <li>
                  <button
                    class="dropdown-item"
                    type="button"
                    @click="openAccountSettingModal"
                  >
                    {{ translate("contact_information_account_settings") }}
                  </button>
                </li>
              </ul>
            </div>
          </td>
        </tr>
      </tbody>

      <tbody v-else>
        <tr>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
        </tr>
        <tr>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
          <td>
            <Skeletor />
          </td>
        </tr>
      </tbody>
    </table>
  </section>
  <!--end::Employer account container-->
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { Modal } from "bootstrap";

import { getEmployerAccount } from "@/api/employer";
import { useAuthStore } from "@/stores";
import { translate } from "@/helpers";
import { Employer } from "@/models/employer";

/**
 * Store init
 */
const authStore = useAuthStore();

/**
 * Data define
 */
const employerAccounts = ref<Employer[]>();
const isLoadingEmployerAccounts = ref<boolean>(true);

const openAccountSettingModal = () => {
  Modal.getOrCreateInstance("#modal-account-settings").show();
};

const loadEmployerAccount = () => {
  isLoadingEmployerAccounts.value = true;

  /**
   * Get data employee
   */
  getEmployerAccount()
    .then((employerAccountsResponse) => {
      employerAccounts.value = employerAccountsResponse.data as Employer[];

      isLoadingEmployerAccounts.value = false;
    })
    .catch(() => {
      // TODO: handle error
    });
};

/**
 * Life cycle event
 */
onMounted(() => {
  loadEmployerAccount();
});

//Watch auth store
watch(
  () => authStore.user,
  () => {
    const employerIndex = employerAccounts.value.findIndex(
      (employer) => employer.id === authStore.user.id
    );
    employerAccounts.value[employerIndex] = {
      ...employerAccounts.value[employerIndex],
      position: authStore.user.position,
      full_name: authStore.user.full_name,
      phone: authStore.user.phone,
    };
  },
  { deep: true }
);
</script>
