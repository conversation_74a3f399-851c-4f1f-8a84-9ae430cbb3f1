<template>
  <div class="w-100 d-flex flex-column justify-content-between align-items-end gap-2">
    <div class="w-100 position-relative">
      <div class="">
        <textarea
            type="text"
            name="search"
            rows="1"
            class="form-control form-icon form-control-solid padding-textarea-note"
            :placeholder="translate('candidate_detail_add_note')"
            v-model="notesData.noteInputValue"
            @click="openForm"
        ></textarea>
      </div>

      <div class="position-absolute top-25 start-0 mx-3">
        <span class="svg-icon svg-icon-2 text-topdev-3">
          <inline-svg src="/assets/icons/candidates/note.svg" />
        </span>
      </div>
    </div>

    <div v-if="isOpenForm" class="w-100 d-flex flex-row justify-content-end align-items-center gap-4">
      <div v-if="notesData.isActionLoading"
          class="spinner-border text-primary w-20px h-20px"
      ></div>
      <div v-else class="w-100 d-flex flex-row justify-content-end align-items-center gap-2">
        <button
            class="btn btn-sm text-capitalize btn-default fw-bolder"
            type="button"
            @click="cancelHandler"
        >
          {{ (translate("search_resumes_cancel")).toLowerCase() }}
        </button>
        <button
            class="btn btn-sm text-capitalize btn-outline btn-outline-danger fw-bolder"
            type="button"
            v-if="!notesData.editId"
            @click="submitAddNote"
        >
          {{ (translate("search_resumes_add")).toLowerCase() }}
        </button>
        <button
            class="btn btn-sm text-capitalize btn-outline btn-outline-danger fw-bolder"
            type="button"
            @click="submitEditNote"
            v-else
        >
          {{ (translate("search_resumes_save").toLowerCase()) }}
        </button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">

import {translate} from "@/helpers";
import {NotesDataState} from "@/models/search-resumes";

interface Props {
  notesData: NotesDataState;
  submitAddNote?: () => void;
  submitEditNote?: () => void;
  openForm: () => void;
  isOpenForm: boolean;
  cancelHandler: () => void
}

const props = defineProps<Props>();

</script>

<style scoped>
.btn-outline-danger:hover {
    background-color: #DD3F24;
    border-color: #DD3F24;
}
.padding-textarea-note {
  padding-top: 0.8rem;
}
</style>
