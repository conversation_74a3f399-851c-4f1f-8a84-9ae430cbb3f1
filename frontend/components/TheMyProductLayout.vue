<template>
  <div class="container-fluid p-6 usage-history">
    <div class="d-flex flex-column page-content">
      <div class="d-flex justify-content-between align-items-end">
        <div>
          <h1 class="text-2xl m-0 p-0 heading">
            {{ translate("layout_my_product_management") }}
          </h1>
          <span class="description">{{ translate("usage_history_my_product_description") }}</span>
        </div>
        <div>
          <a :href="FRONTEND_PRODUCT_URL" target='_blank'
            class="btn btn-danger btn-md d-inline-flex gap-1 justify-center align-items-center">
            <inline-svg src="/assets/icons/plus-circle.svg" width="20" height="20" />
            <span>{{ translate("usage_history_get_more_products") }}</span>
          </a>
        </div>
      </div>
      <div class="d-flex flex-column tab">
        <div class="d-flex justify-content-between align-items-end border-bottom border-gray-500"
          :class="{ 'show-announcement-bar': layoutStore.isShowAnnouncementBar }">
          <AppTabs :navItems="MyProductMenuConfig" />
          <slot name="topbar"></slot>
        </div>

        <div class="tab-content">
          <!--begin::Main content-->
          <slot></slot>
          <!--end::Main content-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";

import AppTabs from "@/components/AppTabs.vue";

import { useLayoutStore } from "@/stores";
import { MyProductMenuConfig } from "@/config/menu";
import { translate } from "@/helpers";
import { FRONTEND_PRODUCT_URL } from "@/constants/usage-history";

const layoutStore = useLayoutStore();

onMounted(() => {
  layoutStore.setPageTitle("layout_my_product_management");
});
</script>
