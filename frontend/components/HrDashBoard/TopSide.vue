<template>
    <div class="flex items-center justify-between">
        <div class="flex w-fit items-center bg-[#F5F5F5] border-gray gap-1 p-2">
            <button class="px-6 py-[3px] " :class="{
                'text-[#BA321B] bg-[#FEE6E2] border-[0.5px] border-[#F98470] shadow-[0px_0px_4px_0px_#6C1B0D4D]': type === 'today',
                'bg-white border-[0.5px] border-white text-[#6D6D6D]': type !== 'today'
            }" @click="handleClick('today')">
                {{translate('hr_dashboard_today')}}
            </button>
            <button class="px-6 py-[3px] " :class="{
                'text-[#BA321B] bg-[#FEE6E2] border-[0.5px] border-[#F98470] shadow-[0px_0px_4px_0px_#6C1B0D4D]': type === 'last7',
                'bg-white border-[0.5px] border-white text-[#6D6D6D]': type !== 'last7'
            }" @click="handleClick('last7')">
                {{translate('hr_dashboard_last_seven_days')}}
            </button>
            <button class="px-6 py-[3px] " :class="{
                'text-[#BA321B] bg-[#FEE6E2] border-[0.5px] border-[#F98470] shadow-[0px_0px_4px_0px_#6C1B0D4D]': type === 'last30',
                'bg-white border-[0.5px] border-white text-[#6D6D6D]': type !== 'last30'
            }" @click="handleClick('last30')">
                {{translate('hr_dashboard_last_thirty_days')}}
            </button>
            <CustomCalendar>
                <template v-slot="{ openDatePicker }">
                    <button class="px-6 py-[3px] flex items-center gap-1" :class="{
                        'text-[#BA321B] bg-[#FEE6E2] border-[0.5px] border-[#F98470] shadow-[0px_0px_4px_0px_#6C1B0D4D]': type === 'custom',
                        'bg-white border-[0.5px] border-white text-[#3D7DED]': type !== 'custom'
                    }" @click="handleClickCustom(openDatePicker)">
                        <svg width="14" height="14" viewBox="0 0 14 14" :class="[
                            type === 'custom' ? 'text-[#BA321B]' : 'text-[#3D7DED]'
                        ]" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M0.333252 5.00004C0.333252 3.74271 0.333252 3.11471 0.723919 2.72404C1.11459 2.33337 1.74259 2.33337 2.99992 2.33337H10.9999C12.2573 2.33337 12.8853 2.33337 13.2759 2.72404C13.6666 3.11471 13.6666 3.74271 13.6666 5.00004C13.6666 5.31404 13.6666 5.47137 13.5693 5.56937C13.4713 5.66671 13.3133 5.66671 12.9999 5.66671H0.999919C0.685919 5.66671 0.528585 5.66671 0.430585 5.56937C0.333252 5.47137 0.333252 5.31337 0.333252 5.00004Z"
                                fill="currentColor" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M0.333252 11C0.333252 12.2573 0.333252 12.8853 0.723919 13.276C1.11459 13.6667 1.74259 13.6667 2.99992 13.6667H10.9999C12.2573 13.6667 12.8853 13.6667 13.2759 13.276C13.6666 12.8853 13.6666 12.2573 13.6666 11V7.66667C13.6666 7.35267 13.6666 7.19533 13.5693 7.09733C13.4713 7 13.3133 7 12.9999 7H0.999919C0.685919 7 0.528585 7 0.430585 7.09733C0.333252 7.19533 0.333252 7.35333 0.333252 7.66667V11ZM3.66659 9C3.66659 8.686 3.66659 8.52867 3.76392 8.43067C3.86192 8.33333 4.01992 8.33333 4.33325 8.33333H5.66659C5.98059 8.33333 6.13792 8.33333 6.23592 8.43067C6.33325 8.52867 6.33325 8.686 6.33325 9C6.33325 9.314 6.33325 9.47133 6.23592 9.56933C6.13792 9.66667 5.97992 9.66667 5.66659 9.66667H4.33325C4.01925 9.66667 3.86192 9.66667 3.76392 9.56933C3.66659 9.47133 3.66659 9.31333 3.66659 9ZM3.76392 11.0973C3.66659 11.1953 3.66659 11.3533 3.66659 11.6667C3.66659 11.98 3.66659 12.138 3.76392 12.236C3.86192 12.3333 4.01992 12.3333 4.33325 12.3333H5.66659C5.98059 12.3333 6.13792 12.3333 6.23592 12.236C6.33325 12.138 6.33325 11.98 6.33325 11.6667C6.33325 11.3533 6.33325 11.1953 6.23592 11.0973C6.13792 11 5.97992 11 5.66659 11H4.33325C4.01925 11 3.86192 11 3.76392 11.0973ZM7.66659 9C7.66659 8.686 7.66659 8.52867 7.76392 8.43067C7.86192 8.33333 8.01992 8.33333 8.33325 8.33333H9.66659C9.98059 8.33333 10.1379 8.33333 10.2359 8.43067C10.3333 8.52867 10.3333 8.686 10.3333 9C10.3333 9.314 10.3333 9.47133 10.2359 9.56933C10.1379 9.66667 9.97992 9.66667 9.66659 9.66667H8.33325C8.01925 9.66667 7.86192 9.66667 7.76392 9.56933C7.66659 9.47133 7.66659 9.31333 7.66659 9ZM7.76392 11.0973C7.66659 11.1953 7.66659 11.3533 7.66659 11.6667C7.66659 11.98 7.66659 12.138 7.76392 12.236C7.86192 12.3333 8.01925 12.3333 8.33325 12.3333H9.66659C9.98059 12.3333 10.1379 12.3333 10.2359 12.236C10.3333 12.138 10.3333 11.98 10.3333 11.6667C10.3333 11.3533 10.3333 11.1953 10.2359 11.0973C10.1379 11 9.97992 11 9.66659 11H8.33325C8.01925 11 7.86192 11 7.76392 11.0973Z"
                                fill="currentColor" />
                            <path d="M3.6665 1V3M10.3332 1V3" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" />
                        </svg>
                        {{translate('hr_dashboard_custom')}}
                    </button>

                </template>
            </CustomCalendar>
        </div>
    </div>
    <div class="item-filter w-[436px] px-4">
        <div class="custom-checkbox-filter checkbox-experiences">
            <Multiselect
                :placeholder="translate('candidate_list_job_title')" class="h-[48px]"
                :options="filteredOptions" :searchable="true" :hideSelected="false" :closeOnSelect="false"
                v-model.lazy="optionsChecked" mode="multiple"
                @searchChange="(query: string, select$: any) => fixWidthMultiselect(query, select$)"
                @change="onChangeFilterData">
                <template v-slot:multiplelabel="{ values }: ValuesType">
                    <div class="multiselect-multiple-label">
                        <span>
                            {{
                                values.length > 2
                                    ? `(+${values.length}) ${translate('hr_dashboard_job_titles')}`
                                    : values.map(({ title }) => title).join(", ")
                            }}</span>
                    </div>
                </template>
                <template #beforelist>
                    <div class="sticky z-[2] top-0 bg-white">
                        <button type="button" class="w-full hover:bg-[#f5f6f8] px-[12px] py-2 -mb-2" :class="{
                            'bg-[#f5f6f8]': checkedAll
                        }" @click="handleCheckAll">
                            <div class="custom-option">
                                <input type="checkbox" :checked="checkedAll">
                                <span style="margin-left: 4px" class="text-overflow-ellipsis">All Jobs</span>
                            </div>
                        </button>
                    </div>
                </template>
                <template v-slot:option="{ option }: { option: { label: string, value: string } }">
                    <div class="custom-option">
                        <input type="checkbox" :checked="optionsChecked.includes(option.value)">
                        <span style="margin-left: 4px" class="text-overflow-ellipsis">{{ option.label }}</span>
                    </div>
                </template>
                <template #afterlist>
                    <div class="sticky bottom-0 bg-white border-t border-gray-200 p-2 flex justify-end gap-6">
                        <button v-on:click="handleReset"
                            class="text-[#312525] w-[75px] text-[14px] uppercase font-semibold h-[36px] rounded-[2px] text-center">
                            Reset
                        </button>
                        <button v-on:click="handleApply"
                            class="bg-[#DD3F24] uppercase font-semibold text-[14px] text-white w-[123px] h-[36px] rounded-[2px] text-center">
                            Apply
                        </button>
                    </div>
                </template>
            </Multiselect>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getAllJobTitle } from "@/api/dashboard";
import CustomCalendar from '@/components/HrDashBoard/CustomCalendar.vue';
import { fixWidthMultiselect, formatDate, translate } from "@/helpers";
import { ValuesType } from "@/models/candidates";
import { useDashBoardStore } from "@/stores";
import Multiselect from "@vueform/multiselect";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";

const dashBoardStore = useDashBoardStore();

const loadingJobs = ref(false);
const checkedAll = ref(false);
const error = ref<string | null>(null);
const { type } = storeToRefs(dashBoardStore)
const options = ref([]);

const handleCheckAll = () => {
    checkedAll.value = !checkedAll.value;
    if (checkedAll.value) {
        optionsChecked.value = filteredOptions.value.map((option) => option.value);
    } else {
        optionsChecked.value = [];
    }
};

const onChangeFilterData = (value) => {
    if(checkedAll.value){
        checkedAll.value = false;
    }
    if(value.length === filteredOptions.value.length) {
        checkedAll.value = true;
    }
    optionsChecked.value = value;
}

const handleApply = () => {
    dashBoardStore.updateJobId(optionsChecked.value);
};
const handleReset = () => {
    optionsChecked.value = [];
    dashBoardStore.updateJobId([]);
};

const getDateDaysAgo = (days: number): string => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return formatDate(date);
};

const setActive = (value) => {
    dashBoardStore.updateTypeFilter(value);
};

const handleClickCustom = (openDatePicker) => {
    setActive("custom");
    if (openDatePicker) openDatePicker();
};
const handleClick = (type: string) => {
    const today = new Date();
    const endDate = formatDate(today);
    let startDate: string;

    switch (type) {
        case 'today':
            setActive('today');
            startDate = endDate;
            break;
        case 'last7':
            setActive('last7');
            startDate = getDateDaysAgo(7);
            break;
        case 'last30':
            setActive('last30');
            startDate = getDateDaysAgo(30);
            break;
        default:
            return;
    }

    dashBoardStore.updateRangeDate(startDate, endDate);
};

const filteredOptions = computed(() => {
    return options.value.filter(option => {
        return option.value
    }
    );
});
const loadJobTitle = async () => {
    try {
        loadingJobs.value = true;
        const response = await getAllJobTitle();
        options.value = response.data
        const listValueJobs = response.data.filter((option) => option.value).map((option) => option.value);
        optionsChecked.value = listValueJobs
        checkedAll.value = true;
        dashBoardStore.updateJobId(listValueJobs);

    } catch (err) {
        error.value = 'Failed to load premium jobs';
    } finally {
        loadingJobs.value = false;
    }
};
const optionsChecked = ref<string[]>([]);




onMounted(async () => {
    loadJobTitle();
});

</script>
