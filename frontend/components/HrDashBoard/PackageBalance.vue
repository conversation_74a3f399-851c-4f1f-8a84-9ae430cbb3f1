<template>
    <div class="bg-white px-5 py-6 w-1/2">
        <div><span class="font-semibold text-[#4F4F4F]">{{translate('hr_dashboard_package_balance')}}</span></div>
        <div v-if="!loading" v-for="(packageItem, index) in formattedStats" :key="index"
            class="border border-[#E3E3E3] py-3 px-8 rounded-lg mt-2">
            <div class="flex items-center gap-3">
                <span
                    class="inline-flex items-center justify-center w-[44px] h-[44px] font-medium rounded-full text-[20px]/[40px]"
                    :style="{ backgroundColor: packageItem.bgColor, color: packageItem.textColor }">
                    {{ packageItem.value }}
                </span>
                <div>
                    <span class="block text-[#4F4F4F] text-xs/[18px]">{{ packageItem.title }}</span>
                    <span class="block text-[#4F4F4F] font-semibold">{{ translate(packageItem.label) }}</span>
                </div>
            </div>
        </div>
        <div class="border border-[#E3E3E3] py-3 px-8 mt-2 rounded-lg flex flex-col gap-3" v-show="loading">
            <Skeletor v-for="i in 4" width="100%" height="60" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getPackageBalance } from '@/api/dashboard';
import { translate } from "@/helpers";
import { PackageItem } from '@/models/dashboard';
import { computed, onMounted, ref } from "vue";
import { Skeletor } from "vue-skeletor";
const loading = ref(false);
const error = ref<string | null>(null);
const packageBalance = ref<PackageItem[]>([]);

const loadPremiumJob = async () => {
    try {
        loading.value = true;
        const response = await getPackageBalance();
        packageBalance.value = response.data
    } catch (err) {
        error.value = 'Failed to load premium jobs';
        console.error(err);
    } finally {
        loading.value = false;
    }
};

const packagesMapping = {
    premium_jobs_expire_next_7_days: {
        label: 'hr_dashboard_expire_in_seven_days',
        title: 'Premium Job Post',
        bgColor: '#F0FDF5',
        textColor: '#15803C'
    },
    premium_jobs_remain: {
        label: 'hr_dashboard_all_remaining_post',
        title: 'Premium Job Post',
        bgColor: '#FEF4F2',
        textColor: '#DD3F24'
    },
    search_cv_credits_expire_next_7_days: {
        label: 'hr_dashboard_expire_in_seven_days',
        bgColor: '#EEF8FF',
        title: 'Search Candidate',
        textColor: '#0C5DE9'
    },
    search_cv_credits_remain: {
        label: 'hr_dashboard_all_remaining_unlock',
        title: 'Search Candidate',
        bgColor: '#FFF8EB',
        textColor: '#D98B06'
    },

};

const formattedStats = computed(() => {
    if (!packageBalance.value) return [];

    return Object.entries(packageBalance.value).map(([key, value]) => ({
        value: value ?? null,
        title: packagesMapping[key]?.title,
        label: packagesMapping[key]?.label,
        bgColor: packagesMapping[key]?.bgColor,
        textColor: packagesMapping[key]?.textColor
    }));
});

onMounted(async () => {
    loadPremiumJob();
});
</script>
