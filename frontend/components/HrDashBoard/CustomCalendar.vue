<template>
    <div class="date-range-picker">
        <v-date-picker is-range model-config="{ type: 'string', mask: 'YYYY-MM-DD' }" :masks="{ input: 'DD-MM-YYYY' }"
            v-model="from_to_raw" :columns="2" locale="vi" color="red" :popover="{ visibility: 'visible' }"
            ref="datePicker">

            <!-- Custom input slot -->
            <template v-slot="{ inputValue }">
                <slot :openDatePicker="openDatePicker" :dateRange="inputValue"></slot>
            </template>
            <template #footer>
                <div class="flex items-center justify-end gap-2 mx-5 py-4 border-top">
                    <span v-if="formattedDateRange" class="text-[#4F4F4F] text-xs font-semibold">
                        {{ formattedDateRange?.formattedDate }}
                    </span>
                    <button @click="cancel" class="px-3 py-1 text-xs bg-[#E7E7E7] rounded-sm">Huỷ</button>
                    <button @click="confirm"
                        class="px-3 py-1 text-xs font-semibold bg-[#DD3F24] text-white rounded-sm">Ok</button>
                </div>

            </template>

        </v-date-picker>
    </div>
</template>

<script setup>
import { useDashBoardStore } from '@/stores';
import { computed, ref } from "vue";
import { formatDate } from '@/helpers'
import { format } from "date-fns";
import { storeToRefs } from "pinia";

const datePicker = ref(null);
const dashBoardStore = useDashBoardStore();

const { from_to_raw } = storeToRefs(dashBoardStore)

// Function to manually open the date picker
const openDatePicker = () => {
    datePicker.value?.showPopover();
};

// Computed property to correctly format the selected date range
const formattedDateRange = computed(() => {
    if (!from_to_raw.value.start || !from_to_raw.value.end) return ""; // Hide when no date is selected

    // Convert to Date objects (handles ISO format)
    const startDate = new Date(from_to_raw.value.start);
    const endDate = new Date(from_to_raw.value.end);

    if (isNaN(startDate) || isNaN(endDate)) return ""; // Prevent invalid dates

    return {
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        formattedDate: `${format(startDate, "dd/MM/yyyy")} - ${format(endDate, "dd/MM/yyyy")}`,
    };
});


// Confirm and cancel actions
const confirm = () => {
    dashBoardStore.updateRangeDate(formattedDateRange?.value?.startDate, formattedDateRange?.value?.endDate);
    datePicker.value.hidePopover();
};

const cancel = () => {
    datePicker.value.hidePopover();
};

</script>


<style scoped>
.date-range-picker {
    max-width: 400px;
    margin: auto;
}

.border-top {
    border-top: 1px solid #B0B0B0;
}

/* Change the background color of the selected range */
.vc-highlight-base {
    background-color: #FEF4F2 !important;
    /* Custom orange */
}

/* Change the text color inside the selected range */
.vc-highlight-content {
    color: white !important;
}
</style>