<template>
    <div class="bg-white px-5 py-6 w-1/2">
        <div><span class="font-semibold">{{translate('hr_dashboard_all_premium_jobs_by_status')}} </span></div>
        <div v-if="!loading" v-for="(job, index) in jobStatuses" :key="index"
            class="border border-[#E3E3E3] py-3 px-8 mt-2 rounded-lg">
            <div class="flex items-center gap-3">
                <span
                    class="inline-flex items-center justify-center w-[44px] font-medium h-[44px] rounded-full text-[20px]/[40px]"
                    :style="{ backgroundColor: job.bgColor, color: job.textColor }">
                    {{ job.total }}
                </span>
                <div>
                    <span class="block text-[#4F4F4F] text-xs/[18px]">Job</span>
                    <span class="block text-[#4F4F4F] font-semibold capitalize">{{ translate(job.label) }}</span>
                </div>
            </div>
        </div>
        <div class="border border-[#E3E3E3] py-3 px-8 mt-2 rounded-lg flex flex-col gap-3" v-show="loading">
            <Skeletor v-for="i in 4" width="100%" height="60" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getPremiumJobsByStatus } from '@/api/dashboard';
import { translate } from "@/helpers";
import { JobStatuses } from '@/models/dashboard';
import { onMounted, ref } from 'vue';
import { Skeletor } from "vue-skeletor";
const loading = ref(false);
const error = ref<string | null>(null);
const jobStatuses = ref<JobStatuses[]>([]);

const loadPremiumJob = async () => {
    try {
        loading.value = true;
        const response = await getPremiumJobsByStatus();
        jobStatuses.value = response.data.map((x) => {
            if (x?.status) {
                switch (x?.status) {
                    case "open":
                        return { ...x, label:'hr_dashboard_open', bgColor: "#F0FDF5", textColor: "#15803C" };
                    case "closed":
                        return { ...x, label:'hr_dashboard_closed', bgColor: "#FEF4F2", textColor: "#DD3F24" };
                    case "review":
                        return { ...x, label:'hr_dashboard_review', bgColor: "#EEF8FF", textColor: "#0C5DE9" };
                    case "draft":
                        return { ...x, label:'hr_dashboard_draft', bgColor: "#FFF8EB", textColor: "#D98B06" };
                }
            }
            else return x;
        });
    } catch (err) {
        error.value = 'Failed to load premium jobs';
        console.error(err);
    } finally {
        loading.value = false;
    }
};


onMounted(async () => {
    loadPremiumJob();
});
</script>
