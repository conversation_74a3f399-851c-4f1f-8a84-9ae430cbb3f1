<template>
  <!-- Candidate skills -->
  <div v-if="candidate.skills.length > 0" class="d-flex flex-wrap align-items-center">
    <div class="skill-title flex flex-column gap-2">
      <p class="m-0 font-bold container-title">
        Skill(s):
      </p>
      <p v-if="candidate.yoe > 0 && candidate.cv_profile_experiences.length < 1" class="m-0 font-bold container-title">
        <span class="bg-gray py-1 px-2 rounded">{{ candidate.yoe }} YOE</span>
      </p>
    </div>
    <div class="flex-1 overflow-hidden">
      <swiper
        v-if="candidate.skills"
        :spaceBetween="5"
        :slidesPerView="'auto'"
        class="tag-skill-container mw-550px"
      >
        <swiper-slide
          class="tag"
          v-for="(skill, index) in candidate.skills"
          :key="index"
        >{{ skill }}</swiper-slide
        >
      </swiper>
    </div>
  </div>

  <!-- Candidate experience and candidate education -->
  <div
    class="space-y-2 box_search_resumes_work_experience_education"
    :key="candidate.id"
  >
    <!-- Candidate experience -->
    <div
      class="flex align-items-start"
      v-show="
        candidate.cv_profile_experiences &&
        candidate.cv_profile_experiences.length > 0 &&
        candidate.cv_profile_experiences.filter((item) => item.position)
          .length > 0
      "
    >
      <div class="skill-title flex flex-column gap-2">
        <p class="m-0 font-bold container-title">
          {{ translate("search_resumes_work_experience") }}
        </p>
        <p v-if="candidate.yoe > 0" class="m-0 font-bold container-title"><span class="bg-gray py-1 px-2 rounded">{{ candidate.yoe }} YOE</span></p>
      </div>
      <ul class="flex-1 candidate-experience-education-list">
        <li
          v-for="(experience, index) in candidate.cv_profile_experiences"
          :key="index"
        >
          <div class="flex mb-1">
            <span
              class="experience-time management text-gray-400"
              v-show="experience.from || experience.to"
            >
              {{ experience.from }} - {{ experience.to ?? translate("search_resumes_present") }}
            </span>
            <span
              class="space-line management"
              v-show="
                (experience.position || experience.company) &&
                (experience.from || experience.to)
              "
            ></span>
            <span
              class="text-gray-600 font-bold"
              v-show="
                experience.position || experience.company
              "
            >
              {{ experience.position }} -
              {{ experience.company }}
            </span>
          </div>
        </li>
      </ul>
    </div>

    <!-- Candidate education -->
    <div
      class="flex align-items-start mt-2"
      v-show="
        candidate.cv_profile_educations && candidate.cv_profile_educations.length > 0
      "
    >
      <p class="m-0 font-bold container-title">
        {{ translate("search_resumes_education") }}
      </p>
      <ul class="candidate-experience-education-list">
        <li
          v-for="(education, index) in candidate.cv_profile_educations"
          :key="index"
        >
          <div class="flex mb-1">
            <span
              class="experience-time management text-gray-400"
              v-show="education.from"
            >
              {{ education.from }} - {{ education.to ?? translate("search_resumes_present") }}
            </span>
            <span
              class="space-line management"
              v-show="
                (education.from || education.to) &&
                education.school_name
              "
            ></span>
            <span
              class="text-gray-600 font-bold"
              v-show="education.school_name"
            >
              {{ education.school_name }}
            </span>
          </div>
        </li>
      </ul>
    </div>
    <!-- View less information -->
    <button
      @click="onViewLess(candidate.id)"
      :class="`btn btn-sm btn-view-less btn-view-less-${candidate.id} d-none`"
      :title="translate('search_resumes_view_less')"
    >
      {{ translate("search_resumes_view_less") }}
      <inline-svg
        src="assets/icons/arrow-up.svg"
        width="16"
        height="16"
        alt="arrow down"
      />
    </button>
    <!-- See more information -->
    <button
      @click="onSeeMore(candidate.id)"
      :class="`btn btn-sm btn-see-more btn-${candidate.id}`"
      :title="translate('search_resumes_see_more')"
    >
      {{ translate("search_resumes_see_more") }}
      <inline-svg
        src="assets/icons/arrow-down.svg"
        width="16"
        height="16"
        alt="arrow down"
      />
    </button>

  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { translate } from "@/helpers";
import { Candidate } from "@/models/candidates";

const { candidate } = defineProps<{
  candidate: Candidate;
}>();

const onSeeMore = (id: number) => {
  let btn = document.querySelector(`.btn-${id}`);
  let btnViewLess = document.querySelector(`.btn-view-less-${id}`);
  btn.classList.add("d-none");
  btn.parentElement.classList.add("no-fix");
  btn.parentElement.classList.remove("fix");
  btnViewLess.classList.remove("d-none");
};
const onViewLess = (id: number) => {
  let btnViewLess = document.querySelector(`.btn-view-less-${id}`);
  let btn = document.querySelector(`.btn-${id}`);
  btnViewLess.classList.add("d-none");
  btn.parentElement.classList.remove("no-fix");
  btn.parentElement.classList.add("fix");
  btn.classList.remove("d-none");
};
</script>
