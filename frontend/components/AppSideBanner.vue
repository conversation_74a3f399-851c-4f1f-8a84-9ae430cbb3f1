<template>
  <div
    v-if="canOpenBannerModal"
    id="block-side-banner"
    class="text-center mt-4 cursor-pointer position-relative"
  >
    <div @click="onOpenJSibeBannerModal" class="this-side-banner">
      <img
        v-if="loadding && dataModal.image !== ''"
        :alt="dataModal.title"
        class="maxw-245px"
        :src="dataModal.image"
      />
      <div v-if="!loadding" class="py-5">
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
        <Skeletor />
      </div>
    </div>
    <img
      alt="Logo"
      class="this-icon-mobile-side-banner mx-auto d-none"
      src="/assets/icons/aside/topdev.svg"
    />
  </div>
</template>

<script lang="ts" setup>
import { Modal } from "bootstrap";
import { computed, ref, watch } from "vue";
import { useLayoutStore } from "@/stores";
const layoutStore = useLayoutStore();

//Define data
const dataModal = ref({ title: "", image: "" });
const loadding = ref(false);

//Life cycle
watch(
  () => layoutStore.dataBanner,
  (dataBanner) => {
    const dataBannerNew = {
      ...dataBanner,
      ...{ image: dataBanner.image },
    };
    dataModal.value = dataBannerNew;
    loadding.value = true;
  },
  { deep: true }
);

//Function
const onOpenJSibeBannerModal = () => {
  if (!loadding) return;
  Modal.getOrCreateInstance("#side-banner-modal").show();
};

const canOpenBannerModal = computed(
  () => !!dataModal.value && !!dataModal.value.title && !!dataModal.value.image
);
</script>
