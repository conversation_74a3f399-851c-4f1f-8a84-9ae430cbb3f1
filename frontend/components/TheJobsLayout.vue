<template>
<!--  <div
    class="d-flex justify-content-between align-items-end border-bottom border-gray-500 bg-topdev-4 topbar-container container-fluid"
    :class="{ 'show-announcement-bar': layoutStore.isShowAnnouncementBar }"
  >
    <AppTabs :navItems="ManageJobMenuConfig" />
    <slot name="topbar"></slot>
  </div>-->
  <div class="company-profile-container container-fluid pt-4">
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import AppTabs from "@/components/AppTabs.vue";

import { useLayoutStore } from "@/stores";
import { ManageJobMenuConfig } from "@/config/menu";

const layoutStore = useLayoutStore();
layoutStore.setPageTitle("layout_manage_jobs");
</script>
