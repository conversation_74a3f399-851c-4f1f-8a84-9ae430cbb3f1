<template>
    <div @click="onSortColumnClick" class="d-flex btn-sort gap-1 align-items-center justify-content-start">
        <span>
            {{ translate(title) }}
        </span>
        <div class="d-flex flex-column align-items-center justify-content-center">
            <span class="sort-icon" :class="sortDirection === 'asc' ? 'active' : ''
                ">
                <inline-svg src="/assets/icons/arrow-up.svg" width="8" height="8" />
            </span>
            <span class="sort-icon" :class="sortDirection === 'desc' ? 'active' : ''
                ">
                <inline-svg src="/assets/icons/arrow-down.svg" width="8" height="8" />
            </span>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { translate } from "@/helpers";
import { SortType } from "@/models/usage-history";
import { ref } from "vue";

const props = defineProps<{
    title: string
    sort: string
    orderBy?: 'asc' | 'desc' | null
}>();

const sortDirection = ref<'asc' | 'desc' | null>(props.orderBy);

type Emit = {
    (e: 'onSort', value: SortType)
}

const emit = defineEmits<Emit>();

const onSortColumnClick = () => {
    sortDirection.value = sortDirection.value != 'asc' ? 'asc' : 'desc';
    emit('onSort', { field: props.sort, direction: sortDirection.value });
}

</script>