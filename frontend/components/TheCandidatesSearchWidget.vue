<template>
  <!-- Topbar -->
  <p
    v-if="isAllCandidates"
    class="text-end fs-8"
    v-html="translate('candidate_list_best_match_status_describe')"
  ></p>

  <div
    class="d-flex justify-content-between align-items-center mb-2 w-100 gap-2"
  >
    <!-- Search Input  -->
    <div class="position-relative flex-grow-1">
      <input
        type="text"
        name="search"
        class="form-control form-control-solid rounded form-control-lg"
        autocomplete="off"
        :placeholder="translate('candidate_list_search_placeholder')"
        :class="{ 'form-icon': isAllCandidates }"
        v-model="search"
        @keyup.enter="onSearch"
      />

      <div
        class="position-absolute translate-middle-y top-1/2 start-0 mx-3"
        v-if="isAllCandidates"
      >
        <span class="svg-icon svg-icon-2 text-topdev-3">
          <inline-svg src="/assets/icons/search.svg" />
        </span>
      </div>
    </div>
    <!-- Best match-->
      <button
        v-if="isAllCandidates"
        type="button"
        class="btn-topdev-2 btn-md d-flex align-items-center btn-outline btn-best-match"
        :class="{
          active: candidatesStore.candidatesParams.filter.matching_status === 1,
        }"
        @click="filterMatchingStatus"
      >
        <span>
          <i>
            <inline-svg
              width="16"
              height="16"
              src="assets/icons/candidates/sparkles.svg"
            />
          </i>

          {{
            translate("candidate_show_best_matched")
          }}
        </span>
      </button>

    <!-- Action  -->
    <!-- Open Filter Modal Button -->
    <button
      type="button"
      class="svg-icon svg-icon-2 text-topdev-3 position-relative btn-filter "
      :class="{ active: isCandidatesFilterChange, 'd-none': isDetail }"
      @click="onOpenFilterModal"
    >
      <inline-svg src="/assets/icons/filter.svg" />
    </button>
    <!-- Search button -->
    <button type="button" class="btn btn-md btn-danger" @click="onSearch">
      <span v-if="isAllCandidates">{{
          translate("candidate_list_search")
        }}</span>
      <i v-else class="fa fa-search fs-6"></i>
    </button>
    <!-- Download Button  -->
    <button
      v-if="isAllCandidates"
      type="button"
      class="btn btn-md btn-success"
      @click="onDownloadButtonClick"
    >
      <span
        v-if="isDownloadBtnLoading"
        class="spinner-border spinner-border-sm align-self-center"
        role="status"
        aria-hidden="true"
      ></span>
      {{ translate("candidate_list_download") }}
    </button>
    <!-- End Action  -->
  </div>

  <!-- Filter bar  -->
  <div
    :class="[
      isAllCandidates
        ? 'justify-content-start'
        : 'detail-candidate justify-content-between',
    ]"
    class="container-filter d-flex align-items-start mb-3 w-100"
  >
    <!-- Show filter index -->
    <div
      v-if="isAllCandidates"
      class="container-filter-box d-flex align-items-center flex-wrap gap-2"
      id="container-filter-box"
    >
      <!-- Select  -->

      <!-- Job title  -->
      <div class="item-filter filter-select" :class="{ active: !!filterData.job_id }">
        <Multiselect
          :placeholder="translate('candidate_list_job_title')"
          :options="candidatesStore.candidatesTaxonomies.jobTitles"
          v-model="filterData.job_id"
          mode="single"
          autocomplete="off"
          :searchable="true"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
          @change="onChangeFilterData"
          class="job-id"
        >
          <template
            v-slot:singlelabel="{ value }: { value: OptionTitle }"
          >
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("candidate_list_job_title") }}:
                {{ `${value.title}(#${value.value})` }}
              </span>
            </div>
          </template>
          <template v-slot:option="{ option }: { option : OptionTitle }">
            {{ option.value ? `${option.title}(#${option.value} - ${translate('job_list_publised_date')} ${option.published_at})` : translate('candidate_list_all_jobs') }}
          </template>
        </Multiselect>
      </div>
      <!-- Job location  -->
      <div class="item-filter" :class="{ active: !!filterData.location_id }">
        <Multiselect
          :placeholder="translate('candidate_list_candidate_location')"
          :options="candidatesStore.candidatesTaxonomies.candidatesProvince"
          v-model="filterData.location_id"
          mode="single"
          autocomplete="off"
          :searchable="true"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
          @change="onChangeFilterData"
        >
          <template
            v-slot:singlelabel="{ value }: { value: { label: string } }"
          >
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("candidate_list_candidate_location") }}:
                {{ value.label }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- Skills -->
      <div
        class="item-filter custom-checkbox-filter checkbox-skills"
        :class="{
          active: filterData.skills_id.length > 0,
        }"
      >
        <Multiselect
          :placeholder="translate('candidate_list_skills')"
          :options="taxonomiesStore.skills"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          v-model.lazy="filterData.skills_id"
          mode="multiple"
          @change="onChangeFilterData"
        >
          <template v-slot:multiplelabel="{ values }: ValuesType">
            <div class="multiselect-multiple-label">
              <span
              >{{ translate("candidate_list_skills") }}:
                {{
                  values.length > 2
                    ? `(+${values.length})`
                    : values.map(({ value, label }) => label).join(", ")
                }}</span
              >
            </div>
          </template>

          <template v-slot:option="{ option }: { option: { label: string, value: string } }">
            <div class="custom-option">
              <input type="checkbox" :checked="filterData.skills_id.includes(option.value)">
              <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- YOE -->
      <div
        class="item-filter custom-checkbox-filter checkbox-experiences"
        :class="{
          active: filterData.experience_ids.length > 0,
        }"
      >
        <Multiselect
          :placeholder="translate('candidate_list_experiences')"
          :options="candidatesStore.candidatesTaxonomies.experiences"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          v-model.lazy="filterData.experience_ids"
          mode="multiple"
          @change="onChangeFilterData"
        >
          <template v-slot:multiplelabel="{ values }: ValuesType">
            <div class="multiselect-multiple-label">
              <span
              >{{ translate("candidate_list_experiences") }}:
                {{
                  values.length > 2
                    ? `(+${values.length})`
                    : values.map(({ value, label }) => translate(label)).join(", ")
                }}</span
              >
            </div>
          </template>

          <template v-slot:option="{ option }: { option: { label: string, value: number } }">
            <div class="custom-option">
              <input type="checkbox" :checked="filterData.experience_ids.includes(option.value)">
              <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- Procedure Status -->
      <div
        class="item-filter"
        :class="{
          active: !!filterData.procedure_status,
        }"
      >
        <Multiselect
          :placeholder="translate('candidate_list_filter_procedure_status')"
          :options="candidatesStore.candidatesTaxonomies.procedureStatus"
          v-model="filterData.procedure_status"
          @change="onChangeFilterData"
        >
          <template
            v-slot:singlelabel="{ value }: { value: { label: string } }"
          >
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("candidate_list_filter_procedure_status") }}:
                {{ translate(value.label) }}
              </span>
            </div>
          </template>
          <template v-slot:option="{ option }: { option: { label: string } }">
            <span class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
          </template>
        </Multiselect>
      </div>
      <!-- Applied date -->
      <div
        class="item-filter"
        :class="{
          active: !!filterData.timeRange.start,
        }"
      >
        <v-date-picker
          v-model="filterData.timeRange"
          is-range
          :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
          :masks="{ input: 'DD-MM-YYYY' }"
          @update:modelValue="onChangeFilterData"
        >
          <template v-slot="{ inputValue, inputEvents }">
            <div class="d-flex justify-center align-items-center">
              <input
                :value="inputValue.start"
                v-on="inputEvents.start"
                class="form-control form-control-solid rounded px-1 py-1 rounded w-100"
                :placeholder="translate('candidate_list_form_date')"
              />
              <input
                :value="inputValue.end"
                v-on="inputEvents.end"
                class="form-control form-control-solid rounded px-1 py-1 rounded w-100 ms-1"
                :placeholder="translate('candidate_list_to_date')"
              />
            </div>
          </template>
        </v-date-picker>
      </div>
    </div>

    <!-- Show filter detail candidates -->
    <div
      v-else-if="!isAllCandidates && isCandidatesFilterChange"
      class="container-filter-box d-flex align-items-center"
      id="container-filter-box"
    >

      <swiper :spaceBetween="6" :slidesPerView="'auto'">
        <!-- Job title  -->
        <swiper-slide class="w-auto">
          <div class="item-filter" :class="{ active: !!filterData.job_id }">
            <Multiselect
              :options="candidatesStore.candidatesTaxonomies.jobTitles"
              v-model="filterData.job_id"
              mode="single"
              autocomplete="off"
              :searchable="true"
              :placeholder="translate('candidate_list_job_title')"
              class="job-id"
            >
              <template
                v-slot:singlelabel="{ value }: { value: OptionTitle }"
              >
                <div class="multiselect-multiple-label">
                  <span>
                    {{ translate("candidate_list_job_title") }}:
                    {{ `${value.title}(#${value.value})` }}
                  </span>
                </div>
              </template>
              <template v-slot:option="{ option }: { option : OptionTitle }">
                {{ option.value ? `${option.title}(#${option.value} - ${translate('job_list_publised_date')} ${option.published_at})` : translate('candidate_list_all_jobs') }}
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Job location  -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{ active: !!filterData.location_id }"
          >
            <Multiselect
              :placeholder="translate('candidate_list_candidate_location')"
              :options="candidatesStore.candidatesTaxonomies.candidatesProvince"
              v-model="filterData.location_id"
              mode="single"
              autocomplete="off"
              :searchable="true"
            >
              <template
                v-slot:singlelabel="{ value }: { value: { label: string } }"
              >
                <div class="multiselect-multiple-label">
                  <span>
                    {{ translate("candidate_list_candidate_location") }}:
                    {{ value.label }}
                  </span>
                </div>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Skills -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: filterData.skills_id.length > 0,
            }"
          >
            <Multiselect
              :placeholder="translate('candidate_list_skills')"
              :options="taxonomiesStore.skills"
              :searchable="true"
              :hideSelected="false"
              :closeOnSelect="false"
              autocomplete="off"
              v-model.lazy="filterData.skills_id"
              mode="multiple"
            >
              <template v-slot:multiplelabel="{ values }: ValuesType">
                <div class="multiselect-multiple-label">
                  <span
                  >{{ translate("candidate_list_skills") }}:
                    {{
                      values.length > 2
                        ? `(+${values.length})`
                        : values.map(({ value, label }) => label).join(", ")
                    }}</span
                  >
                </div>
              </template>

              <template v-slot:option="{ option }: OptionType">
                <span class="text-overflow-ellipsis">{{ option.label }}</span>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Procedure Status -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: !!filterData.procedure_status,
            }"
          >
            <Multiselect
              :placeholder="translate('candidate_list_filter_procedure_status')"
              :options="candidatesStore.candidatesTaxonomies.procedureStatus"
              v-model="filterData.procedure_status"
              mode="single"
              autocomplete="off"
              :searchable="true"
            >
              <template
                v-slot:singlelabel="{ value }: { value: { label: string } }"
              >
                <div class="multiselect-multiple-label">
                  <span>
                    {{ translate("candidate_list_filter_procedure_status") }}:
                    {{ translate(value.label) }}
                  </span>
                </div>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- YOE -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: filterData.experience_ids.length > 0,
            }"
          >
            <Multiselect
              :placeholder="translate('candidate_list_experiences')"
              :options="candidatesStore.candidatesTaxonomies.experiences"
              :searchable="true"
              :hideSelected="false"
              :closeOnSelect="false"
              autocomplete="off"
              v-model.lazy="filterData.experience_ids"
              mode="multiple"
            >
              <template v-slot:option="{ option }: { option: { label: string } }">
                <span class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
              </template>
              <template v-slot:multiplelabel="{ values }: ValuesType">
                <div class="multiselect-multiple-label">
                      <span>
                        {{ translate("candidate_list_experiences") }}:
                        {{
                          values.length > 2
                            ? `(+${values.length})`
                            : values.map(({ value, label }) => translate(label)).join(", ")
                        }}
                      </span>
                </div>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Applied date -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: !!filterData.timeRange.start,
            }"
          >
            <v-date-picker
              v-model="filterData.timeRange"
              is-range
              :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
              :masks="{ input: 'DD-MM-YYYY' }"
            >
              <template v-slot="{ inputValue, inputEvents }">
                <div class="d-flex justify-center align-items-center">
                  <input
                    :value="inputValue.start"
                    v-on="inputEvents.start"
                    class="form-control form-control-solid rounded px-1 py-1 rounded w-100"
                    :placeholder="translate('candidate_list_form_date')"
                  />
                  <input
                    :value="inputValue.end"
                    v-on="inputEvents.end"
                    class="form-control form-control-solid rounded px-1 py-1 rounded w-100 ms-1"
                    :placeholder="translate('candidate_list_to_date')"
                  />
                </div>
              </template>
            </v-date-picker>
          </div>
        </swiper-slide>
      </swiper>
    </div>
    <!-- Filter reset button -->
    <span
      v-if="isCandidatesFilterChange"
      class="btn-reset svg-icon text-topdev-3 d-flex align-items-center ms-4 p-3 mt-0"
      @click="onFilterReset"
    >
      <inline-svg class="me-1" src="/assets/icons/cancel.svg" />
      {{ translate("candidate_list_filter_reset") }}
    </span>
  </div>
  <!-- End filter bar  -->

  <!-- Filter modal  -->
  <TheCandidatesModalFilter
    :filterData="filterData"
    @applied="onModalFilterApply"
    @reset="onFilterReset"
  />
  <!-- End filter modal  -->
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, toRaw, computed } from "vue";
import { Modal } from "bootstrap";
import _ from "lodash";
import { Swiper, SwiperSlide } from "swiper/vue";
import Multiselect from "@vueform/multiselect";
import { ValuesType, OptionType, OptionTitle } from "@/models/candidates";

import TheCandidatesModalFilter from "@/components/TheCandidatesModalFilter.vue";

import { exportCandidates } from "@/api/candidate";
import {
  useCandidatesStore,
  useTaxonomiesStore,
  candidatesParamsDefine
} from "@/stores";
import { CandidatesFilterParams } from "@/models/candidates";
import { showWarningToast, translate, fixWidthMultiselect } from "@/helpers";
import { InlineSvg } from "@/plugins";
import { useRoute, useRouter } from "vue-router";

//Interface
interface Props {
  isAllCandidates: boolean;
  isDetail: boolean;
}


const props = defineProps<Props>();

const router = useRouter();
const route = useRoute();

//Define store
const candidatesStore = useCandidatesStore();
const taxonomiesStore = useTaxonomiesStore();

//Define data
const isDownloadBtnLoading = ref(false);
const search = ref();
const filterData = reactive<CandidatesFilterParams>({
  job_id: null,
  application_status: null,
  procedure_status: null,
  location_id: "",
  skills_id: [],
  experience_ids: [],
  timeRange: {
    start: "",
    end: ""
  },
  matching_status: 1
});

//Function
const onOpenFilterModal = () => {
  Modal.getOrCreateInstance("#filter-modal").show();
};

const filterMatchingStatus = () => {
  let matching_status = candidatesStore.candidatesParams.filter.matching_status;

  // Use a switch statement with break statements to ensure proper flow
  switch (matching_status) {
    case 1:
      matching_status = null;
      break;
    case 0:
      matching_status = 1;
      break;
    case null:
    case undefined: // Optional: handle undefined if there's a chance it could be encountered
      matching_status = 1;
      break;
    default:
      matching_status = 1; // Default case if no match found
  }

  filterData.matching_status = matching_status;

  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, matching_status: matching_status },
    page: 1
  });
  addQueryTab(matching_status)

};
const addQueryTab = (status: number) => {
  let tab: string | null;
  switch (status) {
    case 1:
      tab = 'bestMatch';
      break;
    case 0:
      tab = 'other';
      break;
    case null:
      tab = 'all';
      break;
    default:
      tab = 'bestMatch';
  }
  const { path, query } = route;
  router.replace({
    path,
    query: { ...query, tab: tab }
  });
}

const onModalFilterApply = (data: CandidatesFilterParams) => {
  filterData.job_id = data.job_id;
  filterData.location_id = data.location_id;
  filterData.skills_id = data.skills_id;
  filterData.procedure_status = data.procedure_status;
  filterData.experience_ids = data.experience_ids;
  filterData.timeRange = data.timeRange;
  filterData.matching_status = data.matching_status;
  addQueryTab(data.matching_status)
  setCandidateParams();
};

const setCandidateParams = () => {
  if (filterData.timeRange === null) {
    filterData.timeRange = candidatesParamsDefine.filter.timeRange;
  }
  candidatesStore.setCandidatesParams({ filter: toRaw(filterData), page: 1 });
}
const onChangeFilterData = () => {
  setCandidateParams();
}

const onFilterReset = () => {
  candidatesStore.resetCandidatesParams();

  filterData.job_id = candidatesStore.candidatesParams.filter.job_id;
  filterData.location_id = candidatesStore.candidatesParams.filter.location_id;
  filterData.skills_id = candidatesStore.candidatesParams.filter.skills_id;
  filterData.procedure_status =
    candidatesStore.candidatesParams.filter.procedure_status;
  filterData.experience_ids =
    candidatesStore.candidatesParams.filter.experience_ids;
  filterData.timeRange = candidatesStore.candidatesParams.filter.timeRange;
  filterData.matching_status = candidatesStore.candidatesParams.filter.matching_status;
};

const onSearch = () => {
  candidatesStore.setCandidatesParams({ query: search.value, page: 1 });
};

const onDownloadButtonClick = () => {
  const dataFilter = {
    ...filterData,
    ...{
      query: search.value,
      matching_status: candidatesStore.candidatesParams.filter.matching_status,
      applied_date_from: filterData.timeRange.start,
      applied_date_to: filterData.timeRange.end,
      experience_ids: filterData.experience_ids.join(','),
      skills_id: filterData.skills_id.join(','),
    }
  };
  isDownloadBtnLoading.value = true;
  exportCandidates({ search: dataFilter })
    .then((response) => {
      const downloadUrl = response.data.download_url;
      window.location.href = downloadUrl;
    })
    .catch((error) => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
      throw error;
    })
    .finally(() => {
      isDownloadBtnLoading.value = false;
    });
};

const isCandidatesFilterChange = computed(
  () =>
  {
    // Create copies of the filters without the `matching_status` property
    const filter1 = _.omit(candidatesParamsDefine.filter, 'matching_status');
    const filter2 = _.omit(toRaw(candidatesStore.candidatesParams.filter), 'matching_status');

    // Compare the modified filters
    return !_.isEqual(filter1, filter2);
  }
);

//Life cycle
onMounted(async () => {
  //set search data
  search.value = candidatesStore.candidatesParams.query;

  //Set filter data from store -> This for case user navigate from manage jobs by click at applications row
  filterData.job_id = candidatesStore.candidatesParams.filter.job_id;
  filterData.procedure_status =
    candidatesStore.candidatesParams.filter.procedure_status;
  filterData.experience_ids =
    candidatesStore.candidatesParams.filter.experience_ids;
  filterData.location_id = candidatesStore.candidatesParams.filter.location_id;
  filterData.skills_id = candidatesStore.candidatesParams.filter.skills_id;
  filterData.timeRange = candidatesStore.candidatesParams.filter.timeRange;
  filterData.matching_status = candidatesStore.candidatesParams.filter.matching_status;

  //Get taxonomies
  candidatesStore.getCandidatesTaxonomies();
  taxonomiesStore.getTaxonomies();
});

</script>
