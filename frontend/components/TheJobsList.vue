<template>
  <!-- List Jobs -->
  <div class="list-jobs bg-white">
    <div class="table-responsive">
      <table class="table position-relative">
        <thead>
          <tr class="fw-bold fs-6 text-gray-800 border-bottom border-top">
            <th>ID</th>
            <th>{{ translate("job_list_job_post") }}</th>
            <th>{{ translate("job_list_publised_date") }}</th>
            <th class="text-center">{{ translate("job_list_status") }}</th>
            <th class="text-center">
              {{ translate("job_list_applications") }}
            </th>
            <th class="text-center">
              {{ translate("job_best_match") }}
            </th>
            <th class="text-center">{{ translate("job_list_action") }}</th>
          </tr>
        </thead>
        <tbody v-if="jobs.length > 0" id="list-jobs">
          <AppLoader />
          <tr class="border-bottom" v-for="(job, index) in jobs" :key="index">
            <td>{{ job.id }}</td>
            <!-- Job post  -->
            <td>
              <a
                :href="job.detail_url"
                target="_blank"
                class="fw-bold text-primary"
                >{{ job.name }}</a
              >
              <p class="my-1">
                <b>{{ translate("job_list_location") }}</b
                ><span>{{ ` ${job.location}` }}</span>
              </p>
              <p class="my-1">
                <b>{{ translate("job_list_salary") }}</b
                ><span>{{ ` ${formatSalary(job.salary)}` }}</span>
              </p>
              <swiper
                :spaceBetween="5"
                :slidesPerView="'auto'"
                class="tag-skill-container mw-450px"
              >
                <swiper-slide
                  v-for="(skill, index) in job.skills"
                  :key="index"
                  class="tag"
                  >{{ skill }}
                </swiper-slide>
              </swiper>
            </td>
            <!-- Published date  -->
            <td>
              <p
                class="my-1 fw-bold"
                v-show="job.status === 'Open' || job.status === 'Closed'"
              >
                {{ job.published_date }}
              </p>
              <p class="my-1" v-show="job.status === 'Review'">
                {{ translate("job_list_your post in review") }}
              </p>
              <p class="my-1" v-show="job.status === 'Open' && job.expires_at">
                {{
                  !!job.expires_in
                    ? job.expires_in === 1
                      ? translate("job_list_expires_one_day")
                      : translate("job_list_expires_some_days", [
                          job.expires_in,
                        ])
                    : ""
                }}
              </p>
              <p class="my-1" v-show="job.status === 'Closed'">
                {{ translate("job_list_expired") }}
              </p>

              <p class="my-1 fw-bold"></p>
              <p
                class="my-1"
                v-show="job.status === 'Open' || job.status === 'Closed'"
              >
                <b>{{ translate("job_list_views") }}</b
                >{{ ` ${job.views_count}` }}
              </p>
              <p class="my-1 text-nowrap">
                <b>{{ `${translate("job_list_created_by")}: ` }}</b
                >{{ ` ${job.created_by}` }}
              </p>
            </td>
            <!-- Status  -->
            <td class="text-center">
              <button
                type="button"
                class="btn-topdev-filter"
                :class="{
                  open: job.status === 'Open',
                  review: job.status === 'Review',
                  closed: job.status === 'Closed',
                }"
              >
                {{ job.status }}
                <span v-if="job.status == 'Open' && job.is_48h">*</span>
              </button>
            </td>
            <!-- Applications -->
            <td class="text-center">
              <a
                @click="
                  toCandidatesWithJobFilter(job.application_count, job.id)
                "
                class="cursor"
                :class="[
                  !!job.application_count ? 'text-primary' : 'text-black',
                ]"
                >{{ job.application_count }}</a
              >
            </td>
            <!-- Best Match   -->
            <td class="text-center fw-bold" :class="{ 'text-green-600': job.best_match_count }">
              <template v-if="job.best_match_count">
                <p class="cursor" @click="listCandidateBestMatch(job.id)">
                  {{ job.best_match_count }}
                </p>
              </template>
              <template v-else>
                -
              </template>
            </td>
            <!-- Action  -->
            <td
              class="d-flex flex-row align-items-start justify-content-center border-bottom-0"
            >
              <div class="d-flex flex-column">
                <div>
                  <!-- Edit button with tooltip -->
                  <div
                    class="tooltip-wrapper disabled"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    :title="translate('job_list_cant_edit_message')"
                    v-if="
                      checkDisabledEditButton(job.status, job.published_date)
                    "
                  >
                    <button
                      type="button"
                      class="btn-list-action primary"
                      disabled
                    >
                      {{ translate("job_list_edit") }}
                    </button>
                  </div>
                  <!-- Edit button without tooltip  -->
                  <button
                    v-else
                    type="button"
                    class="btn-list-action primary"
                    @click="editJob(job.id)"
                    :disabled="job.status === 'Closed'"
                  >
                    {{ translate("job_list_edit") }}
                  </button>
                </div>
                <button
                  type="button"
                  class="btn-list-action"
                  data-bs-target="#job-detail-modal"
                  @click="openJobDetailModal(job.id)"
                >
                  {{ translate("job_list_view") }}
                </button>
              </div>
              <div class="btn-group dropstart px-0">
                <button
                  type="button"
                  class="btn text-secondary py-0 pe-0 mt-1"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <inline-svg src="/assets/icons/dots-vertical.svg" />
                </button>
                <ul class="dropdown-menu">
                  <button
                    type="button"
                    class="btn-duplicate-job py-2 text-nowrap"
                    @click="duplicateJob(job.id)"
                  >
                    {{ translate("job_list_dupplicate job post") }}
                  </button>
                </ul>
              </div>
            </td>
          </tr>
        </tbody>

        <!-- If jobs empty -->
        <tbody id="list-jobs" v-else>
          <AppLoader />
          <tr>
            <td colspan="6" class="text-center">
              <inline-svg
                src="/assets/icons/empty-file.svg"
                class="empty-icon"
              ></inline-svg>
              <p style="font-size: 16px">
                {{ translate("contact_information_you_havent_post_job_yet") }}
                <router-link
                  :to="{ name: 'post-job' }"
                  class="text-topdev-1 fw-bold mx-1"
                  @click="onSetAddStatus"
                  >{{ translate("job_list_post_job") }}
                </router-link>
                {{ translate("contact_information_now") }}
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- End Table  -->
    <!-- Detail Job Modal  -->
    <TheJobsModalDetail :job="job" />
    <!-- End Detail Job Modal  -->
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { Modal } from "bootstrap";
import { Swiper, SwiperSlide } from "swiper/vue";

import AppLoader from "@/components/AppLoader.vue";
import TheJobsModalDetail from "@/components/TheJobsModalDetail.vue";

import { fetchJobDetail } from "@/api/job";
import { useJobStore, useCandidatesStore } from "@/stores";

import { Jobs } from "@/models/jobs";
import { formatSalary, showWarningToast, translate } from "@/helpers";
import router from "@/router";

//Define props
interface Props {
  jobs: Jobs[];
}

defineProps<Props>();

//Define store
const jobStore = useJobStore();
const candidatesStore = useCandidatesStore();

//Define data
const route = useRouter();
const job = ref();

//Function
const onSetAddStatus = () => {
  jobStore.setIDAndStatus(undefined, "add");
};

const editJob = (id: number) => {
  route.push({ name: "edit-job", params: { id } });
};

const duplicateJob = (id: number) => {
  jobStore.setIDAndStatus(id, "duplicate");
  route.push({ name: "post-job" });
};

const greaterThan15Days = (date: any) => {
  if (!!date) {
    const fifteenDaysInMs = 15 * 24 * 60 * 60 * 1000;
    const timestampFifteenDaysAgo = new Date().getTime() - fifteenDaysInMs;
    const publishDate = new Date(date.split("-").reverse().join("-")).getTime();

    if (timestampFifteenDaysAgo > publishDate) return true;
    return false;
  }
  return false;
};

const checkDisabledEditButton = (status: string, date) => {
  if (status === "Open") return greaterThan15Days(date);
  return false;
};

const toCandidatesWithJobFilter = (applicationCount, jobId) => {
  if (!applicationCount) return;

  route.push({ name: "candidates" });
  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, job_id: jobId, matching_status: null },
  });
};

const openJobDetailModal = async (id: number) => {
  try {
    const { data } = await fetchJobDetail(id);
    job.value = data;
    Modal.getOrCreateInstance("#jobDetailModal").show();
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  }
};

const listCandidateBestMatch = (jobId: number) => {

  route.push({ name: "candidates" });
  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, job_id: jobId },
  });

}
</script>
