<template>
  <div v-if="hasFreeJobOpened" class="blockui" style="height: 100vh; width: 100%; z-index: 999; position: fixed;"><div class="blockui-overlay"></div></div>

  <div class="col-12" v-if="availablePackages.length < 1">
    <div class="alert free-job-alert flex justify-content-between">
      <span v-html="translate('job_form_free_available_packages', {
        freeITJobOpening: freeITJobOpening,
        freeNonITJobOpening: freeNonITJobOpening
      })" />
      <a href="https://topdev.vn/ManualEmployerDashboard.pdf" target="_blank" class="btn btn-link">
        {{ translate("job_form_see_how_to_add_package") }}
      </a>
    </div>
  </div>
  <!-- End Package selection -->

  <div class="col-12">
    <div class="row">
      <div class="col-9">
        <div class="d-flex flex-column gap-4">
          <!-- Package selection -->
          <div v-if="availablePackages.length > 0"
            class="bg-white p-4 rounded-2"
          >
            <JobPackageSelection
              v-model="packageValue"
              :available-packages="availablePackages"
              :disabled="isPackageSelectionDisabled"
              :disabled-packages="disabledPackages"
              :is-free-job-disabled="isPackageSelectionDisabled"
              :hide-free-job-option="!props.isCreate && props.jobData?.level === 'paid'"
              :show-simple-free-job-view="!props.isCreate && props.jobData?.level === 'free'"
              @package-change="handlePackageChange"
            />
          </div>

          <!-- Basic information -->
          <div class="bg-white p-4 rounded-2">
            <h2>Basic Information</h2>

            <!-- Input job title -->
            <div class="form-group">
              <label for="title" class="required form-label mb-0">{{
                translate("job_form_title")
              }}</label>
              <input
                type="text"
                class="form-control form-control-solid"
                placeholder="Enter title job that you want to hire"
                name="title"
                :class="{ 'is-invalid': titleMeta.touched && titleError }"
                v-model="title"
                @blur="handleTitleBlur"
              />
              <AppErrorMessage name="title" />
            </div>
            <!-- ./Input job title -->

            <div class="form-group">

              <div class="row">
                <!-- Select Category -->
                <div class="col-6">
                  <div class="form-group">
                    <label for="category_id" class="required form-label mb-0">Job Category</label>
                    <Multiselect
                      placeholder="Select category"
                      :class="{
                        'is-invalid': categoryMeta.touched && categoryError,
                      }"
                      :options="mapJobCategories"
                      v-model="categoryId"
                      @update:modelValue="clearSelectedRoles"
                    />
                    <AppErrorMessage name="category_id" />
                  </div>
                </div>
                <!-- ./Select Category -->

                <!-- Select role -->
                <div class="col-6">
                  <div class="form-group">
                    <label for="job_category_id" class="required form-label mb-0">Role</label>
                    <Multiselect
                      placeholder="
                            Select the role
                          "
                      :class="{
                        'is-invalid': roleMeta.touched && roleError,
                      }"
                      :options="filterJobRoles"
                      @blur="handleRoleBlur"
                      :searchable="true"
                      :hideSelected="false"
                      :closeOnSelect="false"
                      mode="tags"
                      v-model="jobCategoryId"
                    >
                      <template #tag="{ option, handleTagRemove, disabled: isDisabled }: { option: { label: string, value: any }, handleTagRemove: (option: any, event: Event) => void, disabled: boolean }">
                        <div
                          class="multiselect-tag is-user"
                          :class="{ 'is-disabled': isDisabled }"
                        >
                          <span class="address-tag-content">{{ option.label }}</span>
                          <span
                            v-if="!isDisabled"
                            class="multiselect-tag-remove"
                            @click="(e: Event) => handleTagRemove(option, e)"
                          >
                            <span class="multiselect-tag-remove-icon"></span>
                          </span>
                        </div>
                      </template>
                    </Multiselect>
                    <AppErrorMessage name="job_category_id" />
                  </div>
                </div>
                <!-- ./Select role -->
              </div>
            </div>

            <!-- Select Skills -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Skills</label
              >
              <p class="heading lh-sm mb-2">
                Select job skills (maximum 5 skills)
              </p>
              <Multiselect
                placeholder="
                      Search skills
                    "
                :class="{
                  'is-invalid': skillsIdsMeta.touched && skillsIdsError,
                }"
                :options="taxonomiesStore.skills"
                :searchable="true"
                :hideSelected="false"
                :closeOnSelect="false"
                mode="multiple"
                v-model="skillsIds"
              >
                <template v-slot:multiplelabel="{ values }: any">
                  <div class="multiselect-multiple-label">
                    <span>
                      {{
                        values
                          .map(({ value, label }) => translate(label))
                          .join(", ")
                      }}
                    </span>
                  </div>
                </template>
              </Multiselect>
              <AppErrorMessage name="skills_ids" />
            </div>
            <!-- ./Select Skills -->

            <!-- Checkbox Level -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Level</label
              >
              <div
                class="d-flex gap-2 justify-content-between"
                :class="{
                  'is-invalid': jobLevelsMeta.touched && jobLevelsError,
                }"
              >
                <div
                  class="form-check form-check-custom form-check-solid form-check-sm"
                  v-for="level in taxonomiesStore.jobLevels"
                >
                  <input
                    class="form-check-input"
                    type="checkbox"
                    :value="level.value"
                    :id="`check-joblevel-${level.value}`"
                    v-model="jobLevels"
                  />
                  <label
                    class="form-check-label"
                    :for="`check-joblevel-${level.value}`"
                  >
                    {{ level.label }}
                  </label>
                </div>
              </div>
              <AppErrorMessage name="job_levels" />
            </div>
            <!-- ./Checkbox Level -->

            <!-- Select YOE -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Year of experience</label
              >
              <p class="heading lh-sm mb-2">
                Only the minimum year of experience will be displayed on TopDev
                website
              </p>
              <div class="row">
                <div class="col-6">
                  <div class="form-group">
                    <label for="titleInput" class="required form-label mb-0"
                      >From</label
                    >
                    <Multiselect
                      placeholder="
                            Min
                          "
                      :class="{
                        'is-invalid':
                          experienceFromMeta.touched && experienceFromError,
                      }"
                      :options="taxonomiesStore.experiences"
                      v-model="experienceFromValue"
                      @blur="handleExperienceFromBlur"
                    />
                    <AppErrorMessage name="experiences_ids.from" />
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-group">
                    <label for="titleInput" class="form-label mb-0">To</label>
                    <Multiselect
                      placeholder="
                            Max
                          "
                      :class="{
                        'is-invalid': 0,
                      }"
                      :options="taxonomiesStore.experiences"
                      v-model="experienceToValue"
                    />
                    <AppErrorMessage name="experiences_ids.to" />
                  </div>
                </div>
              </div>
            </div>
            <!-- ./Select YOE -->

            <!-- Check Job type -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Job type</label
              >
              <div
                class="d-flex gap-2"
                :class="{
                  'is-invalid': jobTypesMeta.touched && jobTypesError,
                }"
              >
                <div
                  class="form-check form-check-custom form-check-solid form-check-sm"
                  v-for="jobType in taxonomiesStore.jobTypes"
                >
                  <input
                    class="form-check-input"
                    type="checkbox"
                    :value="jobType.value"
                    :id="`checkbox-jobtype-${jobType.value}`"
                    v-model="jobTypes"
                    @blur="jobTypesBlur"
                  />
                  <label
                    class="form-check-label"
                    :for="`checkbox-jobtype-${jobType.value}`"
                  >
                    {{ jobType.label }}
                  </label>
                </div>
              </div>
              <AppErrorMessage name="job_types" />
            </div>
            <!-- ./Check Job type -->

            <!-- Check Contract type -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Contract type</label
              >
              <div
                class="d-flex gap-2"
                :class="{
                  'is-invalid': contractTypeMeta.touched && contractTypeError,
                }"
              >
                <div
                  class="form-check form-check-custom form-check-solid form-check-sm"
                  v-for="contractType in taxonomiesStore.contractTypes"
                >
                  <input
                    class="form-check-input"
                    type="checkbox"
                    :value="contractType.value"
                    :id="`checkbox-contracttype-${contractType.value}`"
                    v-model="contractTypeValue"
                    @blur="contractTypeBlur"
                  />
                  <label
                    class="form-check-label"
                    :for="`checkbox-contracttype-${contractType.value}`"
                  >
                    {{ contractType.label }}
                  </label>
                </div>
              </div>
              <AppErrorMessage name="contract_type" />
            </div>
            <!-- ./Check Contract type -->

            <!-- Select working location -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Working Location</label
              >
              <Multiselect
                placeholder="
                      Select the address that has been added to Company profile
                    "
                :class="{
                  'is-invalid': addressesIdMeta.touched && addressesIdError,
                }"
                :options="companyAddresses"
                v-model="addressesIdValue"
                @blur="addressesIdBlur"
                mode="tags"
                :hideSelected="false"
                :closeOnSelect="false"
              >
                <template v-slot:tag="{ option, handleTagRemove, disabled }">
                  <div
                    class="multiselect-tag is-user"
                    :class="{
                      'is-disabled': disabled,
                    }"
                  >
                    <span class="address-tag-content">{{ option.label }}</span>
                    <span
                      v-if="!disabled"
                      class="multiselect-tag-remove"
                      @click="handleTagRemove(option, $event)"
                    >
                      <span class="multiselect-tag-remove-icon"></span>
                    </span>
                  </div>
                </template>
              </Multiselect>
              <AppErrorMessage name="addresses_id" />
            </div>
            <!-- ./Select working location -->
          </div>
          <!-- ./Basic information -->

          <!-- Job detail -->
          <div class="bg-white p-4 rounded-2">
            <h2>Job Detail</h2>

            <!-- Job description -->
            <div class="form-group">
              <label for="titleInput" class="form-label mb-0"
                >Job description</label
              >
              <AppTextEditor
                name="content"
                :height="155"
                :initValue="contentValue"
                @update:initValue="(value) => setContentValue(value)"
              />
              <span :class="{ 'is-invalid': contentError }"></span>
              <AppErrorMessage name="content" />
            </div>
            <!-- ./Job description -->

            <!-- Responsibilities -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Responsibilities</label
              >
              <draggable
                :class="{
                  'is-invalid': true,
                }"
                v-model="responsibilitiesList"
                item-key="key"
                @end="onDragEnd"
              >
                <template #item="{ element, index }">
                  <li
                    class="d-flex justify-content-between align-items-center gap-3 mb-2 selected-item-parent"
                  >
                    <div class="grab">
                      <inline-svg src="/assets/icons/drag-drop-2.svg" />
                    </div>
                    <div class="position-relative flex-grow-1 gap-2 selected-item">
                      <textarea
                        :data-ref="`responsibilityTextarea_${index}`"
                        class="form-control form-control-solid border-0"
                        :placeholder="
                          translate('job_form_enter_your_responsibilities')
                        "
                        name="title"
                        v-model="element.description"
                        rows="1"
                        @input="autoGrow($event.target)"
                        @blur="updateResponsibilityField(index, element)"
                        @keydown="preventEnter"
                      ></textarea>
                      <button
                        type="button"
                        class="btn btn-sm hover-svg-primary position-absolute end-1 top-3"
                        @click="focusResponsibilityTextarea(index)"
                      >
                        <inline-svg
                          class="selected-item-icon"
                          src="/assets/icons/pen.svg"
                        />
                      </button>
                    </div>
                    <div class="d-flex justify-content-between">
                      <button
                        @click="removeResponsibilityField(index)"
                        type="button"
                        class="btn btn-sm hover-svg-primary btn-trash-item"
                      >
                        <inline-svg
                          class="selected-item-icon"
                          src="/assets/icons/trash.svg"
                        />
                      </button>
                    </div>
                  </li>
                </template>
              </draggable>
              <AppErrorMessage name="responsibilities" />
              <div class="input-taxonomies-select-container mt-2" v-click-outside="closeResponsibilityDropdown">
                <div class="select-container position-relative">
                  <input
                    type="text"
                    class="form-control form-control-solid"
                    :placeholder="translate('job_form_input_your_responsibilities')"
                    v-model="responsibilitySearchKeyword"
                    @keydown="handleResponsibilityKeydown"
                    @focus="showResponsibilityDropdown = true"
                  />
                  <div
                    v-if="showResponsibilityDropdown && initialResponsibilities.length > 0"
                    class="search-results-dropdown tags-dropdown"
                  >
                    <div class="tags-container">
                      <div class="suggested-tags">
                        <span class="text-muted">{{ translate('job_form_input_suggest_tag_input') }}:</span>
                        <span
                          v-for="item in initialResponsibilities"
                          :key="item.id"
                          @click="selectResponsibility(item)"
                          class="tag-item"
                          :class="{ 'selected': isResponsibilitySelected(item.id) }"
                        >
                          {{ item.text_en }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="plus-container">
                  <button
                    class="btn-plus"
                    type="button"
                    @click="addCustomResponsibility"
                  >
                    <img src="/assets/icons/plus-circle.svg" />
                  </button>
                </div>
              </div>
            </div>
            <!-- ./Responsibilities -->

            <!-- Requirements -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Requirements</label
              >
              <draggable
                :class="{
                  'is-invalid': true,
                }"
                v-model="requirementsList"
                item-key="key"
                @end="onDragEnd"
              >
                <template #item="{ element, index }">
                  <li
                    class="d-flex justify-content-between align-items-center gap-3 mb-2 selected-item-parent"
                  >
                    <div class="grab">
                      <inline-svg src="/assets/icons/drag-drop-2.svg" />
                    </div>
                    <div class="flex-grow-1 gap-2 selected-item position-relative">
                      <textarea
                        :data-ref="`requirementTextarea_${index}`"
                        class="form-control form-control-solid border-0"
                        :placeholder="
                          translate('job_form_enter_your_requirements')
                        "
                        name="title"
                        v-model="element.description"
                        rows="1"
                        @input="autoGrow($event.target)"
                        @blur="updateRequirementField(index, element)"
                        @keydown="preventEnter"
                      ></textarea>
                      <button
                        type="button"
                        class="btn btn-sm hover-svg-primary position-absolute end-1 top-3"
                        @click="focusRequirementTextarea(index)"
                      >
                        <inline-svg
                          class="selected-item-icon"
                          src="/assets/icons/pen.svg"
                        />
                      </button>
                    </div>
                    <div class="d-flex justify-content-between">
                      <button
                        @click="removeRequirementsField(index)"
                        type="button"
                        class="btn btn-sm hover-svg-primary btn-trash-item"
                      >
                        <inline-svg
                          class="selected-item-icon"
                          src="/assets/icons/trash.svg"
                        />
                      </button>
                    </div>
                  </li>
                </template>
              </draggable>
              <AppErrorMessage name="requirements" />
              <div class="input-taxonomies-select-container mt-2"
                   v-click-outside="closeRequirementDropdown">
                <div class="select-container position-relative">
                  <input
                    type="text"
                    class="form-control form-control-solid"
                    :placeholder="translate('job_form_input_your_requirements')"
                    v-model="requirementSearchKeyword"
                    @keydown="handleRequirementKeydown"
                    @focus="showRequirementDropdown = true"
                  />
                  <div
                    v-if="showRequirementDropdown && initialRequirements.length > 0"
                    class="search-results-dropdown tags-dropdown"
                  >
                    <div class="tags-container">
                      <div class="suggested-tags">
                        <span class="text-muted">{{ translate('job_form_input_suggest_tag_input') }}:</span>
                        <span
                          v-for="item in initialRequirements"
                          :key="item.id"
                          @click="selectRequirement(item)"
                          class="tag-item"
                          :class="{ 'selected': isRequirementSelected(item.id) }"
                        >
                          {{ item.text_en }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="plus-container">
                  <button
                    class="btn-plus"
                    type="button"
                    @click="addCustomRequirement"
                  >
                    <img src="/assets/icons/plus-circle.svg" />
                  </button>
                </div>
              </div>
            </div>
            <!-- ./Requirements -->

            <!-- Recruitment process -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Recruitment process</label
              >
              <draggable
                :class="{
                  'is-invalid': true,
                }"
                v-model="recruimentProcessList"
                item-key="key"
              >
                <template #item="{ element, index }">
                  <li
                    class="d-flex justify-content-between align-items-center gap-4 mb-2"
                  >
                    <div class="grab">
                      <inline-svg src="/assets/icons/drag-drop-2.svg" />
                    </div>
                    <div class="item-text">Round {{ index + 1 }}</div>
                    <div class="flex-grow-1">
                      <input
                        type="text"
                        class="form-control form-control-solid"
                        :placeholder="
                          translate('job_form_enter_your_recruitment_process')
                        "
                        name="title"
                        v-model="element.description"
                        @blur="updateRecruitmentProcessField(index, element)"
                      />
                    </div>
                    <div class="d-flex justify-content-between">
                      <button
                        @click="removerecruimentProcessField(index)"
                        type="button"
                        class="btn btn-sm hover-svg-primary p-0"
                      >
                        <inline-svg
                          src="/assets/icons/trash.svg"
                          class="ms-3"
                        />
                      </button>
                    </div>
                  </li>
                </template>
                >
              </draggable>
              <AppErrorMessage name="recruiment_process" />
              <div class="input-taxonomies-select-container mt-2">
                <div class="select-container">
                  <Multiselect
                    :placeholder="
                      translate('job_form_select_your_recruitment_process')
                    "
                    :class="{
                      'is-invalid': 0,
                    }"
                    :options="taxonomiesStore.recruitmentProcesses"
                    v-model="recruitmentProcessSelectedValue"
                    @update:modelValue="addRecruitmentProcess"
                    :searchable="true"
                  />
                </div>
                <div class="plus-container">
                  <button
                    class="btn-plus"
                    type="button"
                    @click="addRecruitmentProcess"
                  >
                    <img src="/assets/icons/plus-circle.svg" />
                  </button>
                </div>
              </div>
            </div>
            <!-- ./Recruitment process -->
          </div>
          <!-- ./Job detail -->

          <!-- Education -->
          <div class="bg-white p-4 rounded-2">
            <h2>Education</h2>

            <!-- Degree -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Degree</label
              >
              <div class="d-flex gap-2">
                <div
                  class="form-check form-check-custom form-check-solid form-check-sm"
                  v-for="education in taxonomiesStore.education"
                >
                  <input
                    class="form-check-input"
                    type="checkbox"
                    :value="education.value"
                    :id="`checkbox-education-${education.value}`"
                    v-model="educationDegreeValue"
                  />
                  <label
                    class="form-check-label"
                    :for="`checkbox-education-${education.value}`"
                  >
                    {{ education.label }}
                  </label>
                </div>
              </div>
            </div>
            <!-- ./Degree -->

            <!-- Major -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Major</label
              >
              <Multiselect
                placeholder="
                      Select the address that has been added to Company profile
                    "
                :class="{
                  'is-invalid': 0,
                }"
                :options="taxonomiesStore.educationMajor"
                v-model="educationMajorValue"
                @blur="educationMajorBlur"
                mode="tags"
                :hideSelected="false"
                :closeOnSelect="false"
                :searchable="true"
              >
                <template v-slot:tag="{ option, handleTagRemove, disabled }">
                  <div
                    class="multiselect-tag is-user"
                    :class="{
                      'is-disabled': disabled,
                    }"
                  >
                    <span class="address-tag-content">{{ option.label }}</span>
                    <span
                      v-if="!disabled"
                      class="multiselect-tag-remove"
                      @click="handleTagRemove(option, $event)"
                    >
                      <span class="multiselect-tag-remove-icon"></span>
                    </span>
                  </div>
                </template>
              </Multiselect>
              <AppErrorMessage name="education_major" />
            </div>
            <!-- ./Major -->

            <!-- Certification -->
            <div class="form-group">
              <label for="titleInput" class="form-label mb-0"
                >Certification</label
              >
              <input
                type="text"
                class="form-control form-control-solid"
                :placeholder="translate('job_form_enter_your_job_title')"
                name="title"
                v-model="educationCertificateValue"
                @blur="educationCertificateBlur"
                :class="{
                  'is-invalid':
                    educationCertificateMeta.touched &&
                    educationCertificateError,
                }"
              />
              <AppErrorMessage name="education_certificate" />
            </div>
            <!-- ./Certification -->
          </div>
          <!-- ./Education -->

          <!-- Benefit -->
          <div class="bg-white p-4 rounded-2">
            <h2>Benefit</h2>

            <!-- Salary -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Salary</label
              >
              <div class="row">
                <!-- Salary -->
                <div class="col-4" v-if="!salaryValue.is_negotiable">
                  <div class="form-group">
                    <label for="titleInput" class="required form-label mb-0"
                      >From</label
                    >
                    <AppCurrencyInput
                      v-model.lazy="minSalaryValue"
                      :options="{ currency: salaryValue.currency }"
                      :isInvalid="
                        salaryMeta.touched &&
                        (!!minSalaryError || !!maxSalaryError)
                      "
                      :disabled="salaryValue.is_negotiable === 1"
                    />
                  </div>
                </div>
                <div class="col-4" v-if="!salaryValue.is_negotiable">
                  <div class="form-group">
                    <label for="titleInput" class="required form-label mb-0"
                      >to</label
                    >
                    <AppCurrencyInput
                      v-model.lazy="maxSalaryValue"
                      :options="{ currency: salaryValue.currency }"
                      :isInvalid="
                        salaryMeta.touched &&
                        (!!minSalaryError || !!maxSalaryError)
                      "
                      :disabled="salaryValue.is_negotiable === 1"
                    />
                  </div>
                </div>
                <!-- End salary -->

                <!-- Salary estimate -->
                <div class="col-4" v-if="salaryValue.is_negotiable">
                  <div class="form-group">
                    <label for="titleInput" class="required form-label mb-0"
                      >Estimate from</label
                    >
                    <AppCurrencyInput
                      v-model.lazy="minEstimateSalaryValue"
                      :options="{ currency: salaryValue.currency }"
                      :isInvalid="
                        salaryMeta.touched &&
                        (!!minEstimateSalaryError || !!maxEstimateSalaryError)
                      "
                    />
                  </div>
                </div>
                <div class="col-4" v-if="salaryValue.is_negotiable">
                  <div class="form-group">
                    <label for="titleInput" class="required form-label mb-0"
                      >Estimate to</label
                    >
                    <AppCurrencyInput
                      v-model.lazy="maxEstimateSalaryValue"
                      :options="{ currency: salaryValue.currency }"
                      :isInvalid="
                        salaryMeta.touched &&
                        (!!minEstimateSalaryError || !!maxEstimateSalaryError)
                      "
                    />
                  </div>
                </div>
                <!-- End salary estimate -->

                <div class="col-4">
                  <label>&nbsp;</label>
                  <div class="rounded-2 button-group-switch">
                    <button
                      id="btn-usd"
                      type="button"
                      @click="onChangeCurrency('USD')"
                      :class="{
                        'bg-white': salaryValue.currency == 'USD',
                      }"
                    >
                      USD
                    </button>
                    <button
                      id="btn-vnd"
                      type="button"
                      @click="onChangeCurrency('VND')"
                      :class="{
                        'bg-white': salaryValue.currency == 'VND',
                      }"
                    >
                      VND
                    </button>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <div v-if="!salaryValue.is_negotiable">
                    <div
                      :class="{
                        'is-invalid': salaryMeta.touched,
                      }"
                    ></div>
                    <AppErrorMessage v-if="minSalaryError" name="salary.min" />
                    <AppErrorMessage
                      v-else-if="maxSalaryError"
                      name="salary.max"
                    />
                  </div>

                  <div v-if="!salaryValue.is_negotiable">
                    <div
                      :class="{
                        'is-invalid': salaryMeta.touched,
                      }"
                    ></div>
                    <AppErrorMessage
                      v-if="minEstimateSalaryError"
                      name="salary.min_estimate"
                    />
                    <AppErrorMessage
                      v-else-if="maxEstimateSalaryError"
                      name="salary.max_estimate"
                    />
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <div
                    class="form-check form-check-custom form-check-solid form-check-sm"
                  >
                    <input
                      class="form-check-input"
                      type="checkbox"
                      value="1"
                      v-model="salaryValue.is_negotiable"
                      id="checkbox-salaryIsNegotiableValue"
                    />
                    <label
                      class="form-check-label"
                      for="checkbox-salaryIsNegotiableValue"
                    >
                      Negotiable
                    </label>
                  </div>
                  <span class="ml-5 size-3">
                    Choose this option if you don't want to show salary on
                    TopDev site
                  </span>
                </div>
              </div>
            </div>
            <!-- ./Salary -->

            <!-- Benefits -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Benefits</label
              >
              <draggable v-model="benefitsList" item-key="key">
                <template #item="{ element, index }">
                  <li
                    class="d-flex justify-content-between align-items-center gap-4 mb-2"
                  >
                    <div class="grab">
                      <inline-svg src="/assets/icons/drag-drop-2.svg" />
                    </div>
                    <div class="item-text">
                      {{ element.name }}
                    </div>
                    <div class="flex-grow-1">
                      <input
                        type="text"
                        class="form-control form-control-solid"
                        :placeholder="
                          translate('job_form_enter_your_job_title')
                        "
                        name="title"
                        v-model="element.description"
                        @blur="updateBenefitField(index, element)"
                      />
                    </div>
                    <div class="d-flex justify-content-between">
                      <button
                        @click="removebenefitsField(index)"
                        type="button"
                        class="btn btn-sm hover-svg-primary p-0"
                      >
                        <inline-svg
                          src="/assets/icons/trash.svg"
                          class="ms-3"
                        />
                      </button>
                    </div>
                  </li>
                </template>
                >
              </draggable>
              <div class="input-taxonomies-select-container mt-2">
                <div class="select-container">
                  <Multiselect
                    placeholder="
                      Select the address that has been added to Company profile
                    "
                    :class="{
                      'is-invalid': 0,
                    }"
                    :options="taxonomiesStore.benefits"
                    v-model="benefitSelectedValue"
                    @update:modelValue="addBenefit"
                    :searchable="true"
                  />
                </div>
                <div class="plus-container">
                  <button class="btn-plus" type="button" @click="addBenefit">
                    <img src="/assets/icons/plus-circle.svg" />
                  </button>
                </div>
              </div>
            </div>
            <!-- ./Benefits -->
          </div>
          <!-- ./Benefit -->

          <!-- Design -->
          <div class="bg-white p-4 rounded-2">
            <h2>Design</h2>

            <!-- Choose banner -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Choose banner</label
              >
              <p class="heading lh-sm mb-2">Choose 1 banner</p>
              <div class="banner-select-container">
                <div
                  class="banner-card"
                  v-for="banner in taxonomiesStore.taxonomies.job_banner"
                  :class="{
                    'banner-card__selected': banner.id == topBannerValue,
                  }"
                >
                  <div class="banner-card__content">
                    <div class="banner-card__header">
                      <img :src="banner.thumbnail_url" class="w-full" />
                    </div>
                    <div class="banner-card__info">
                      <p class="mb-0">
                        {{ banner.text_vi ?? banner.text_en }}
                      </p>
                      <div class="banner-card__buttons">
                        <button
                          class="btn btn-topdev-1"
                          type="button"
                          :disabled="levelValue != 'paid'"
                          @click="settopBanner(banner.id)"
                        >
                          Chose
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- ./Choose banner -->

            <!-- Choose template color -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Choose template color</label
              >
              <div class="color-selector-container">
                <button
                  class="color-selector"
                  :style="{
                    'background-color': color.description,
                  }"
                  v-for="color in taxonomiesStore.taxonomies.job_template_color"
                  type="button"
                  :disabled="levelValue != 'paid'"
                  @click="setTemplateColor(color)"
                >
                  <span v-if="jobTemplateColorValue == color.id"></span>
                </button>
              </div>
            </div>
            <!-- ./Choose template color -->

            <!-- Choose template -->
            <div class="form-group">
              <label for="titleInput" class="required form-label mb-0"
                >Choose template</label
              >
              <p class="heading lh-sm mb-2">Choose 1 template</p>
              <div class="banner-select-container">
                <div
                  class="banner-card"
                  v-for="template in taxonomiesStore.taxonomies.job_template"
                  :class="{
                    'banner-card__selected': template.id == jobTemplateValue,
                  }"
                >
                  <div class="banner-card__content">
                    <div class="banner-card__header">
                      <img
                        :src="`https://c.topdevvn.com/uploads/2025/01/23/template-${template.id}-${templateColor}.png`"
                        class="w-100"
                      />
                    </div>
                    <div class="banner-card__info">
                      <p class="mb-0">
                        {{ template.text_vi ?? template.text_en }}
                      </p>
                      <div class="banner-card__buttons">
                        <button
                          class="btn btn-topdev-1"
                          type="button"
                          :disabled="levelValue != 'paid'"
                          @click="setjobTemplate(template.id)"
                        >
                          Chose
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- ./Choose template -->

            <!-- Company tagline -->
            <div class="form-group">
              <label for="titleInput" class="form-label mb-0"
                >Company tagline</label
              >
              <input
                type="text"
                class="form-control form-control-solid"
                :class="{
                  'is-invalid':
                    companyTaglineMeta.touched && companyTaglineError,
                }"
                :placeholder="translate('job_form_enter_your_job_title')"
                name="title"
                v-model="companyTaglineValue"
                :disabled="levelValue != 'paid'"
              />
              <AppErrorMessage name="company_tagline" />
            </div>
            <!-- ./Company tagline -->

            <!-- Upload company logo -->
            <div class="form-group">
              <label for="titleInput" class="form-label mb-0"
                >Upload company logo</label
              >
              <div id="logo-upload-container">
                <AppImageUpload
                  type="logo"
                  id="logo-image"
                  @uploaded="setCompanyLogo"
                  :url="companyLogoValue as string"
                  v-if="levelValue == 'paid'"
                />
              </div>
            </div>
            <!-- ./Upload company logo -->
          </div>
          <!-- ./Design -->

          <!-- Additional information -->
          <div class="bg-white p-4 rounded-2">
            <h2>Additional Information</h2>

            <TheJobFormEmailAndNote />
          </div>
          <!-- ./Additional information -->
        </div>
      </div>

      <div class="col-3">
        <div class="bg-white p-4 rounded-2" id="progress-container">
          <div>
            <div
              class="d-flex justify-content-between w-100 fs-4 fw-bolder mb-3"
            >
              <span>Progress</span>
              <span>{{ progressPercentage }}%</span>
            </div>
            <div class="h-8px bg-light rounded mb-3">
              <div
                class="bg-success rounded h-8px"
                role="progressbar"
                :style="{ width: progressPercentage + '%' }"
                aria-valuenow="progressPercentage"
                aria-valuemin="0"
                aria-valuemax="100"
              ></div>
            </div>
          </div>
          <div>
            <p>You can click on the tag to quickly jump to section</p>
          </div>
          <ul class="progress-list">
            <li>
              <div><p>Basic information</p></div>
              <div>
                <span :class="{ success: isBasicInfoFilled }">
                  <img
                    :src="
                      isBasicInfoFilled
                        ? '/assets/icons/check-circle.svg'
                        : '/assets/icons/cross-circle.svg'
                    "
                    alt=""
                  />
                </span>
              </div>
            </li>
            <li>
              <div><p>Job Detail</p></div>
              <div>
                <span :class="{ success: isJobDetailFilled }">
                  <img
                    :src="
                      isJobDetailFilled
                        ? '/assets/icons/check-circle.svg'
                        : '/assets/icons/cross-circle.svg'
                    "
                    alt=""
                  />
                </span>
              </div>
            </li>
            <li>
              <div><p>Education</p></div>
              <div>
                <span :class="{ success: isEducationFilled }">
                  <img
                    :src="
                      isEducationFilled
                        ? '/assets/icons/check-circle.svg'
                        : '/assets/icons/cross-circle.svg'
                    "
                    alt=""
                  />
                </span>
              </div>
            </li>
            <li>
              <div><p>Benefit</p></div>
              <div>
                <span :class="{ success: isBenefitFilled }">
                  <img
                    :src="
                      isBenefitFilled
                        ? '/assets/icons/check-circle.svg'
                        : '/assets/icons/cross-circle.svg'
                    "
                    alt=""
                  />
                </span>
              </div>
            </li>
            <li v-if="levelValue == 'paid'">
              <div><p>Design</p></div>
              <div>
                <span :class="{ success: isDesignFilled }">
                  <img
                    :src="
                      isDesignFilled
                        ? '/assets/icons/check-circle.svg'
                        : '/assets/icons/cross-circle.svg'
                    "
                    alt=""
                  />
                </span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import axios from 'axios';
import AppImageUpload from "@/components/AppImageUpload.vue";
import Multiselect from "@vueform/multiselect";
import AppTextEditor from "@/components/AppTextEditor.vue";
import JobPackageSelection from "@/components/JobPackageSelection.vue";
import AppErrorMessage from "@/components/AppErrorMessage.vue";
import TheJobFormEmailAndNote from "@/components/TheJobFormEmailAndNote.vue";
import { useField, useFieldArray, useForm } from "vee-validate";
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { AvailablePackage } from "@/models/usage-history";
import { showWarningToast, translate } from "@/helpers";
import {
  useAuthStore,
  useJobStore,
  useLayoutStore,
  useTaxonomiesStore,
} from "@/stores";
import {
  checkItJobPostingQuotaAction,
  checkRequestPackageTicket,
  fetchAvailablePackages,
  fetchCheckAvailablePackage,
  fetchCheckHasFreeJobOpened,
  fetchCompanyInfo
} from "@/api/company";
import { getJobCategories } from "@/api/job";
import { Modal } from "bootstrap";
import AppCurrencyInput from "@/components/AppCurrencyInput.vue";
import { JobCategories, JobCategory, JobRoles, Salary } from "@/models/jobs";

interface Props {
  isCreate?: boolean;
  initialPackageId?: string | null;
  jobData?: {
    level?: string;
    package_id?: number | string;
  } | null;
}

const props = withDefaults(defineProps<Props>(), {
  isCreate: false,
  initialPackageId: null,
  jobData: null
});

// Define emits
const emit = defineEmits<{
  (e: 'package-change', isFree: boolean): void;
}>();

// Store
const taxonomiesStore = useTaxonomiesStore();

// Data
const responsibilitySelectedValue = ref();
const requirementSelectedValue = ref();
const recruitmentProcessSelectedValue = ref();
const benefitSelectedValue = ref();
const availablePackages = ref<AvailablePackage[]>([]);
const hasFreeJobOpened = ref(false);
const hasFreeJobPostingQuota = ref(false);
const freeJobOpeningCount = ref(0);
const freeITJobOpening = ref(0);
const freeNonITJobOpening = ref(0);

const setHasFreeJobOpened = (value: boolean) => {
  hasFreeJobOpened.value = value;
};
const setHasFreeJobPostingQuota = (value: boolean) => {
  hasFreeJobPostingQuota.value = value;
};

interface JobCategoryOption {
  value: number;
  label: string;
}

const jobCategories = ref<JobCategory[]>([]);
const jobRoles = ref<JobRoles>({});
const itJobCount = ref<number>(0);
const disabledPackages = ref<string[]>([]);
const isPackageSelectionDisabled = computed(() => {
  // Disable package selection when editing any job
  return !props.isCreate;
});

// Computed
const isFreeJobSelected = computed(() => packageValue.value === 'free');
const isITCategorySelected = computed(() => categoryId.value === 1);

// Filter available packages to exclude free job when IT category is selected
const filteredAvailablePackages = computed(() => {
  if (isITCategorySelected.value) {
    return availablePackages.value.filter(pkg => pkg.value !== 'free');
  }
  return availablePackages.value;
});

// Watch for changes to category and package selection
watch(() => categoryId.value, (newCategoryId, oldCategoryId) => {
  if (newCategoryId === 1 && packageValue.value === 'free') {
    // If IT category is selected and free job was selected, clear the package selection
    packageValue.value = '';
  }
});

// Handle package change event from JobPackageSelection component
const handlePackageChange = (isFreePackage: boolean) => {
  // Don't change level if editing an existing free job
  if (!props.isCreate && props.jobData?.level === 'free') {
    return;
  }

  if (isFreePackage) {
    setLevelValue('free');

    // Clear IT category if selected
    if (categoryId.value === 1) {
      setCategoryId(null);

      // remove job category id = 1 in jobCategories
      jobCategories.value = jobCategories.value.filter(
        (category: JobCategory) => category.id !== 1
      );
    }
  } else {
    // Only set to paid if not editing a free job
    if (props.isCreate || props.jobData?.level !== 'free') {
      setLevelValue('paid');
    }
  }
};

// Watch for changes to package value
watch(() => packageValue.value, (newPackageValue) => {
  // Don't change level if editing an existing free job
  if (!props.isCreate && props.jobData?.level === 'free') {
    return;
  }

  if (newPackageValue === 'free') {
    // If free job is selected, set level to 'free' and clear IT category if selected
    setLevelValue('free');

    // Clear IT category if selected
    if (categoryId.value === 1) {
      setCategoryId(null);
    }
  } else if (newPackageValue) {
    // Only set to paid if not editing a free job
    if (props.isCreate || props.jobData?.level !== 'free') {
      setLevelValue('paid');
    }
  }
});

// Form field
const {
  value: title,
  handleBlur: handleTitleBlur,
  meta: titleMeta,
  errorMessage: titleError,
} = useField("title");

const {
  value: categoryId,
  meta: categoryMeta,
  errorMessage: categoryError,
  setValue: setCategoryId
} = useField<number>("category_id");

const {
  value: jobCategoryId,
  meta: roleMeta,
  handleBlur: handleRoleBlur,
  errorMessage: roleError,
} = useField("job_category_id");

const {
  value: skillsIds,
  meta: skillsIdsMeta,
  errorMessage: skillsIdsError,
} = useField("skills_ids");

const {
  value: jobLevels,
  meta: jobLevelsMeta,
  errorMessage: jobLevelsError,
} = useField("job_levels");

const {
  value: experienceFromValue,
  meta: experienceFromMeta,
  handleBlur: handleExperienceFromBlur,
  errorMessage: experienceFromError,
} = useField("experiences_ids.from");

const { value: experienceToValue } = useField("experiences_ids.to");

const {
  value: jobTypes,
  meta: jobTypesMeta,
  handleBlur: jobTypesBlur,
  errorMessage: jobTypesError,
} = useField("job_types");

const {
  value: contractTypeValue,
  meta: contractTypeMeta,
  handleBlur: contractTypeBlur,
  errorMessage: contractTypeError,
} = useField("contract_type");

const {
  value: addressesIdValue,
  meta: addressesIdMeta,
  handleBlur: addressesIdBlur,
  errorMessage: addressesIdError,
} = useField("addresses_id");

const {
  value: contentValue,
  errorMessage: contentError,
  setValue: setContentValue,
} = useField<string>("content");

const {
  fields: responsibilitiesField,
  push: pushResponsibilityField,
  remove: removeResponsibilityField,
  replace: setResponsibilitiesValue,
} = useFieldArray("responsibilities");

const responsibilitiesList = computed({
  get() {
    return responsibilitiesField.value.map((item) => item.value);
  },

  set(value: any) {
    setResponsibilitiesValue(value);
  },
});

const addResponsibility = () => {
  const responsibility = taxonomiesStore.taxonomies.responsibilities.find(
    (e) => e.id == responsibilitySelectedValue.value
  );

  pushResponsibilityField({
    id: responsibility.id,
    name: responsibility.text_en,
    description: responsibility.description,
  });

  responsibilitySelectedValue.value = null;
};

const {
  fields: requirementsField,
  push: pushRequirementsField,
  remove: removeRequirementsField,
  replace: setRequirementsValue,
} = useFieldArray("requirements");

const requirementsList = computed({
  get() {
    return requirementsField.value.map((item) => item.value);
  },

  set(value: any) {
    setRequirementsValue(value);
  },
});

const addRequirement = () => {
  const requirement = taxonomiesStore.taxonomies.requirements.find(
    (e) => e.id == requirementSelectedValue.value
  );

  pushRequirementsField({
    id: requirement.id,
    name: requirement.text_en,
    description: requirement.description,
  });

  requirementSelectedValue.value = null;
};

const { value: educationDegreeValue } = useField<string>("education_degree");

const {
  value: educationMajorValue,
  handleBlur: educationMajorBlur,
  errorMessage: educationMajorError,
} = useField<string>("education_major");

const { value: levelValue, setValue: setLevelValue } =
  useField<string>("level");

const {
  value: educationCertificateValue,
  meta: educationCertificateMeta,
  handleBlur: educationCertificateBlur,
  errorMessage: educationCertificateError,
} = useField("education_certificate");

const onChangeCurrency = (value: string) => {
  salaryValue.value.currency = value;
};

const onChangeSalaryType = () => {
  salaryValue.value.min = 0;
};

const { value: salaryValue, meta: salaryMeta } = useField<Salary>("salary");

const { value: minSalaryValue, errorMessage: minSalaryError } = useField<
  number | null
>("salary.min");

const { value: maxSalaryValue, errorMessage: maxSalaryError } = useField<
  number | null
>("salary.max");

const { value: minEstimateSalaryValue, errorMessage: minEstimateSalaryError } =
  useField<number | null>("salary.min_estimate");

const { value: maxEstimateSalaryValue, errorMessage: maxEstimateSalaryError } =
  useField<number | null>("salary.max_estimate");

watch(
  () => salaryValue.value.is_negotiable,
  () => onChangeSalaryType()
);

watch(
  () => hasFreeJobOpened.value,
  (value) => {
    if (value) {
      document.documentElement.style.overflow = 'hidden';
      return
    }

    document.documentElement.style.overflow = 'auto';
  }
);

const {
  fields: benefitsField,
  push: pushbenefitsField,
  remove: removebenefitsField,
  replace: setbenefitsValue,
} = useFieldArray("benefits");

const benefitsList = computed({
  get() {
    return benefitsField.value.map((item) => item.value);
  },

  set(value: any) {
    setbenefitsValue(value);
  },
});

const addBenefit = () => {
  const benefit = taxonomiesStore.taxonomies.benefits.find(
    (e) => e.id == benefitSelectedValue.value
  );

  pushbenefitsField({
    id: benefit.id,
    name: benefit.text_en,
    description: benefit.description,
  });

  benefitSelectedValue.value = null;
};

const {
  fields: recruimentProcessField,
  push: pushrecruimentProcessField,
  remove: removerecruimentProcessField,
  replace: setrecruimentProcessValue,
} = useFieldArray("recruiment_process");

const recruimentProcessList = computed({
  get() {
    return recruimentProcessField.value.map((item) => item.value);
  },

  set(value: any) {
    setrecruimentProcessValue(value);
  },
});

const addRecruitmentProcess = () => {
  const process = taxonomiesStore.taxonomies.recruitment_processes.find(
    (e) => e.id == recruitmentProcessSelectedValue.value
  );

  pushrecruimentProcessField({
    id: process.id,
    name: process.text_en,
    description: process.description,
  });

  recruitmentProcessSelectedValue.value = null;
};

const {
  value: companyTaglineValue,
  meta: companyTaglineMeta,
  errorMessage: companyTaglineError,
} = useField("company_tagline");

const { value: companyLogoValue, setValue: setCompanyLogo } =
  useField("company_logo");

const {
  value: packageValue,
  errorMessage: packageError,
  setValue: setPackageValue
} = useField<string>("package_id");

const { value: topBannerValue, setValue: settopBanner } =
  useField("job_banner");

const { value: jobTemplateValue, setValue: setjobTemplate } =
  useField("job_template");

const { value: jobTemplateColorValue, setValue: setjobTemplateColor } =
  useField("job_template_color");

const getAvailablePackages = computed(() =>
  availablePackages.value.map((item: AvailablePackage) => ({
    value: item.value,
    label: `${
      item.is_free_package
        ? `${item.label} ${translate(
            "usage_history_job_posting_package_gift_title"
          )}`
        : item.label
    } (${translate("usage_history_job_posting_quota_package_expired_at", [
      item.expired_at,
    ])})`,
  }))
);

const clearSelectedRoles = () => {
  jobCategoryId.value = [];
}

const checkPackageAvailable = async (onSubmit = false): Promise<boolean> => {
  const { available } = (await fetchCheckAvailablePackage()) as any;

  return !!available;
};

const checkHasFreeJobOpened = async (): Promise<boolean> => {
  const { hasFreeJobPostingOpened } = (await fetchCheckHasFreeJobOpened()) as any;
  return hasFreeJobPostingOpened;
};

const checkFreeJobPostingQuota = async (): Promise<boolean> => {
  const { hasFreeJobPostingQuota, freeITJobPostingOpening, freeJobOpeningCount, freeNonITJobPostingOpening } = (await fetchCheckHasFreeJobOpened()) as any;
  freeITJobOpening.value = 1 - freeITJobPostingOpening;
  freeNonITJobOpening.value = freeNonITJobPostingOpening;
  freeJobOpeningCount.value = freeJobOpeningCount;
  if (freeJobOpeningCount.value >= 6) {
    freeITJobOpening.value = 0;
    freeNonITJobOpening.value = 0;
  }

  return hasFreeJobPostingQuota;
};

const openModalSelectJobPackage = () => {
  Modal.getOrCreateInstance("#modal-select-job-package").show();
};

// Progress bar
const isBasicInfoFilled = computed(() => {
  return (
    !!title.value &&
    (jobCategoryId.value as any[]).length > 0 &&
    (skillsIds.value as any[]).length > 0 &&
    (jobLevels.value as any[]).length > 0 &&
    !!experienceFromValue.value &&
    (jobTypes.value as any[]).length > 0 &&
    (contractTypeValue.value as any[]).length > 0 &&
    (addressesIdValue.value as any[]).length > 0
  );
});

const isJobDetailFilled = computed(() => {
  return (
    !!contentValue.value &&
    responsibilitiesList.value.length > 0 &&
    requirementsList.value.length > 0 &&
    recruimentProcessList.value.length > 0
  );
});

const isEducationFilled = computed(() => {
  return educationDegreeValue.value.length > 0 && educationMajorValue.value;
});

const isBenefitFilled = computed<boolean>(() => {
  return (
    !!minSalaryValue.value &&
    !!maxSalaryValue.value &&
    benefitsList.value.length > 0
  );
});

const isDesignFilled = computed<boolean>(() => {
  return (
    !!topBannerValue.value &&
    !!jobTemplateValue.value &&
    !!jobTemplateColorValue.value
  );
});

const progressPercentage = computed(() => {
  const sections = [
    isBasicInfoFilled,
    isJobDetailFilled,
    isEducationFilled,
    isBenefitFilled,
  ];

  if (levelValue.value == "paid") {
    sections.push(isDesignFilled);
  }

  const filledSections = sections.filter((section) => section.value).length;
  return (filledSections / sections.length) * 100;
});

// Fetch IT job quota from API
const fetchItJobQuota = async () => {
  try {
    const { it_job_count }: any = await checkItJobPostingQuotaAction();
    itJobCount.value = it_job_count || 0;
    return itJobCount.value;
  } catch (error) {
    console.error('Error fetching IT job quota:', error);
    return 0;
  }
};

const mapJobCategories = computed(() => {
  // If free job is selected and company has already posted an IT job, exclude IT category
  if ((isFreeJobSelected.value && levelValue.value === 'paid')
    || (levelValue.value === 'free'
      && (itJobCount.value >= 1 || hasFreeJobPostingQuota.value)
    )
  ) {
    return jobCategories.value
      .filter(item => item.id !== 1) // Exclude IT category (ID: 1)
      .map(item => ({
        value: item.id,
        label: item.name
      }));
  }

  // Show all categories when:
  // 1. It's a paid job, or
  // 2. Company hasn't reached the free IT job quota yet
  return jobCategories.value.map(item => ({
    value: item.id,
    label: item.name
  }));
});

const filterJobRoles = computed(() => {
  if (!categoryId.value || !jobRoles.value[categoryId.value]) {
    return [];
  }
  const jobRolesByCat = jobRoles.value[categoryId.value] || [];
  return jobRolesByCat.map((item) => ({
    value: item.id,
    label: item.name,
  }));
});

const companyAddresses = ref();
const templateColor = ref("blue");

const setTemplateColor = (color) => {
  templateColor.value = color.slug;
  setjobTemplateColor(color.id);
};

onBeforeUnmount(() => {
  // Reset document overflow when back to other page
  document.documentElement.style.overflow = 'auto';
});

// Life cycle: on mounted
onMounted(async () => {
  await fetchItJobQuota();

  getJobCategories().then((result: JobCategories) => {
    jobCategories.value = result.categories;
    jobRoles.value = result.roles;
  });

  // If editing an existing job, set the initial package value
  if (!props.isCreate) {
    // Wait for the next tick to ensure the package selection is rendered
    await nextTick();

    let packageIdToSet = null;

    // Determine the package ID to set
    if (props.initialPackageId) {
      packageIdToSet = props.initialPackageId;
    } else if (props.jobData) {
      packageIdToSet = props.jobData.level === 'free'
        ? 'free'
        : (props.jobData.package_id?.toString() || null);
    }

    // If we have a package ID to set
    if (packageIdToSet) {
      // Set the package value
      packageValue.value = packageIdToSet;

      // If it's a free job, set the level to free
      if (packageIdToSet === 'free') {
        setLevelValue('free');
      }

      // Emit the package change event
      emit('package-change', packageIdToSet === 'free');

      // Debug log
      console.log('Set initial package:', {
        packageId: packageIdToSet,
        isFree: packageIdToSet === 'free',
        level: levelValue.value
      });
    }
  }

  const { data } = await checkRequestPackageTicket();

  if (data.isRequestingPackageTicket) {
    Modal.getOrCreateInstance("#modal-pending-packages-usage-topup").show();
    return;
  }

  const hasFreeJobPostingQuotaValue = props.isCreate ? await checkFreeJobPostingQuota() : false;
  setHasFreeJobPostingQuota(hasFreeJobPostingQuotaValue);

  const isPackageAvailable = await checkPackageAvailable().then(async (available) => {
    if (!available) {
      hasFreeJobOpened.value = props.isCreate ? await checkHasFreeJobOpened() : false;
      if (hasFreeJobOpened.value) {
        return available;
      }
      openModalSelectJobPackage();
    }

    return available;
  });

  // Free job posting should check already have free opened job or not before allowing to post job
  if (!isPackageAvailable) {
    if (hasFreeJobOpened.value) {
      Modal.getOrCreateInstance("#modal-free-plan-post-job").show();
      return;
    }
  }

  if (isPackageAvailable) {
    await fetchAvailablePackages(packageValue.value as string).then(
      ({ data }) => {
        availablePackages.value = data;

        // Only set level to paid if it's not a free job
        if (data.length > 0 && levelValue.value !== 'free') {
          setLevelValue("paid");
        }
      }
    );
  }

  // Get company addresses
  await fetchCompanyInfo().then((result) => {
    if (result.data) {
      companyAddresses.value = result.data.addresses.map((address: any) => ({
        value: address.id,
        label: address.full_address,
      }));
    }
  });

  recalculateAllTextareaHeights();

  // Second attempt after a short delay to ensure content is rendered
  setTimeout(() => {
    recalculateAllTextareaHeights();
  }, 100);

  // Third attempt after the page is fully loaded
  window.addEventListener('load', recalculateAllTextareaHeights);
});

// Add these new refs
const responsibilitySearchKeyword = ref('');
const requirementSearchKeyword = ref('');

const initialResponsibilities = computed(() => {
  if (!responsibilitySearchKeyword.value) {
    return taxonomiesStore.taxonomies.responsibilities
      .filter(item => item.text_en && item.text_en.trim() !== '')
      .slice(0, 25);
  }
  return filteredResponsibilities.value;
});

const initialRequirements = computed(() => {
  if (!requirementSearchKeyword.value) {
    return taxonomiesStore.taxonomies.requirements
      .filter(item => item.text_en && item.text_en.trim() !== '')
      .slice(0, 25);
  }
  return filteredRequirements.value;
});

// Add computed properties for filtered lists
const filteredResponsibilities = computed(() => {
  if (!responsibilitySearchKeyword.value) return [];

  return taxonomiesStore.taxonomies.responsibilities
    .filter(item =>
      item.text_en &&
      item.text_en.trim() !== '' &&
      item.text_en.toLowerCase().includes(responsibilitySearchKeyword.value.toLowerCase())
    );
});

const filteredRequirements = computed(() => {
  if (!requirementSearchKeyword.value) return [];

  return taxonomiesStore.taxonomies.requirements
    .filter(item =>
      item.text_en &&
      item.text_en.trim() !== '' &&
      item.text_en.toLowerCase().includes(requirementSearchKeyword.value.toLowerCase())
    );
});

// Add methods to check if an item is already selected
const isResponsibilitySelected = (id) => {
  return responsibilitiesList.value.some(item => item.id === id);
};

const isRequirementSelected = (id) => {
  return requirementsList.value.some(item => item.id === id);
};

// Replace the existing addResponsibility method
const selectResponsibility = (responsibility) => {
  if (isResponsibilitySelected(responsibility.id)) {
    // Remove the item if already selected
    const index = responsibilitiesList.value.findIndex(item => item.id === responsibility.id);
    if (index !== -1) {
      removeResponsibilityField(index);
    }
  } else {
    // Add the item if not selected
    pushResponsibilityField({
      id: responsibility.id,
      name: responsibility.text_en,
      description: responsibility.description || responsibility.text_en,
    });
  }
  // Don't close the dropdown to allow multiple selections
};

// Replace the existing addRequirement method
const selectRequirement = (requirement) => {
  if (isRequirementSelected(requirement.id)) {
    // Remove the item if already selected
    const index = requirementsList.value.findIndex(item => item.id === requirement.id);
    if (index !== -1) {
      removeRequirementsField(index);
    }
  } else {
    // Add the item if not selected
    pushRequirementsField({
      id: requirement.id,
      name: requirement.text_en,
      description: requirement.description || requirement.text_en,
    });
  }
  // Don't close the dropdown to allow multiple selections
};

// Add methods to handle keyboard events
const handleResponsibilityKeydown = (e) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    addCustomResponsibility();
  }
};

const handleRequirementKeydown = (e) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    addCustomRequirement();
  }
};

// Add these refs for dropdown visibility
const showResponsibilityDropdown = ref(false);
const showRequirementDropdown = ref(false);

// Add methods to close dropdowns
const closeResponsibilityDropdown = () => {
  showResponsibilityDropdown.value = false;
};

const closeRequirementDropdown = () => {
  showRequirementDropdown.value = false;
};

const vClickOutside = {
  mounted(el, binding) {
    el._clickOutside = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener('click', el._clickOutside);
  },
  unmounted(el) {
    document.removeEventListener('click', el._clickOutside);
  }
};

const getOtherResponsibilityId = computed(() => {
  const otherItem = taxonomiesStore.taxonomies.responsibilities.find(
    item => item.text_en === "Other"
  );
  return otherItem ? otherItem.id : null;
});

const getOtherRequirementId = computed(() => {
  const otherItem = taxonomiesStore.taxonomies.requirements.find(
    item => item.text_en === "Other"
  );
  return otherItem ? otherItem.id : null;
});

const addCustomResponsibility = () => {
  if (responsibilitySearchKeyword.value.trim() && getOtherResponsibilityId.value) {
    pushResponsibilityField({
      id: getOtherResponsibilityId.value,
      name: "Other",
      description: responsibilitySearchKeyword.value.trim()
    });
    responsibilitySearchKeyword.value = '';
  }
};

const addCustomRequirement = () => {
  if (requirementSearchKeyword.value.trim() && getOtherRequirementId.value) {
    pushRequirementsField({
      id: getOtherRequirementId.value,
      name: "Other",
      description: requirementSearchKeyword.value.trim()
    });
    requirementSearchKeyword.value = '';
  }
};

const autoGrow = (element) => {
  element.style.height = "auto";
  element.style.height = (element.scrollHeight) + "px";
};

// Prevent Enter key from creating new lines in textarea
const preventEnter = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    // Optionally blur the textarea to trigger save
    event.target.blur();
  }
};

const focusResponsibilityTextarea = (index: number) => {
  nextTick(() => {
    const textarea = document.querySelector(`textarea[data-ref="responsibilityTextarea_${index}"]`) as HTMLTextAreaElement | null;
    if (textarea) {
      textarea.focus();
      textarea.select();
    }
  });
};

const focusRequirementTextarea = (index: number) => {
  nextTick(() => {
    const textarea = document.querySelector(`textarea[data-ref="requirementTextarea_${index}"]`) as HTMLTextAreaElement | null;
    if (textarea) {
      textarea.focus();
      textarea.select();
    }
  });
};

// Add missing update methods for responsibilities and requirements
const updateResponsibilityField = (index, element) => {
  // Update the field array with the new description
  const currentFields = [...responsibilitiesField.value];
  if (currentFields[index] && currentFields[index].value) {
    currentFields[index].value = Object.assign(
      {},
      currentFields[index].value,
      { description: element.description }
    );
    setResponsibilitiesValue(currentFields.map(field => field.value));
  }

  nextTick(() => {
    const textarea = document.querySelector(`textarea[data-ref="responsibilityTextarea_${index}"]`);
    if (textarea) {
      autoGrow(textarea);
    }
  });
};

const updateRequirementField = (index, element) => {
  // Update the field array with the new description
  const currentFields = [...requirementsField.value];
  if (currentFields[index] && currentFields[index].value) {
    currentFields[index].value = Object.assign(
      {},
      currentFields[index].value,
      { description: element.description }
    );
    setRequirementsValue(currentFields.map(field => field.value));
  }

  nextTick(() => {
    const textarea = document.querySelector(`textarea[data-ref="requirementTextarea_${index}"]`);
    if (textarea) {
      autoGrow(textarea);
    }
  });
};

const updateRecruitmentProcessField = (index, element) => {
  // Update the field array with the new description
  const currentFields = [...recruimentProcessField.value];
  if (currentFields[index] && currentFields[index].value) {
    currentFields[index].value = Object.assign(
      {},
      currentFields[index].value,
      { description: element.description }
    );
    setrecruimentProcessValue(currentFields.map(field => field.value));
  }
};

const updateBenefitField = (index, element) => {
  // Update the field array with the new description
  const currentFields = [...benefitsField.value];
  if (currentFields[index] && currentFields[index].value) {
    currentFields[index].value = Object.assign(
      {},
      currentFields[index].value,
      { description: element.description }
    );
    setbenefitsValue(currentFields.map(field => field.value));
  }
};

// Add a more robust recalculation function
const recalculateAllTextareaHeights = () => {
  nextTick(() => {
    document.querySelectorAll('textarea').forEach(el => {
      // Force recalculation by setting height to auto first
      el.style.height = 'auto';
      // Then set to scrollHeight
      el.style.height = (el.scrollHeight) + "px";
    });
  });
};

// Update the onBeforeUnmount to clean up the event listener
onBeforeUnmount(() => {
  // Clean up event listeners
  window.removeEventListener('load', recalculateAllTextareaHeights);
  document.documentElement.style.overflow = 'auto';
});

// Additionally, watch for changes in form data that might affect textarea content
watch([
  responsibilitiesList,
  requirementsList,
  () => props.isCreate // Watch for prop changes
], () => {
  recalculateAllTextareaHeights();
}, { deep: true, immediate: true });

const onDragEnd = () => {
  recalculateAllTextareaHeights();
};
</script>
<style scoped>
.search-results-dropdown.tags-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #e4e6ef;
  border-radius: 0.475rem;
  box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  z-index: 100;
  max-height: 170px;
  overflow-y: auto;
  padding: 0.75rem;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 120px;
  overflow-y: auto;
}

.tags-container::-webkit-scrollbar {
  width: 4px;
}

.tags-container::-webkit-scrollbar-thumb {
  background-color: #e4e6ef;
  border-radius: 4px;
}

.tags-container::-webkit-scrollbar-thumb:hover {
  background-color: #c9ccd7;
}

.suggested-tags {
  width: 100%;
  margin-bottom: 0.5rem;
}

.text-muted {
  color: #b5b5c3;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.tag-item {
  background-color: #f5f8fa;
  border-radius: 1.4rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
  display: inline-block;
  margin-right: 0.5rem;
}

.tag-item:hover {
  background-color: #e4e6ef;
}

.tag-item.selected {
  background-color: #DD3F24;
  color: white;
}

.input-taxonomies-select-container {
  display: flex;
  gap: 0.5rem;
}

.select-container {
  flex-grow: 1;
}

.plus-container {
  display: flex;
  align-items: center;
}

.btn-plus {
  background: none;
  border: none;
  cursor: pointer;
}

.free-job-alert {
  background-color: #E7E7E7;
  border-left: 4px solid;
}

.form-control.form-control-solid {
  background-color: #fff;
  border-color: #e4e6ef;
}

.form-control.form-control-solid:focus {
  background-color: #fff;
  border-color: #e4e6ef;
}
.selected-item {
  border: unset;
  border-radius: 8px;
  font-size: 14px;
}
.selected-item:hover, .selected-item:focus {
  background-color: #E7E7E7;
}
.selected-item .form-control.form-control-solid {
  font-size: 14px;
  padding: 12px 50px 12px 12px;
}
.selected-item:hover .form-control.form-control-solid,
.selected-item:focus .form-control.form-control-solid,
.selected-item .form-control.form-control-solid:focus{
  background-color: #E7E7E7;
}
.selected-item .btn.hover-svg-primary {
  padding: 12px;
}
.selected-item-parent .btn.hover-svg-primary.btn-trash-item {
  padding: 4px !important;
}

.selected-item-icon {
  width: 16px;
  height: 16px;
}
</style>
