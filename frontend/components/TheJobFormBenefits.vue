<template>
  <div>
    <!-- Benefits  -->
    <div class="mb-6">
      <label class="form-label mb-0">{{
          translate("job_form_job_benefits")
        }}</label>
      <AppTextEditor
        name="benefits"
        :initValue="benefits"
        :class="{ 'is-invalid': benefitMeta.dirty && benefitError }"
        :height="280"
        @update:initValue="(value) => changeBenefits(value)"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useField } from "vee-validate";
import AppTextEditor from "@/components/AppTextEditor.vue";

import { translate } from "@/helpers";

const {
  value: benefits,
  meta: benefitMeta,
  errorMessage: benefitError,
  handleChange: changeBenefits,
} = useField<string>("benefits");

</script>
