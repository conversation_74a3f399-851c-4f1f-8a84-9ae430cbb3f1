<template>
  <editor
    :init="options"
    :initial-value="initValue === 'null' ? '' : initValue"
    v-model.lazy="content"
  />
</template>

<script lang="ts">
import { defineComponent, ref, watch } from "vue";

/* Import tinymce for self hosted */
import "tinymce";
import "tinymce/icons/default";
import "tinymce/themes/silver";
import "tinymce/models/dom";

/* Import plugins */
import "tinymce/plugins/lists";

import { onMounted } from "vue";
import Editor from "@tinymce/tinymce-vue";

export default defineComponent({
  name: "TextEditor",
  components: {
    editor: Editor,
  },
  props: {
    initValue: String,
    height: Number,
  },
  emits: ["update:initValue"],
  setup(props, { emit }) {
    const options = {
      menubar: false,
      branding: false,
      toolbar2: null,
      elementpath: false,
      plugins: ["lists"],
      toolbar: "bold italic underline alignleft alignjustify bullist numlist",
      content_style:
        'body { font-family: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Helvetica, "sans-serif"; }',
      height: props.height,
      skin: false,
      content_css: false,
      paste_as_text: true,
    };

    const content = ref();

    onMounted(() => {
      content.value = props.initValue;
    });

    watch(
      () => content.value,
      (values) => emit("update:initValue", values)
    );

    return {
      options,
      content,
    };
  },
});
</script>
