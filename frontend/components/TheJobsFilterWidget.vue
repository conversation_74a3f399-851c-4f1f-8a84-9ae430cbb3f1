<template>
  <swiper :spaceBetween="5" :slidesPerView="'auto'">
    <swiper-slide class="w-auto">
      <button
        class="btn-topdev-filter"
        :class="{ active: status === null }"
        @click="setStatusValue(null)"
      >
        {{ `${translate("job_list_all")} (${allJob})` }}
      </button>
    </swiper-slide>
    <swiper-slide class="w-auto">
      <button
        type="button"
        class="btn-topdev-filter ms-3"
        :class="{ active: status === 2 }"
        @click="setStatusValue(2)"
      >
        {{ `${translate("job_list_review")} (${reviewJob})` }}
      </button>
    </swiper-slide>
    <swiper-slide class="w-auto">
      <button
        type="button"
        class="btn-topdev-filter ms-3"
        :class="{ active: status === 3 }"
        @click="setStatusValue(3)"
      >
        {{ `${translate("job_list_open")} (${openJob})` }}
      </button>
    </swiper-slide>
    <swiper-slide class="w-auto">
      <button
        type="button"
        class="btn-topdev-filter ms-3"
        :class="{ active: status === 1 }"
        @click="setStatusValue(1)"
      >
        {{ `${translate("job_list_closed")} (${closedJob})` }}
      </button>
    </swiper-slide>
    <swiper-slide class="w-auto">
      <button
        type="button"
        class="btn-topdev-filter ms-3"
        :class="{ active: status === 0 }"
        @click="setStatusValue(0)"
      >
        {{ `${translate("job_list_draft")} (${draftJob})` }}
      </button>
    </swiper-slide>
  </swiper>
</template>
<script lang="ts" setup>
import { ref, Ref, onMounted } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";

import { translate } from "@/helpers";
import { JobsFacetes } from "@/models/jobs";

//Define props
interface Props {
  jobsFacets: JobsFacetes;
}
const props = defineProps<Props>();

//Define emit
interface Emit {
  (e: "update", value: string | number): void;
}
const emit = defineEmits<Emit>();

//Define data
const status: Ref<number> = ref(null);
const reviewJob = ref(0);
const closedJob = ref(0);
const openJob = ref(0);
const draftJob = ref(0);
const allJob = ref(0);

//Function
const setStatusValue = (value: number) => {
  status.value = value;
  emit("update", value);
};

//Life cycle
onMounted(() => {
  props.jobsFacets.status.forEach((status) => {
    allJob.value += status.total;
    if (status.status_code === "Review") reviewJob.value = status.total;
    else if (status.status_code === "Closed") closedJob.value = status.total;
    else if (status.status_code === "Open") openJob.value = status.total;
    else if (status.status_code === "Draft") draftJob.value = status.total;
  });
});
</script>
