<template>
  <section id="top-concerns-container" v-if="!!faqs">
    <!--begin::Section header-->
    <div class="d-flex justify-content-between">
      <div class="section-header">
        <h2 class="mb-0">{{ translate("about_company_top_concerns") }}</h2>
        <span class="heading">{{
          translate("about_company_add_the_frequently_ask_questions")
        }}</span>
      </div>

      <button
        class="btn svg-icon svg-icon-2 text-primary fw-bolder d-flex align-items-center px-0 py-0"
        type="button"
        @click="onAddFaq"
        :disabled="!isHideAddButton"
      >
        <inline-svg src="/assets/icons/plus.svg" class="me-1" />{{
          translate("about_company_add")
        }}
      </button>
    </div>
    <!--end::Section header-->

    <!--begin::Section body-->
    <div class="section-body">
      <ul
        class="sortable list-unstyled d-flex flex-column gap-3 top-conern-list"
      >
        <li
          v-for="(faq, index) in faqs"
          :key="index"
          class="d-flex gap-4 align-items-start"
        >
          <div class="d-flex flex-column flex-grow-1">
            <div class="input-with-icon mb-1">
              <span class="icon">
                <inline-svg src="/assets/icons/question.svg" />
              </span>
              <Field :name="`faqs[${index}].question`" v-slot="{ field, meta }">
                <input
                  type="text"
                  class="form-control"
                  :placeholder="translate('about_company_add_question')"
                  v-bind="field"
                  :class="{ 'is-invalid': !meta.valid }"
                />
              </Field>
              <AppErrorMessage :name="`faqs[${index}].question`" />
            </div>
            <div class="input-with-icon">
              <span class="icon">
                <inline-svg src="/assets/icons/answer.svg" />
              </span>
              <Field :name="`faqs[${index}].answer`" v-slot="{ field, meta }">
                <input
                  type="text"
                  class="form-control"
                  :placeholder="translate('about_company_add_answer')"
                  v-bind="field"
                  :class="{ 'is-invalid': !meta.valid }"
                />
              </Field>
              <AppErrorMessage :name="`faqs[${index}].answer`" />
            </div>
          </div>

          <button
            type="button"
            class="btn hover-svg-primary px-0 py-0 mt-1"
            @click="onDeleteFaq(index)"
          >
            <inline-svg src="/assets/icons/trash.svg" />
          </button>
        </li>
      </ul>
    </div>
    <!--end::Section body-->
  </section>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { Field, useFieldArray } from "vee-validate";

import AppErrorMessage from "@/components/AppErrorMessage.vue";
import { translate } from "@/helpers";

const {
  fields: faqs,
  push: pushFAQ,
  remove: removeFAQ,
} = useFieldArray("faqs");

//Define data
const isHideAddButton = computed(() => faqs.value.length < 10);

/**
 * Define function
 */
const onDeleteFaq = (index: number) => {
  removeFAQ(index);
};

const onAddFaq = () => {
  pushFAQ({
    question: "",
    answer: "",
    active: 1,
  });
};
</script>
