<template>
  <form class="col" autocomplete="off" action="#">
    <div class="d-flex justify-content-end align-items-center">
      <div class="position-relative flex-grow-1">
        <input
          type="text"
          name="search"
          class="form-control form-icon form-control-solid rounded"
          :placeholder="translate('job_list_search_placeholder')"
          v-model="searchField"
          @keyup.enter="onSearch"
        />
        <div class="position-absolute translate-middle-y top-42 start-0 mx-3">
          <span class="svg-icon svg-icon-2 text-topdev-3">
            <inline-svg src="/assets/icons/search.svg" />
          </span>
        </div>
      </div>
      <span
        :class="{ active: !!props.isChangeJob }"
        class="svg svg-icon-2 ps-2 text-topdev-3 position-relative ms-1 cursor"
        @click="onOpenJobFilterModal"
      >
        <inline-svg src="assets/icons/filter.svg" />
      </span>
      <button
        type="button"
        class="btn btn-sm btn-primary ms-3 fs-6 py-2"
        @click="onSearch"
      >
        {{ translate("job_list_search") }}
      </button>
      <button
        type="button"
        class="btn btn-sm btn-success d-flex gap-2 ms-3 fs-6 py-2"
        @click="downloadJobs"
      >
        <span
          v-if="isDownloadBtnLoading"
          class="spinner-border spinner-border-sm align-self-center"
          role="status"
          aria-hidden="true"
        ></span>
        {{ translate("job_list_download") }}
      </button>
    </div>
  </form>
  <!-- Filter bar  -->
  <form
    class="d-flex col-12 align-items-start flex-wrap mt-3 container-filter w-100"
     autocomplete="off" action="#"
    >
    <span class="title-filter">{{ translate("candidate_list_filter") }}:</span>
    <!-- Show filter index -->
    <div
      class="container-filter-box d-flex flex-wrap align-items-center"
      id="container-filter-box"
    >
      <!-- Job title  -->
      <div
        class="item-filter flex-grow-1 ms-2"
        :class="{ active: !!props.jobsParams.job_id }"
      >
        <Multiselect
          :options="props.filterOptions.jobs"
          v-model="props.jobsParams.job_id"
          mode="single"
          autocomplete="off"
          :searchable="true"
          :placeholder="translate('job_list_job_title')"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template v-slot:singlelabel="{ value }: { value: OptionTitle }">
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("job_list_job_title") }}: {{ value.label }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- Job location  -->
      <div
        class="item-filter flex-grow-1 ms-2"
        :class="{ active: !!props.jobsParams.location_id }"
      >
        <Multiselect
          :placeholder="translate('job_list_province_city')"
          :options="props.filterOptions.province"
          v-model="props.jobsParams.location_id"
          mode="single"
          autocomplete="off"
          :searchable="true"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template v-slot:singlelabel="{ value }">
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("job_list_province_city") }}:
                {{ value.label }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- Skills -->
      <div
        class="item-filter flex-grow-1 ms-2"
        :class="{
          active: props.jobsParams.skills_id.length > 0,
        }"
      >
        <Multiselect
          :placeholder="translate('job_list_skills')"
          :options="props.filterOptions.taxonomiesSkills"
          :searchable="true"
          :hideSelected="false"
          :closeOnSelect="false"
          v-model="props.jobsParams.skills_id"
          mode="multiple"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template v-slot:multiplelabel="{ values }">
            <div class="multiselect-multiple-label">
              <span
                >{{ translate("job_list_skills") }}:
                {{
                  values.length > 2
                    ? values.length
                    : values.map(({ value, label }) => label).join(", ")
                }}</span
              >
            </div>
          </template>

          <template v-slot:option="{ option }">
            <span class="text-overflow-ellipsis">{{ option.label }}</span>
          </template>
        </Multiselect>
      </div>
      <!-- Application status -->
      <div
        class="item-filter flex-grow-1 ms-2"
        :class="{
          active: !!props.jobsParams.status,
        }"
      >
        <Multiselect
          :placeholder="translate('job_list_status')"
          :options="props.filterOptions.status"
          v-model="props.jobsParams.status"
        >
          <template v-slot:singlelabel="{ value }">
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("job_list_status") }}:
                {{ value.label }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- Applied date -->
      <div
        class="item-filter flex-grow-1 ms-2"
        :class="{
          active: !!props.jobsParams.created_by,
        }"
      >
        <Multiselect
          :placeholder="translate('job_list_created_by')"
          :options="props.filterOptions.employerName"
          v-model="props.jobsParams.created_by"
        >
          <template v-slot:singlelabel="{ value }">
            <div class="multiselect-multiple-label">
              <span>
                {{ translate("job_list_created_by") }}:
                {{ value.label }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
    </div>
    <!-- Filter reset button -->
    <span
      v-if="!!props.isChangeJob"
      class="btn-reset svg-icon text-topdev-3 d-flex align-items-center ms-4 p-3 mt-0"
      @click="onReset"
    >
      <inline-svg class="me-1" src="assets/icons/cancel.svg" />
      {{ translate("candidate_list_filter_reset") }}
    </span>
  </form>
  <!-- End filter bar  -->
</template>
<script setup lang="ts">
import { ref, toRaw, watch } from "vue";
import { Modal } from "bootstrap";

import { exportJobs } from "@/api/job";
import { showWarningToast, translate, fixWidthMultiselect } from "@/helpers";
import { FilterOptions, JobsParams } from "@/models/jobs";
import { InlineSvg } from "@/plugins";
import Multiselect from "@vueform/multiselect";
import { OptionTitle } from "@/models/candidates";

interface Emit {
  (e: "search", value: string): void;
  (e: "setJobsFilter", value: JobsParams, isChange: boolean): void;
}
const emit = defineEmits<Emit>();

//Define props
interface Props {
  isChangeJob: boolean;
  filterOptions: FilterOptions;
  jobsParams: JobsParams;
}

const props = defineProps<Props>();

//Define data
const searchField = ref("");
const isDownloadBtnLoading = ref(false);

//Function
const onOpenJobFilterModal = () => {
  Modal.getOrCreateInstance("#job-filter-modal").show();
};

const onSearch = () => {
  emit("search", searchField.value);
};

const onReset = () => {
  props.jobsParams.location_id = null;
  props.jobsParams.job_id = null;
  props.jobsParams.skills_id = [];
  props.jobsParams.status = null;
  props.jobsParams.created_by = null;

  emit("setJobsFilter", props.jobsParams, false);
};

const downloadJobs = async () => {
  const dataFilter = { ...props.jobsParams, ...{ query: searchField.value } };

  isDownloadBtnLoading.value = true;
  try {
    const { data } = await exportJobs({ search: dataFilter });
    window.location.href = data.download_url;
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isDownloadBtnLoading.value = false;
  }
};

//Watch
watch(
  () => props.jobsParams,
  (filter) => {
    //Set data from props
    const filterData = {
      location_id: filter.location_id,
      job_id: filter.job_id,
      skills_id: toRaw(filter.skills_id),
      status: filter.status,
      created_by: toRaw(filter.created_by),
    };
    emit("setJobsFilter", filterData, true);
  },
  { deep: true }
);
</script>
