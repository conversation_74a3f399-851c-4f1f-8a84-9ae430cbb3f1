<template>
  <div
    class="modal fade"
    tabindex="-1"
    id="modal-credits-usage"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-body p-5">
          <!--Header modal-->
          <div class="d-flex justify-content-between flex-wrap">
            <div>
              <h2 class="modal-title">
                {{ translate("layout_account_credits_usage") }}
              </h2>
              <span class="modal-sub-title">
                {{ translate("layout_account_credits_usage_information") }}
              </span>
            </div>
            <div class="text-end">
              <div class="modal-title">
                <strong
                  >{{ translate("layout_account_current_balance") }}:
                </strong>
                <span class="number-cre">{{
                  translate("layout_credits_number", [
                    searchResumesStore.credits.toLocaleString("en"),
                  ])
                }}</span>
              </div>
              <button
                @click="onOpenTopUp"
                class="btn btn-topup-credits p-0 modal-sub-title fw-normal text-primary"
              >
                <inline-svg
                  src="/assets/icons/topup.svg"
                  class="me-1"
                ></inline-svg>
                {{ translate("layout_account_top_up_credits") }}
              </button>
            </div>
          </div>
          <hr class="topdev-background-2" />
          <div class="row mb-2 align-items-center">
            <div class="col-md-7">
              <v-date-picker
                v-model="searchResumesStore.timeRangeCreditsUsage"
                is-range
                :model-config="{ type: 'string', mask: 'YYYY-MM-DD' }"
                :masks="{ input: 'DD-MM-YYYY' }"
              >
                <template v-slot="{ inputValue, inputEvents }">
                  <div class="d-flex justify-center align-items-center">
                    <input
                      :value="inputValue.start"
                      v-on="inputEvents.start"
                      class="form-control form-control-solid"
                      :placeholder="translate('candidate_list_form_date')"
                    />
                    <span class="svg-icon svg-icon-2x mx-2">
                      <inline-svg src="/assets/icons/arrow-right.svg" />
                    </span>
                    <input
                      :value="inputValue.end"
                      v-on="inputEvents.end"
                      class="form-control form-control-solid"
                      :placeholder="translate('candidate_list_to_date')"
                    />
                  </div>
                </template>
              </v-date-picker>
            </div>
            <!-- Filter reset button -->
            <div class="col-md-3 ps-0">
              <span
                v-if="searchResumesStore.isChangeSearchCredits"
                class="btn-reset cursor svg-icon text-topdev-3 d-flex align-items-center"
                @click="resetSearchCredits"
              >
                <inline-svg
                  class="me-1 w-15px h-15px"
                  src="/assets/icons/cancel.svg"
                />
                {{ translate("candidate_list_filter_reset") }}
              </span>
            </div>
          </div>
          <hr class="topdev-background-2 mb-0" />
          <!--End header modal-->
          <!--Body modal-->
          <div class="limit-max-height">
            <div class="table-responsive">
              <table v-if="loadHistory" class="table position-relative">
                <tbody v-if="historyCredits.length > 0" id="table-body">
                  <tr
                    v-for="(historyCredit, index) in historyCredits"
                    :key="index"
                    class="border-bottom"
                  >
                    <td class="mw-100px">{{ historyCredit.created_at }}</td>
                    <td class="mw-400px">
                      <p
                        class="mb-0 hover-history-credit"
                        v-html="
                          historyCredit.credit_type == 1
                            ? translate('layout_credits_usage_content_plus', [
                                historyCredit.amount,
                              ])
                            : translate('layout_credits_usage_content_minus', [
                                historyCredit.username,
                                historyCredit.resume_username,
                                historyCredit.amount,
                                historyCredit.resume_id,
                              ])
                        "
                      ></p>
                    </td>
                    <td class="color-credits">
                      {{
                        translate("layout_credits_number", [
                          historyCredit.credit_type == 1
                            ? "+ " + historyCredit.amount
                            : "- " + historyCredit.amount,
                        ])
                      }}
                    </td>
                  </tr>
                </tbody>
                <!-- If jobs empty -->
                <tbody v-else id="table-body">
                  <tr>
                    <td colspan="6" class="text-center">
                      <inline-svg
                        src="/assets/icons/candidates/no-search-result.svg"
                        class="empty-icon"
                      ></inline-svg>
                      <p style="font-size: 16px">
                        {{
                          translate(
                            "candidate_list_you_currently_have_no_candidates"
                          )
                        }}
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- Skeletor  -->
              <div v-else class="py-5">
                <Skeletor />
                <Skeletor />
                <Skeletor />
              </div>
            </div>
          </div>
          <!--End body modal-->
          <hr class="topdev-background-2" />
          <!--Footer modal-->
          <div class="d-flex justify-content-end flex-wrap">
            <!-- Cancel Button  -->
            <button
              type="button"
              class="btn btn-sm btn-secondary"
              data-bs-dismiss="modal"
              @click="onCloseDialog"
            >
              {{ translate("layout_credits_close") }}
            </button>
            <!-- Done Button  -->
          </div>
          <!--End footer modal-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { Modal } from "bootstrap";
import _ from "lodash";

import { useSearchResumesStore, timeRangeCreditsUsageDefine } from "@/stores";
import { translate, showWarningToast } from "@/helpers";
import { fetchShoppingMeHistories } from "@/api/search-resume";
/**
 * Define init
 */
const searchResumesStore = useSearchResumesStore();
const historyCredits = ref([]);
const loadHistory = ref(false);

const resetSearchCredits = () => {
  searchResumesStore.resetTimeRangeCreditsUsage();
};

const onCloseDialog = () => {
  searchResumesStore.resetTimeRangeCreditsUsage();
  Modal.getOrCreateInstance("#modal-credits-usage").hide();
};

const onOpenTopUp = () => {
  Modal.getOrCreateInstance("#modal-credits-usage-topup").show();
  Modal.getOrCreateInstance("#modal-credits-usage").hide();
};

onMounted(async () => {
  try {
    // const { data } = await fetchShoppingMeHistories({
    //   "filters[created_between]": "" + "," + "",
    // });
    // historyCredits.value = data;
  } catch (error) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    loadHistory.value = true;
  }
});

watch(
  () => searchResumesStore.timeRangeCreditsUsage,
  async (range) => {
    if (range === null) return;
    try {
      // var { data } = await fetchShoppingMeHistories({
      //   "filters[created_between]": range?.start + "," + range?.end,
      // });
      // historyCredits.value = data;
      if (!_.isEqual(range, timeRangeCreditsUsageDefine)) {
        searchResumesStore.isChangeSearchCredits = true;
      }
    } catch (error) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    } finally {
      loadHistory.value = true;
    }
  },
  { deep: true }
);
</script>
