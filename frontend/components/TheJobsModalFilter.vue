<template>
  <div
    class="modal fade"
    id="job-filter-modal"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-dialog-centered mw-500px">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{ translate("job_list_filter") }}</h4>
          <button
            type="button"
            class="btn-close"
            aria-label="Close"
            @click="onCloseModal"
          ></button>
        </div>
        <div class="modal-body">
          <div class="my-5">
            <label for="" class="form-label">{{
              translate("job_list_job_title")
            }}</label>
            <Multiselect
              :options="props.filterOptions.jobs"
              v-model="filterFields.job_id"
              mode="single"
              :searchable="true"
              :placeholder="translate('job_list_all_jobs')"
            />
          </div>

          <div class="my-5">
            <label for="" class="form-label">{{
              translate("job_list_job_location")
            }}</label>
            <Multiselect
              :placeholder="translate('job_list_province_city')"
              :options="props.filterOptions.province"
              v-model="filterFields.location_id"
              :searchable="true"
            />
          </div>

          <div class="my-5">
            <label for="" class="form-label">{{
              translate("job_list_skills")
            }}</label>
            <Multiselect
              :placeholder="translate('job_list_skills')"
              :options="props.filterOptions.taxonomiesSkills"
              :searchable="true"
              :hideSelected="false"
              :closeOnSelect="false"
              v-model="filterFields.skills_id"
              mode="tags"
            />
          </div>

          <div class="my-5">
            <label for="" class="form-label">{{
              translate("job_list_status")
            }}</label>
            <Multiselect
              :placeholder="translate('job_list_status')"
              :options="props.filterOptions.status"
              v-model="filterFields.status"
            />
          </div>

          <div class="my-5">
            <label for="" class="form-label">{{
              translate("job_list_created_by")
            }}</label>
            <Multiselect
              :placeholder="translate('job_list_created_by')"
              :options="props.filterOptions.employerName"
              v-model="filterFields.created_by"
            />
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-sm btn-secondary"
            @click="onReset"
          >
            {{ translate("job_list_reset") }}
          </button>
          <button type="button" class="btn btn-sm btn-primary" @click="onApply">
            {{ translate("job_list_apply") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, toRaw } from "vue";
import Multiselect from "@vueform/multiselect";
import { Modal } from "bootstrap";

import { translate } from "@/helpers";
import { JobsParams, FilterOptions } from "@/models/jobs";

//Interface
interface Emits {
  (e: "setJobsFilter", value: JobsParams, isChange: boolean): void;
}

const emit = defineEmits<Emits>();

//Define props for options
interface Props {
  filterOptions: FilterOptions;
  jobsParams: JobsParams;
}
const props = defineProps<Props>();

//Define data
const filterFields = reactive<JobsParams>({
  location_id: props.jobsParams.location_id,
  job_id: props.jobsParams.job_id,
  skills_id: props.jobsParams.skills_id,
  status: props.jobsParams.status,
  created_by: props.jobsParams.created_by,
});

//Function
const onCloseModal = () => {
  props.jobsParams.location_id = null;
  props.jobsParams.job_id = null;
  props.jobsParams.skills_id = [];
  props.jobsParams.status = null;
  props.jobsParams.created_by = null;

  Modal.getOrCreateInstance("#job-filter-modal").hide();
};
const onReset = () => {
  onCloseModal();
  emit("setJobsFilter", props.jobsParams, false);
};

const onApply = () => {
  const filter = {
    job_id: filterFields.job_id,
    status: filterFields.status,
    location_id: filterFields.location_id,
    skills_id: toRaw(filterFields.skills_id),
    created_by: toRaw(filterFields.created_by),
  };
  emit("setJobsFilter", filter, true);
  Modal.getOrCreateInstance("#job-filter-modal").hide();
};
//watch
watch(
  () => props.jobsParams,
  (filterData) => {
    //Set data from props
    filterFields.job_id = filterData.job_id;
    filterFields.status = filterData.status;
    filterFields.location_id = filterData.location_id;
    filterFields.skills_id = filterData.skills_id;
    filterFields.created_by = filterData.created_by;
  },
  { deep: true }
);
</script>
