<template>
    <div class="example">
        <apexcharts 
            width="100%" 
            height="455" 
            type="bar" 
            :options="computedChartOptions" 
            :series="series"
        ></apexcharts>
    </div>
</template>

<script lang="ts">
import { SeriesItem } from "@/models/dashboard";
import { defineComponent, computed, PropType } from "vue";
import VueApexCharts from "vue3-apexcharts";



export default defineComponent({
    name: "ChartBar",
    components: {
        apexcharts: VueApexCharts,
    },
    props: {
        categories: {
            type: Array as PropType<string[]>,
            required: true
        },
        series: {
            type: Array as PropType<SeriesItem[]>,
            required: true
        }
    },
    setup(props) {
        const computedChartOptions = computed(() => {
            const columnWidth = props.categories.length > 20 ? "18px" : "32px";

            return {
                chart: {
                    id: "basic-bar",
                    type: "bar",
                    stacked: true,
                    toolbar: { show: false },
                    zoom: { enabled: true }
                },
                xaxis: {
                    categories: props.categories
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadiusApplication: "end",
                        borderRadiusWhenStacked: "last",
                        columnWidth: columnWidth,
                        dataLabels: {
                            position: "top",
                            total: {
                                enabled: true,
                                style: { fontSize: "12px", fontWeight: 400 }
                            }
                        }
                    }
                },
                grid: {
                    strokeDashArray: 2,
                    xaxis: { lines: { show: true, strokeDashArray: 4 } },
                    yaxis: { lines: { show: true, strokeDashArray: 4 } }
                },
                dataLabels: {
                    enabled: true,
                    style: { fontSize: "12px", fontWeight: "400", colors: ["#fff"] }
                }
            };
        });

        return { computedChartOptions };
    }
});
</script>
