<template>
  <div class="d-flex flex-column justify-content-start job-posting-usage">
    <div class="d-flex align-items-center gap-1">
      <inline-svg src="/assets/icons/clock-nine.svg" width="20" height="20" />
      <h2 class="m-0 credit-title text-2xl">
        {{ translate("usage_history_job_posting_usage_log") }}
      </h2>
    </div>
    <div>
      <table class="table package-table table-sm table-hover">
        <tbody ref="tableUsageBodyRef" :class="{ 'table-data-empty': !usages.data || usages.data.length == 0 }">
          <tr v-if="usages.data && usages.data.length" class="border-bottom border-gray-300"
            v-for="(item, index) in usages.data" :key="index">
            <td class="text-gray-500" style="width: 25%">
              <span class="text-black font-bold unset">
                {{ convertDatetime(item.created_at, "DD-MM-YYYY") }}
              </span>
              {{ convertDatetime(item.created_at, "hh:mm:ss") }}
            </td>
            <td style="width: 50%"
              v-html="translate('usage_history_job_posting_usage_log_use_description', [`<a href='${item.job_detail}' target='_blank'>${item.job_title}</a>`])">
            </td>
            <td class="job-package-title" style="width: 25%">-1 {{ item.package_name }}</td>
          </tr>
          <!-- If invoices is empty -->
          <tr v-else id="table-body-empty">
            <td colspan="3" class="text-center">
              <inline-svg src="/assets/icons/empty-file.svg" class="empty-icon"></inline-svg>
              <p style="font-size: 16px">
                {{
                  translate("usage_history_job_posting_not_found_usage_logs")
                }}
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { translate } from "@/helpers";
import { convertDatetime } from "@/helpers/date";
import { JobPackageUsageResponse } from "@/models/usage-history";
import { onMounted, ref, watch } from "vue";

const tableUsageBodyRef = ref<HTMLDivElement>(null);
const isLoadingData = ref<boolean>(false);

type Usage = {
  usages: JobPackageUsageResponse
}

type Emit = {
  (e: 'loadMore', value: number);
}

const props = defineProps<Usage>();
const emit = defineEmits<Emit>();

onMounted(() => {
  handleLoadMorePackage();
});

/**
 * Watch data
 */
watch(
  () => props.usages,
  () => {
    isLoadingData.value = false;
  },
  { deep: true }
);

const handleLoadMorePackage = () => {
  if (tableUsageBodyRef.value) {
    const element = tableUsageBodyRef.value;
    tableUsageBodyRef.value.addEventListener("scroll", async () => {
      if (
        element.scrollHeight - element.scrollTop >= element.clientHeight &&
        props.usages.current_page < props.usages.last_page &&
        !isLoadingData.value
      ) {
        // is scroll to bottom && not last page && not loading
        isLoadingData.value = true;
        emit('loadMore', props.usages.current_page + 1);
      }
    });
  }
};

</script>
