<template>
  <div
    :class="[
      isSearchResumeDetail ? 'detail-candidate justify-content-between' : '',
    ]"
    class="container-filter d-flex align-items-center mb-3 w-100"
  >

    <!-- All Search Resumes Page -->
    <form
      v-if="!isSearchResumeDetail"
      class="container-filter-box d-flex flex-wrap align-items-center gap-4"
      id="container-filter-box"
      autocomplete="off"
    >
      <!-- Skills  -->
      <div class="item-filter custom-checkbox-filter checkbox-skills" :class="{ active: filterData.skill.length > 0 }">
        <Multiselect
          :options="taxonomiesStore.skills"
          v-model.lazy="filterData.skill"
          mode="multiple"
          :placeholder="translate('candidate_list_skills')"
          :searchable="true"
          autocomplete="off"
          :hideSelected="false"
          :closeOnSelect="false"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template v-slot:multiplelabel="{ values }: ValuesType">
            <div class="multiselect-multiple-label">
              <span>
                {{
                  values.length > 2
                    ? translate("candidate_list_skills") + ": "
                    : ""
                }}
                {{
                  values.length > 2
                    ? `(+${values.length})`
                    : values.map(({ value, label }) => label).join(", ")
                }}
              </span>
            </div>
          </template>
          <!-- Placeholder -->
          <template v-slot:placeholder="value">
            <div class="multiselect-placeholder">
              {{ translate("candidate_list_skills") }}
            </div>
          </template>
          <template v-slot:option="{ option }: { option: { label: string, value: string } }">
            <div class="custom-option">
              <input type="checkbox" :checked="filterData.skill.includes(option.value)">
              <span style="margin-left: 8px" class="text-overflow-ellipsis"> {{ option.label }}</span>
            </div>
          </template>
        </Multiselect>
      </div>

      <!-- Experience  -->
      <div class="item-filter custom-checkbox-filter checkbox-experiences"
           :class="{ active: filterData.experience.length > 0 }">
        <Multiselect
          :placeholder="translate('candidate_list_work_experience')"
          :options="taxonomiesStore.experiences"
          mode="multiple"
          v-model.lazy="filterData.experience"
          :searchable="true"
          autocomplete="off"
          :hideSelected="false"
          :closeOnSelect="false"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template v-slot:multiplelabel="{ values }: ValuesType">
            <div class="multiselect-multiple-label">
              <span>
              {{
                  values.length > 2
                    ? translate("candidate_list_work_experience") + ": "
                    : ""
                }}
                {{
                  values.length > 2
                    ? `(+${values.length})`
                    : values.map(({ value, label }) => label).join(", ")
                }}
              </span>
            </div>
          </template>
          <template v-slot:option="{ option }: { option: { label: string, value: string } }">
            <div class="custom-option">
              <input type="checkbox" :checked="filterData.experience.includes(option.value)">
              <span style="margin-left: 8px" class="text-overflow-ellipsis">{{ option.label }}</span>
            </div>
          </template>
        </Multiselect>
      </div>

      <!-- Candidate location -->
      <div
        class="item-filter"
        :class="{
          active: !!filterData.location,
        }"
      >
        <Multiselect
          :placeholder="translate('search_resumes_location')"
          :options="locationOptions"
          v-model="filterData.location"
          :searchable="true"
          :hideSelected="false"
          @searchChange="(query: string,select$: any) => fixWidthMultiselect(query,select$)"
        >
          <template
            v-slot:singlelabel="{ value }: { value: { label: string } }"
          >
            <div class="multiselect-multiple-label">
              <span>
                {{ `${value.label}` }}
              </span>
            </div>
          </template>
        </Multiselect>
      </div>
      <!-- More Filter -->
      <div
        class="btn-reset d-flex align-items-center font-semibold"
        @click="onOpenFilterModal"
      >
        <div class="d-inline-block w-6 h-6">
          <inline-svg src="/assets/icons/plus.svg" width="16" height="16" />
        </div>
        <span>{{ translate("search_resumes_more_filters") }}</span>
      </div>
      <!-- End more filter  -->
    </form>

    <!-- Detail Search Resumes Page  -->
    <div
      v-else-if="isSearchResumeDetail && searchResumesStore.isChange"
      class="container-filter-box d-flex align-items-center"
      id="container-filter-box"
    >
      <!-- Skills  -->
      <swiper :spaceBetween="6" :slidesPerView="'auto'">
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{ active: filterData.skill.length > 0 }"
          >
            <Multiselect
              :options="taxonomiesStore.skills"
              v-model="filterData.skill"
              mode="multiple"
              :searchable="true"
              :hideSelected="false"
              :closeOnSelect="false"
              autocomplete="off"
            >
              <template v-slot:multiplelabel="{ values }: ValuesType">
                <div class="multiselect-multiple-label">
                  <span>
                    {{ translate("candidate_list_skills") }}:
                    {{
                      values.length > 2
                        ? `(+${values.length})`
                        : values.map(({ value, label }) => label).join(", ")
                    }}
                  </span>
                </div>
              </template>
              <template v-slot:option="{ option }: OptionType">
                <span class="text-overflow-ellipsis">{{ option.label }}</span>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Experience  -->
        <swiper-slide class="w-auto">
          <div class="item-filter" :class="{ active: filterData.experience.length > 0 }">
            <Multiselect
              :options="taxonomiesStore.experiences"
              v-model="filterData.experience"
              :placeholder="translate('candidate_list_experiences')"
              :searchable="true"
              :hideSelected="false"
              :closeOnSelect="false"
              autocomplete="off"
              mode="multiple"
            >
              <template v-slot:option="{ option }: { option: { label: string } }">
                <span class="text-overflow-ellipsis">{{ translate(option.label) }}</span>
              </template>
              <template v-slot:multiplelabel="{ values }: ValuesType">
                <div class="multiselect-multiple-label">
                      <span>
                        {{ translate("candidate_list_experiences") }}:
                        {{
                          values.length > 2
                            ? `(+${values.length})`
                            : values.map(({ value, label }) => translate(label)).join(", ")
                        }}
                      </span>
                </div>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!--  CV’s languages -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: !!filterData.candidate_language,
            }"
          >
            <Multiselect
              :options="cvLanguagesOption"
              v-model="filterData.candidate_language"
            >
              <template
                v-slot:singlelabel="{ value }: { value: { label: string } }"
              >
                <div class="multiselect-multiple-label">
                  <span>
                    {{ `${value.label}` }}
                  </span>
                </div>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>

        <!-- Candidate location -->
        <swiper-slide class="w-auto">
          <div
            class="item-filter"
            :class="{
              active: !!filterData.location,
            }"
          >
            <Multiselect
              :options="locationsOption"
              v-model="filterData.location"
            >
              <template
                v-slot:singlelabel="{ value }: { value: { label: string } }"
              >
                <div class="multiselect-multiple-label">
                  <span>
                    {{ `${value.label}` }}
                  </span>
                </div>
              </template>
              <template v-slot:option="{ option }: OptionType">
                <span class="text-overflow-ellipsis">
                  {{ option.label }}
                </span>
              </template>
            </Multiselect>
          </div>
        </swiper-slide>
      </swiper>
    </div>

    <!-- Filter Reset Button -->
    <div
      v-if="searchResumesStore.isChange"
      class="btn-reset text-topdev-3 d-flex align-items-center ms-4 btn-reset-filter"
      @click="onFilterReset"
    >
      <inline-svg
        class="me-1"
        src="/assets/icons/cancel.svg"
        width="16"
        height="16"
      />
      {{ translate("candidate_list_filter_reset") }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, reactive, ref } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import Multiselect from "@vueform/multiselect";

import { useSearchResumesStore, useTaxonomiesStore } from "@/stores";
import { translate, fixWidthMultiselect, updateQueryParam } from "@/helpers";
import { ResumesFilterParams, Option } from "@/models/search-resumes";
import { ValuesType, OptionType } from "@/models/candidates";
import { TaxonomiesKey } from "@/stores/taxonomies";

interface Props {
  filterData: ResumesFilterParams;
  isSearchResumeDetail: boolean;
  locationsOption: Array<Option>;
  cvLanguagesOption: Array<Option>;
}

interface Emits {
  (e: "reset");

  (e: "openFilterModal");
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const taxonomiesStore = useTaxonomiesStore();
const searchResumesStore = useSearchResumesStore();

let filterData = reactive(props.filterData);
const locationOptions = ref(props.locationsOption);

const onFilterReset = () => {
  emits("reset");
};

const onOpenFilterModal = () => {
  emits("openFilterModal");
};

const updateURLFilterParams = (filterParams: ResumesFilterParams) => {
  updateQueryParam("skill", filterParams.skill.join(","));
  updateQueryParam("experience", filterParams.experience.join(","));
  updateQueryParam("language", filterParams.language);
  updateQueryParam("location", filterParams.location);
  updateQueryParam("candidate_language", filterParams.candidate_language);
  updateQueryParam("timeRange_start", filterParams.timeRange.start);
  updateQueryParam("timeRange_end", filterParams.timeRange.end);
};

const getSortMultipleSelectedData = (
  field: TaxonomiesKey,
  arrCompare: string[]
) => {
  return taxonomiesStore[field].sort((a, b) => {
    const aInFilter = arrCompare.includes(a.value);
    const bInFilter = arrCompare.includes(b.value);
    if (aInFilter && bInFilter) {
      return 0;
    } else if (aInFilter) {
      return -1;
    } else if (bInFilter) {
      return 1;
    } else {
      return 0;
    }
  });
};

const getSortSingleSelectedData = (field: TaxonomiesKey, valueCompare) => {
  return taxonomiesStore[field].sort((a, b) => {
    return a.value === valueCompare ? -1 : b.value === valueCompare ? 1 : 0;
  });
};
const getSortSingleSelectedLocationData = (valueCompare) => {
  return props.locationsOption.sort((a, b) => {
    return a.value === valueCompare ? -1 : b.value === valueCompare ? 1 : 0;
  });
};

watch(
  () => props.locationsOption,
  (value) => {
    locationOptions.value = value;
  },
  { deep: true }
);

watch(
  () => props.filterData,
  (filter) => {
    filterData = filter;

    // Sort filters after select
    taxonomiesStore.setSkillsData(
      getSortMultipleSelectedData("skills", filterData.skill)
    );
    taxonomiesStore.setExperiencesData(
      getSortSingleSelectedData("experiences", filterData.experience)
    );
    locationOptions.value = getSortSingleSelectedLocationData(
      filterData.location
    );
  },
  { deep: true }
);
</script>
