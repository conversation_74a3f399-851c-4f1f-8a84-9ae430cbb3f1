import moment from "moment/moment";

/**
 * Converts a given datetime string to the specified format using moment.js.
 *
 * @param {string} date - The datetime string to be converted.
 * @param {string} [format="DD/MM/YYYY"] - The format to which the datetime string should be converted. Defaults to "DD/MM/YYYY".
 * @return {string} The converted datetime string.
 */
export const convertDatetime = (
    date?: string,
    format: string = "DD/MM/YYYY"
): string => {
    return date ? moment(date).format(format) : 'Unknown';
};