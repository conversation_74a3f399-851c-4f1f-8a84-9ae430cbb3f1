import swal from "sweetalert2";
import { translate } from "@/helpers";

export const showConfirmMixin = swal.mixin({
  customClass: {
    container: "topdev-confirm",
  },
});

const showUnlockResumeConfirmMixin = swal.mixin({
  showCancelButton: true,
  customClass: {
    container: "unlock-candidate-confirm",
  },
});

const showConfirmWaitingApproved = swal.mixin({
  customClass: {
    container: "topdev-confirm-waiting-approved topdev-confirm"
  }
});

const showAvailableUnlockResumeConfirmMixin = swal.mixin({
  showCancelButton: true,
  customClass: {
    container: "unlock-candidate-confirm",
  },
});

export const confirmNavigation = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_havent_saved_your_changes"),
    text: translate("swal_confirm_changes_not_saved"),
    showCancelButton: true,
    confirmButtonText: translate("swal_confirm_leave_page"),
    cancelButtonText: translate("swal_confirm_stay_on_this_page"),
  });
};

export const confirmUserWaitingApproved = async () => {
  return await showConfirmWaitingApproved.fire({
    title: translate("swal_confirm_access_denied"),
    confirmButtonText: translate("swal_confirm_waiting_approved_ok"),
    html: translate("swal_confirm_waiting_approved"),
    allowOutsideClick: false,
  });
};

export const confirmUserDenied = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_access_denied"),
    confirmButtonText: translate("swal_confirm_logout"),
    html: translate("swal_confirm_contact_to_active_account"),
    allowOutsideClick: false,
  });
};

export const confirmSessionExpired = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_session_has_expired"),
    confirmButtonText: translate("swal_confirm_login"),
    html: translate("swal_confirm_please_login_again"),
    allowOutsideClick: false,
  });
};

export const confirmCancelForm = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_changes_will_lost"),
    text: translate("swal_confirm_action_cannot_undone"),
    showCancelButton: true,
    confirmButtonText: translate("swal_confirm_discard_changes"),
    cancelButtonText: translate("swal_confirm_cancel"),
  });
};

export const confirmUnlockResume = async (
  price: number,
  currentCredits: number
) => {
  return await showAvailableUnlockResumeConfirmMixin.fire({
    html: translate('swal_confirm_available_unlock_title', [currentCredits.toLocaleString(
      "en"
    ), price]),
    imageUrl: "/assets/images/unlock-candidate-info.png",
    showCancelButton: true,
    cancelButtonText: translate('swal_confirm_button_cancel'),
    confirmButtonText: translate('swal_confirm_unlock_now'),
  });
};

export const notificationNotEnoughCredits = async (
  price: number,
  currentCredits: number
) => {
  return await showUnlockResumeConfirmMixin.fire({
    html: translate('swal_confirm_not_enough_credits',  [currentCredits.toLocaleString(
      "en"
    ), price]),
    imageUrl: "/assets/images/not-enough-credits.png",
    cancelButtonText: translate('swal_confirm_thanks_maybe_later'),
    confirmButtonText: translate('search_resumes_send_me_information'),
  });
};

export const confirmSendInformationTopup = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_send_information"),
    text: translate("swal_confirm_action_cannot_undone_send_information"),
    showCancelButton: true,
    confirmButtonText: translate("swal_confirm_send"),
    cancelButtonText: translate("swal_confirm_cancel"),
  });
};

export const confirmChangeStatusCandidates = async () => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_title_change_status_candidates"),
    text: translate("swal_confirm_change_status_candidates"),
    showCancelButton: true,
    confirmButtonText: translate("swal_confirm_button_change"),
    cancelButtonText: translate("swal_confirm_button_cancel"),
  });
};

export const confirmRequestRefund = async (
  reason: string,
  onChangeReason: (value: string) => void
) => {
  return await showConfirmMixin.fire({
    title: translate("swal_confirm_session_has_expired"),
    confirmButtonText: translate("swal_confirm_request_refund"),
    html: translate("swal_confirm_please_login_again"),
    allowOutsideClick: false,
  });
};
