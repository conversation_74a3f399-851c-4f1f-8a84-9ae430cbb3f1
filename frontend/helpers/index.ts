import { QueryParamsType, ResumesFilterParams } from "@/models/search-resumes";
import { translate } from "./i18n";
import moment from "moment";
import { format } from "date-fns";

moment.locale("vi");

export * from "./auth";
export * from "./confirm";
export * from "./i18n";
export * from "./toast";

export function scrollToTop() {
  document.body.scrollTop = 0; // For Safari
  document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
}

export const scrollToFirstValidationError = () => {
  const el = document.getElementsByClassName("invalid-feedback");
  if (el && el.length >= 0) {
    el[0].scrollIntoView(false);
  }
};

/**
 * Format salary from, to or upto
 *
 * @param min
 * @param max
 * @param currency
 */
const formatSalaryMinMaxDisplay = (min, max, currency) => {
  min = parseInt(min);
  max = parseInt(max);

  if (!min && !max) {
    return "";
  }

  if (!min && max) {
    return "Up to " + formatMoney(max, currency);
  }

  if (min && (!max || max == 0)) {
    return "From " + formatMoney(min, currency);
  }

  return formatMoney(min, currency) + " to " + formatMoney(max, currency);
};

export function formatMoney(value, currency) {
  if (currency === "VND") {
    return Intl.NumberFormat("vi-VN").format(value) + " VND";
  }

  return "USD " + Intl.NumberFormat("en-US").format(value);
}

export function formatSalary(salary) {
  if (parseInt(salary.is_negotiable) === 1) {
    const value = formatSalaryMinMaxDisplay(
      salary.min_estimate,
      salary.max_estimate,
      salary.currency_estimate
    );

    if (value) {
      return "Negotiable - " + value + " (estimation)";
    }

    return "Negotiable";
  }

  return formatSalaryMinMaxDisplay(salary.min, salary.max, salary.currency);
}

export function calculateDaysFromNow(dayParame: string) {
  const callFormatString = "YYYY,0,DD";
  const returnFormatString = "DD[-]MM[-]YYYY";
  //Custom moment calendar
  const formatDate = moment(dayParame).calendar({
    lastDay: "[Yesterday]",
    sameDay: "[Today]",
    lastWeek: function (now) {
      //Define today
      const today = moment(now).format(callFormatString).split(",");
      //Define date parame
      const date = moment(dayParame).format(callFormatString).split(",");
      //Count dated
      const getDay = moment(today).diff(moment(date), "days");
      if (getDay == 2 || getDay == 3)
        return `[${getDay} ${translate("search_resumes_days_ago")}]`;
      return returnFormatString;
    },
    sameElse: returnFormatString,
  });
  return formatDate;
}

export function formatDateMMYYYY(date) {
  if (!date) return null;

  const d = new Date(date);
  return `${d.getMonth() + 1}/${d.getFullYear()}`;
}

export function fixWidthMultiselect(query: string, select: any) {
  if (query.length > 0) {
    const width = select.$el.offsetWidth;
    select.$el.style.minWidth = `${width}px`;
    select.multiselect.classList.add("is-active-custum");
  } else {
    select.$el.style.minWidth = "";
    select.multiselect.classList.remove("is-active-custum");
  }
  return;
}

export const updateQueryParam = (key: QueryParamsType, value: string) => {
  const url = new URL(window.location.href);
  const searchParams = url.searchParams;
  if (!value) {
    searchParams.delete(key);
  } else {
    searchParams.set(key, value);
  }
  let decodeUrl = searchParams.toString();
  if (key !== "keyword") {
    decodeUrl = decodeURIComponent(searchParams.toString());
  }

  const newUrl = `${window.location.pathname}?${decodeUrl}${window.location.hash}`;

  history.pushState({ path: newUrl }, "", newUrl);
};

export const getQueryParam = (key: QueryParamsType) => {
  const url = new URL(window.location.href);
  const searchParams = url.searchParams;
  let value = searchParams.get(key);
  if (!value) {
    return null;
  }
  if (key === "keyword") {
    value = decodeURIComponent(value);
  }
  return value;
};

export const formatDate = (date: Date): string => {
  return format(date, "yyyy/MM/dd")
};