import swal from "sweetalert2";

export const showToast = swal.mixin({
  toast: true,
  position: "top-end",
  showConfirmButton: false,
  timer: 3000,
  customClass: {
    container: "max-index",
  },
  timerProgressBar: true,
  didOpen: (toast: any) => {
    toast.addEventListener("mouseenter", swal.stopTimer);
    toast.addEventListener("mouseleave", swal.resumeTimer);
  },
});

export const showSuccesToast = (
  title: string,
  message: string,
  callback = null
) => {
  showToast
    .fire({
      title: title,
      icon: "success",
      text: message,
    })
    .then(callback);
};

export const showWarningToast = (
  title: string,
  message: string,
  callback = null
) => {
  showToast
    .fire({
      title: title,
      icon: "warning",
      text: message,
    })
    .then(callback);
};
