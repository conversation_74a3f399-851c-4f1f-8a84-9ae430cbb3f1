
import { createUnlockResumeDraft } from "@/api/search-resume";
import { showWarningToast, translate, updateQueryParam } from "@/helpers";
import { ResumesParams } from "@/models/search-resumes";

export const onCreateUnlockResumeDraft = async (resumeId: number, credit: number) => {
  try {
    let { data } = await createUnlockResumeDraft(resumeId, credit);
    return data.key;
  } catch (error) {
    const codes = error.response?.data?.errors?.code;
    let errorTitle = 'toast_something_went_wrong',
      errorMessage = 'toast_save_failed_message';

    if (codes.includes(3)) {
      errorMessage = 'search_resumes_resume_unlock_before';
    } else if (codes.includes(4)) {
      errorMessage = 'search_resumes_toast_not_enough_credit';
    }

    showWarningToast(translate(errorTitle), translate(errorMessage));
    throw error;
  }
};

export const updateQueryURL = (params: ResumesParams) => {
  updateQueryParam("showWishList", params.showWishList ? "1" : "");
  updateQueryParam("keyword", params.keyword.join(","));
  updateQueryParam(
    "showResumesUnlocked",
    params.showResumesUnlocked ? "1" : ""
  );
  updateQueryParam("skill", params.filter.skill.join(","));
  updateQueryParam("experience", params.filter.experience.join(","));
  updateQueryParam("language", params.filter.language);
  updateQueryParam("location", params.filter.location);
  updateQueryParam("candidate_language", params.filter.candidate_language);
  updateQueryParam("timeRange_start", params.filter.timeRange.start);
  updateQueryParam("timeRange_end", params.filter.timeRange.end);
  updateQueryParam("page", params.page.toString());
  updateQueryParam("page_size", params.page_size.toString());
};
