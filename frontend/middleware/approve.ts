import { confirmUserWaitingApproved } from "@/helpers";
import { useAuthStore } from "@/stores";

export default async function approve(to, from, next) {
  let user = useAuthStore().user;

  if (!user.id) {
    user = await useAuthStore().getUserInfo();
  }

  if (user.id && user.approved === false) {
    await confirmUserWaitingApproved();

    return next({
      name: "company",
    });
  }

  return next();
}
