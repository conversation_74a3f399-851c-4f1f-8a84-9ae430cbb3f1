.button-group-switch {
  /* Auto layout */
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 2px;
  gap: 8px;

  /* neutral/200 */
  background: #d1d1d1;
  border-radius: 4px;

  button {
    border: none;

    /* Medium/Bold* */
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    /* identical to box height, or 138% */
    display: flex;
    align-items: center;
    text-align: center;
    letter-spacing: 0.01em;

    /* neutral/600 */
    color: #5d5d5d;
    border-radius: 4px;
    background: none;
  }

  #btn-paid, #btn-usd {
    /* Auto layout */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 16px;

    /* Inside auto layout */
    flex: none;
    order: 0;
    flex-grow: 1;
  }

  #btn-free, #btn-vnd {
    /* Auto layout */
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 16px;

    /* Inside auto layout */
    flex: none;
    order: 1;
    flex-grow: 1;
  }
}

.banner-card {
  display: flex;
  width: 242px;
  padding: var(--spacing-md, 8px);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;

  border-radius: 4px;
  border: 1px solid var(--Grey-200, #dbdbdb);
  background: var(--Grey-White, #fff);

  &.banner-card__selected {
    background-color: #FEE6E2;
  }

  .banner-card__info {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .banner-card__buttons {
      display: flex;
      align-items: center;
      justify-content: center;

      button {
        display: flex;
        height: 26px;
        padding: 16px 32px;
        justify-content: center;
        align-items: center;
        gap: 12px;
      }
    }
  }
}

.color-selector-container {
  display: flex;
  align-items: center;
  gap: 10px;
  align-self: stretch;

  .color-selector {
    display: flex;
    width: 36px;
    height: 36px;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: none;
    padding: 0;

    span {
      display: block;
      width: 60%;
      height: 60%;
      background: white;
      border-radius: 50%;
    }
  }
}

.progress-list {
  list-style-type: none;
  padding-left: 0;

  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;

  li {
    display: flex;
    padding: var(--spacing-xs, 4px) var(--spacing-xs, 4px)
      var(--spacing-xs, 4px) var(--spacing-sm, 8px);
    justify-content: space-between;
    align-items: center;
    flex: 1 0 0;
    align-self: stretch;

    border-radius: 4px;
    background: var(--Blue-50, #eef8ff);

    p {
      color: var(--neutral-900, #3d3d3d);
      font-feature-settings: "liga" off, "clig" off;

      /* Small/Bold* */
      font-family: Roboto;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px; /* 142.857% */
      letter-spacing: 0.14px;

      margin-bottom: 0;
    }

    span {
      display: block;
      border-radius: var(--spacing-xs, 4px);
      background: var(--neutral-400, #888);
      padding: 0px var(--spacing-xs, 4px);

      &.success {
        background: var(--Green-600, #16a34a);
      }

      svg {
        width: 24px;
        height: 24px;
      }
    }
  }
}

.item-text {
  width: 258px;
}

.input-taxonomies-select-container {
  display: flex;
  padding: 8px 0px 8px 16px;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 4px;
  background: var(--neutral-200, #d1d1d1);

  .select-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1 0 0;
  }

  .plus-container {
    display: flex;
    align-items: center;
    align-self: stretch;
    padding: 0 12px;

    .btn-plus {
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}

.banner-select-container {
  display: flex;
  gap: 8px;
}

#progress-container {
  position: sticky;
  top: 200px;
}

.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  z-index: 10;
  margin-top: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-result-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    color: #ff3d00;
    font-weight: 500;
  }
}

.input-taxonomies-select-container {
  .select-container {
    input {
      width: 100%;
      border: none;
      background: transparent;

      &:focus {
        outline: none;
      }
    }
  }
}
