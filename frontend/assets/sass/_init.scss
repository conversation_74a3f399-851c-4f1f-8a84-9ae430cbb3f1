//
// Main init file of global bootstrap and theme functions, mixins, variables and config
//

// Custom functions & mixins
@import "./core/base/functions";
@import "./core/base/mixins";
@import "./core/components/mixins";
@import "./core/vendors/plugins/mixins";

// Custom variables
@import "./core/components/variables";

// Bootstrap initializaton
@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";
@import "~bootstrap/scss/mixins";
@import "~bootstrap/scss/utilities";

// Font awesome
//@import "~@fortawesome/fontawesome-free/css/all.min.css";
@import "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css";

// Multiselect
@import "~@vueform/multiselect/themes/default.css";

// Skeleton
@import '~vue-skeletor/dist/vue-skeletor.css';

//Tagify
@import '@yaireo/tagify/dist/tagify.css';

//V-calendar
@import 'v-calendar/dist/style.css';

//Swiper
@import 'swiper/css';
@import 'swiper/css/scrollbar';

// 3rd-Party plugins variables
@import "./core/vendors/plugins/variables";

// Custom layout variables
@import "./layout/variables";

// TODO: Do we need docs?
//@import "./core/layout/docs/variables";

// Topdev Custom Variables
@import "./custom/variables";
