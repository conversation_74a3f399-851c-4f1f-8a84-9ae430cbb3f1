//
// Main
//

// Body
body {
  background-color: $page-bg;
}

// Font color from Content background color
.text-page-bg {
  color: $page-bg;
}

// Desktop Mode
@include media-breakpoint-up(lg) {
  // Containers
  .container,
  .container-fluid {
    padding: 0 get($content-spacing, desktop);
  }

  // Wrapper
  .wrapper {
    transition: padding-left get($aside-config, transition-speed) ease;
    padding-top: get($header-config, height, desktop);
    padding-left: get($aside-config, width);

    // Aside default enabled, aside fixed and aside minimize modes
    [data-kt-aside-minimize="on"] & {
      transition: padding-left get($aside-config, transition-speed) ease;
      padding-left: 80px;
    }
  }
}

// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  // Containers
  .container,
  .container-fluid {
    max-width: none;
    padding: 0 get($content-spacing, tablet-and-mobile);
  }

  // Wrapper
  .wrapper {
    transition: padding-left 0.3s ease;

    // Fixed header mode
    .header-tablet-and-mobile-fixed & {
      padding-top: get($header-config, height, tablet-and-mobile);
    }

    .page-title {
      margin-left: 20px;
    }
  }
}

.wrapper-custom {
  padding-left: 265px;
  @include media-breakpoint-up(lg) {
    padding-top: 74px;
  }
  [data-kt-aside-minimize="on"] & {
    padding-left: 82px;
    padding-top: 60px;
    @include media-breakpoint-up(lg){
      padding-top: 74px;
    }
  }
}
