//
// Layout Config
//

// Root font Sizes
$root-font-size: 13px; // Root font size for desktop mode
$root-font-size-lg: 13px; // Root font size for tablet mode
$root-font-size-md: 12px; // Root font size for mobile mode

// Page bg
$page-bg: if(isDarkMode(), #151521, $gray-100) !default;

// Content border radius
$content-border-radius: 1.5rem !default;

// Config collapse menu
$menu-collapse-config: (
  width: 82px,
) !default;

// Content Spacing
$content-spacing: (
  desktop: 30px,
  // Padding for desktop mode
  tablet-and-mobile: 15px // Padding for tablet and mobile modes,,
) !default;

// Header
$header-config: (
  height: (
    desktop: 74px,
    tablet-and-mobile: 60px,
  ),
  bg-color: if(isDarkMode(), #1e1e2d, $white),
  border-bottom: if(isDarkMode(), 1px solid #2d2d43, 0),
  z-index: 115,
  box-shadow: if(isDarkMode(), none, 0px 10px 30px 0px rgba(82, 63, 105, 0.05)),
) !default;

// Aside
$aside-config: (
  width: 265px,
  // Aside width for desktop mode
  z-index: 101,
  // Aside's z-index property
  transition-speed: 0.3s,
  // transition speed
  padding-x: 25px,
  footer-padding-x: 15px,
  menu-padding-x: 10px,
  menu-indention: 0.75rem,
  bg-color: #1e1e2d,
  scrollbar-color: #4d4d69,
  scrollbar-hover-color: lighten(#4d4d69, 2%),
) !default;

// Aside
$footer-config: (
  bg-color: if(isDarkMode(), #1b1b28, $white),
) !default;
