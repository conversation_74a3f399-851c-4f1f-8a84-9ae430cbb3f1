//
// Main
//

// General mode
.app-main {
  display: flex;
}

// Desktop mode
@include media-breakpoint-up(lg) {
  // Integration
  .app-main {
    @include app-layout-transition();

    [data-kt-app-aside-sticky="true"] & {
      margin-left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px)
      );
    }

    // Aside Panel
    [data-kt-app-aside-panel-sticky="true"] & {
      margin-left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px) + var(--kt-app-aside-panel-width) +
          var(--kt-app-aside-panel-gap-start, 0px) +
          var(--kt-app-aside-panel-gap-end, 0px)
      );
    }
  }
}
