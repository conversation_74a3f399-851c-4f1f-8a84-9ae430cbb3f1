//
// Variables
//

// Layouts
$app-body-bg-color: null !default;
$app-blank-bg-color: null !default;
$app-default-bg-color: null !default;

// General
$app-general-transition-duration: 0.3s !default;
$app-general-transition-timing: ease !default;
$app-general-root-font-size-desktop: 13px;
$app-general-root-font-size-tablet: 12px;
$app-general-root-font-size-mobile: 12px;

// Container
$app-container-padding-x: 30px;
$app-container-padding-x-mobile: 20px;

// Content
$app-content-padding-y: 30px;
$app-content-padding-y-mobile: 20px;
$app-content-padding-x: 0;
$app-content-padding-x-mobile: 0;

// Header base
$app-header-base-height: 70px !default;
$app-header-base-height-mobile: 70px !default;
$app-header-base-z-index: null !default;
$app-header-base-z-index-mobile: null !default;
$app-header-base-bg-color: null !default;
$app-header-base-bg-color-mobile: null !default;
$app-header-base-box-shadow: null !default;
$app-header-base-box-shadow-mobile: null !default;
$app-header-base-border-bottom: null !default;
$app-header-base-border-bottom-mobile: null !default;

$app-header-fixed-z-index: 100 !default;
$app-header-fixed-z-index-mobile: 100 !default;

$app-header-minimize-height: 70px !default;
$app-header-minimize-height-mobile: 70px !default;
$app-header-minimize-z-index: 100 !default;
$app-header-minimize-z-index-mobile: 100 !default;
$app-header-minimize-bg-color: null !default;
$app-header-minimize-bg-color-mobile: null !default;
$app-header-minimize-box-shadow: null !default;
$app-header-minimize-box-shadow-mobile: null !default;
$app-header-minimize-border-bottom: null !default;
$app-header-minimize-border-bottom-mobile: null !default;

$app-header-sticky-height: 70px !default;
$app-header-sticky-height-mobile: 70px !default;
$app-header-sticky-z-index: 100 !default;
$app-header-sticky-z-index-mobile: 100 !default;
$app-header-sticky-bg-color: null !default;
$app-header-sticky-bg-color-mobile: null !default;
$app-header-sticky-box-shadow: null !default;
$app-header-sticky-box-shadow-mobile: null !default;
$app-header-sticky-border-bottom: null !default;
$app-header-sticky-border-bottom-mobile: null !default;

// Header primary
$app-header-primary-base-height: 70px !default;
$app-header-primary-base-z-index: null !default;
$app-header-primary-base-z-index-mobile: null !default;
$app-header-primary-base-bg-color: null !default;
$app-header-primary-base-bg-color-mobile: null !default;
$app-header-primary-base-box-shadow: null !default;
$app-header-primary-base-box-shadow-mobile: null !default;
$app-header-primary-base-border-bottom: null !default;
$app-header-primary-base-border-bottom-mobile: null !default;

$app-header-primary-fixed-z-index: 100 !default;

$app-header-primary-minimize-height: 70px !default;
$app-header-primary-minimize-z-index: 100 !default;
$app-header-primary-minimize-bg-color: null !default;
$app-header-primary-minimize-box-shadow: null !default;
$app-header-primary-minimize-border-top: null !default;
$app-header-primary-minimize-border-bottom: null !default;

$app-header-primary-sticky-height: 70px !default;
$app-header-primary-sticky-z-index: 100 !default;
$app-header-primary-sticky-bg-color: null !default;
$app-header-primary-sticky-box-shadow: null !default;
$app-header-primary-sticky-border-top: null !default;
$app-header-primary-sticky-border-bottom: null !default;

// Header secondary
$app-header-secondary-base-height: 70px !default;
$app-header-secondary-base-z-index: null !default;
$app-header-secondary-base-bg-color: null !default;
$app-header-secondary-base-bg-color-mobile: null !default;
$app-header-secondary-base-box-shadow: null !default;
$app-header-secondary-base-box-shadow-mobile: null !default;
$app-header-secondary-base-border-top: null !default;
$app-header-secondary-base-border-bottom: null !default;
$app-header-secondary-base-border-start-mobile: null !default;
$app-header-secondary-base-border-end-mobile: null !default;

$app-header-secondary-fixed-z-index: 100 !default;

$app-header-secondary-minimize-height: 70px !default;
$app-header-secondary-minimize-z-index: 100 !default;
$app-header-secondary-minimize-bg-color: null !default;
$app-header-secondary-minimize-box-shadow: null !default;
$app-header-secondary-minimize-border-top: null !default;
$app-header-secondary-minimize-border-bottom: null !default;

$app-header-secondary-sticky-height: 70px !default;
$app-header-secondary-sticky-z-index: 100 !default;
$app-header-secondary-sticky-bg-color: null !default;
$app-header-secondary-sticky-box-shadow: null !default;
$app-header-secondary-sticky-border-top: null !default;
$app-header-secondary-sticky-border-bottom: null !default;

// Toolbar base
$app-toolbar-base-height: 70px !default;
$app-toolbar-base-height-mobile: 70px !default;
$app-toolbar-base-z-index: null !default;
$app-toolbar-base-z-index-mobile: null !default;
$app-toolbar-base-bg-color: null !default;
$app-toolbar-base-bg-color-mobile: null !default;
$app-toolbar-base-box-shadow: null !default;
$app-toolbar-base-box-shadow-mobile: null !default;
$app-toolbar-base-border-top: null !default;
$app-toolbar-base-border-top-mobile: null !default;
$app-toolbar-base-border-bottom: null !default;
$app-toolbar-base-border-bottom-mobile: null !default;

$app-toolbar-fixed-z-index: 100 !default;
$app-toolbar-fixed-z-index-mobile: 100 !default;

$app-toolbar-minimize-height: 70px !default;
$app-toolbar-minimize-height-mobile: 70px !default;
$app-toolbar-minimize-z-index: 100 !default;
$app-toolbar-minimize-z-index-mobile: 100 !default;
$app-toolbar-minimize-bg-color: null !default;
$app-toolbar-minimize-bg-color-mobile: null !default;
$app-toolbar-minimize-box-shadow: null !default;
$app-toolbar-minimize-box-shadow-mobile: null !default;
$app-toolbar-minimize-border-top: null !default;
$app-toolbar-minimize-border-top-mobile: null !default;
$app-toolbar-minimize-border-bottom: null !default;
$app-toolbar-minimize-border-bottom-mobile: null !default;

$app-toolbar-sticky-height: 70px !default;
$app-toolbar-sticky-height-mobile: 70px !default;
$app-toolbar-sticky-z-index: 100 !default;
$app-toolbar-sticky-z-index-mobile: 100 !default;
$app-toolbar-sticky-bg-color: null !default;
$app-toolbar-sticky-bg-color-mobile: null !default;
$app-toolbar-sticky-box-shadow: null !default;
$app-toolbar-sticky-box-shadow-mobile: null !default;
$app-toolbar-sticky-border-top: null !default;
$app-toolbar-sticky-border-top-mobile: null !default;
$app-toolbar-sticky-border-bottom: null !default;
$app-toolbar-sticky-border-bottom-mobile: null !default;

// Aside base
$app-aside-base-width: 300px !default;
$app-aside-base-width-mobile: 275px !default;
$app-aside-base-z-index: null !default;
$app-aside-base-z-index-mobile: 106 !default;
$app-aside-base-bg-color: null !default;
$app-aside-base-bg-color-mobile: null !default;
$app-aside-base-box-shadow: null !default;
$app-aside-base-box-shadow-mobile: null !default;
$app-aside-base-border-start: null !default;
$app-aside-base-border-start-mobile: null !default;
$app-aside-base-border-end: null !default;
$app-aside-base-border-end-mobile: null !default;
$app-aside-base-gap-start: 0px !default;
$app-aside-base-gap-start-mobile: 0px !default;
$app-aside-base-gap-end: 0px !default;
$app-aside-base-gap-end-mobile: 0px !default;
$app-aside-base-gap-top: 0px !default;
$app-aside-base-gap-top-mobile: 0px !default;
$app-aside-base-gap-bottom: 0px !default;
$app-aside-base-gap-bottom-mobile: 0px !default;

$app-aside-fixed-z-index: 105 !default;

$app-aside-sticky-top: auto !default;
$app-aside-sticky-bottom: auto !default;
$app-aside-sticky-left: auto !default;
$app-aside-sticky-width: 300px !default;
$app-aside-sticky-z-index: 105 !default;
$app-aside-sticky-bg-color: null !default;
$app-aside-sticky-box-shadow: null !default;
$app-aside-sticky-border-start: null !default;
$app-aside-sticky-border-end: null !default;
$app-aside-sticky-gap-start: 0px !default;
$app-aside-sticky-gap-end: 0px !default;
$app-aside-sticky-gap-top: 0px !default;
$app-aside-sticky-gap-bottom: 0px !default;

$app-aside-minimize-width: 75px !default;
$app-aside-minimize-width-mobile: 75px !default;
$app-aside-minimize-bg-color: null !default;
$app-aside-minimize-bg-color-mobile: null !default;
$app-aside-minimize-box-shadow: null !default;
$app-aside-minimize-box-shadow-mobile: null !default;
$app-aside-minimize-hover-box-shadow: null !default;
$app-aside-minimize-hover-box-shadow-mobile: null !default;
$app-aside-minimize-border-start: null !default;
$app-aside-minimize-border-start-mobile: null !default;
$app-aside-minimize-border-end: null !default;
$app-aside-minimize-border-end-mobile: null !default;
$app-aside-minimize-gap-start: 0px !default;
$app-aside-minimize-gap-start-mobile: 0px !default;
$app-aside-minimize-gap-end: 0px !default;
$app-aside-minimize-gap-end-mobile: 0px !default;
$app-aside-minimize-gap-top: 0px !default;
$app-aside-minimize-gap-top-mobile: 0px !default;
$app-aside-minimize-gap-bottom: 0px !default;
$app-aside-minimize-gap-bottom-mobile: 0px !default;

// Aside primary
$app-aside-primary-base-width: 100px !default;
$app-aside-primary-base-width-mobile: 100px !default;
$app-aside-primary-base-z-index: 107 !default;
$app-aside-primary-base-z-index-mobile: 108 !default;
$app-aside-primary-base-bg-color: null !default;
$app-aside-primary-base-bg-color-mobile: null !default;
$app-aside-primary-base-box-shadow: null !default;
$app-aside-primary-base-box-shadow-mobile: null !default;
$app-aside-primary-base-border-start: null !default;
$app-aside-primary-base-border-start-mobile: null !default;
$app-aside-primary-base-border-end: null !default;
$app-aside-primary-base-border-end-mobile: null !default;
$app-aside-primary-base-gap-start: 0px !default;
$app-aside-primary-base-gap-start-mobile: 0px !default;
$app-aside-primary-base-gap-end: 0px !default;
$app-aside-primary-base-gap-end-mobile: 0px !default;
$app-aside-primary-base-gap-top: 0px !default;
$app-aside-primary-base-gap-top-mobile: 0px !default;
$app-aside-primary-base-gap-bottom: 0px !default;
$app-aside-primary-base-gap-bottom-mobile: 0px !default;

$app-aside-primary-minimize-width: 75px !default;
$app-aside-primary-minimize-width-mobile: 75px !default;
$app-aside-primary-minimize-z-index: null !default;
$app-aside-primary-minimize-bg-color: null !default;
$app-aside-primary-minimize-bg-color-mobile: null !default;
$app-aside-primary-minimize-box-shadow: null !default;
$app-aside-primary-minimize-box-shadow-mobile: null !default;
$app-aside-primary-minimize-hover-box-shadow: null !default;
$app-aside-primary-minimize-hover-box-shadow-mobile: null !default;
$app-aside-primary-minimize-border-start: null !default;
$app-aside-primary-minimize-border-start-mobile: null !default;
$app-aside-primary-minimize-border-end: null !default;
$app-aside-primary-minimize-border-end-mobile: null !default;
$app-aside-primary-minimize-gap-start: 0px !default;
$app-aside-primary-minimize-gap-start-mobile: 0px !default;
$app-aside-primary-minimize-gap-end: 0px !default;
$app-aside-primary-minimize-gap-end-mobile: 0px !default;
$app-aside-primary-minimize-gap-top: 0px !default;
$app-aside-primary-minimize-gap-top-mobile: 0px !default;
$app-aside-primary-minimize-gap-bottom: 0px !default;
$app-aside-primary-minimize-gap-bottom-mobile: 0px !default;

// Aside secondary
$app-aside-secondary-base-z-index: 106 !default;
$app-aside-secondary-base-z-index-mobile: 107 !default;
$app-aside-secondary-base-bg-color: null !default;
$app-aside-secondary-base-bg-color-mobile: null !default;
$app-aside-secondary-base-box-shadow: null !default;
$app-aside-secondary-base-box-shadow-mobile: null !default;
$app-aside-secondary-base-border-start: null !default;
$app-aside-secondary-base-border-start-mobile: null !default;
$app-aside-secondary-base-border-end: null !default;
$app-aside-secondary-base-border-end-mobile: null !default;
$app-aside-secondary-base-gap-start: 0px !default;
$app-aside-secondary-base-gap-start-mobile: 0px !default;
$app-aside-secondary-base-gap-end: 0px !default;
$app-aside-secondary-base-gap-end-mobile: 0px !default;
$app-aside-secondary-base-gap-top: 0px !default;
$app-aside-secondary-base-gap-top-mobile: 0px !default;
$app-aside-secondary-base-gap-bottom: 0px !default;
$app-aside-secondary-base-gap-bottom-mobile: 0px !default;

$app-aside-secondary-minimize-width: 75px !default;
$app-aside-secondary-minimize-width-mobile: 75px !default;
$app-aside-secondary-minimize-z-index: null !default;
$app-aside-secondary-minimize-bg-color: null !default;
$app-aside-secondary-minimize-bg-color-mobile: null !default;
$app-aside-secondary-minimize-box-shadow: null !default;
$app-aside-secondary-minimize-box-shadow-mobile: null !default;
$app-aside-secondary-minimize-hover-box-shadow: null !default;
$app-aside-secondary-minimize-hover-box-shadow-mobile: null !default;
$app-aside-secondary-minimize-border-start: null !default;
$app-aside-secondary-minimize-border-start-mobile: null !default;
$app-aside-secondary-minimize-border-end: null !default;
$app-aside-secondary-minimize-border-end-mobile: null !default;
$app-aside-secondary-minimize-gap-start: 0px !default;
$app-aside-secondary-minimize-gap-start-mobile: 0px !default;
$app-aside-secondary-minimize-gap-end: 0px !default;
$app-aside-secondary-minimize-gap-end-mobile: 0px !default;
$app-aside-secondary-minimize-gap-top: 0px !default;
$app-aside-secondary-minimize-gap-top-mobile: 0px !default;
$app-aside-secondary-minimize-gap-bottom: 0px !default;
$app-aside-secondary-minimize-gap-bottom-mobile: 0px !default;

// Aside panel
$app-aside-panel-base-width: 300px !default;
$app-aside-panel-base-width-mobile: 300px !default;
$app-aside-panel-base-z-index: null !default;
$app-aside-panel-base-z-index-mobile: null !default;
$app-aside-panel-base-bg-color: null !default;
$app-aside-panel-base-bg-color-mobile: null !default;
$app-aside-panel-base-box-shadow: null !default;
$app-aside-panel-base-box-shadow-mobile: null !default;
$app-aside-panel-base-border-start: null !default;
$app-aside-panel-base-border-start-mobile: null !default;
$app-aside-panel-base-border-end: null !default;
$app-aside-panel-base-border-end-mobile: null !default;
$app-aside-panel-base-gap-start: 0px !default;
$app-aside-panel-base-gap-start-mobile: 0px !default;
$app-aside-panel-base-gap-end: 0px !default;
$app-aside-panel-base-gap-end-mobile: 0px !default;
$app-aside-panel-base-gap-top: 0px !default;
$app-aside-panel-base-gap-top-mobile: 0px !default;
$app-aside-panel-base-gap-bottom: 0px !default;
$app-aside-panel-base-gap-bottom-mobile: 0px !default;

$app-aside-panel-fixed-z-index: 104 !default;
$app-aside-panel-fixed-z-index-mobile: 105 !default;

$app-aside-panel-sticky-top: auto !default;
$app-aside-panel-sticky-bottom: auto !default;
$app-aside-panel-sticky-width: 300px !default;
$app-aside-panel-sticky-z-index: 104 !default;
$app-aside-panel-sticky-bg-color: null !default;
$app-aside-panel-sticky-box-shadow: null !default;
$app-aside-panel-sticky-border-start: null !default;
$app-aside-panel-sticky-border-end: null !default;
$app-aside-panel-sticky-gap-start: 0px !default;
$app-aside-panel-sticky-gap-start-mobile: 0px !default;
$app-aside-panel-sticky-gap-end: 0px !default;
$app-aside-panel-sticky-gap-end-mobile: 0px !default;
$app-aside-panel-sticky-gap-top: 0px !default;
$app-aside-panel-sticky-gap-top-mobile: 0px !default;
$app-aside-panel-sticky-gap-bottom: 0px !default;
$app-aside-panel-sticky-gap-bottom-mobile: 0px !default;

$app-aside-panel-minimize-width: 75px !default;
$app-aside-panel-minimize-width-mobile: 75px !default;
$app-aside-panel-minimize-bg-color: null !default;
$app-aside-panel-minimize-bg-color-mobile: null !default;
$app-aside-panel-minimize-box-shadow: null !default;
$app-aside-panel-minimize-box-shadow-mobile: null !default;
$app-aside-panel-minimize-hover-box-shadow: null !default;
$app-aside-panel-minimize-hover-box-shadow-mobile: null !default;
$app-aside-panel-minimize-border-start: null !default;
$app-aside-panel-minimize-border-start-mobile: null !default;
$app-aside-panel-minimize-border-end: null !default;
$app-aside-panel-minimize-border-end-mobile: null !default;
$app-aside-panel-minimize-gap-start: 0px !default;
$app-aside-panel-minimize-gap-start-mobile: 0px !default;
$app-aside-panel-minimize-gap-end: 0px !default;
$app-aside-panel-minimize-gap-end-mobile: 0px !default;
$app-aside-panel-minimize-gap-top: 0px !default;
$app-aside-panel-minimize-gap-top-mobile: 0px !default;
$app-aside-panel-minimize-gap-bottom: 0px !default;
$app-aside-panel-minimize-gap-bottom-mobile: 0px !default;

// Page
$app-page-bg-color: null !default;

// Wrapper
$app-wrapper-bg-color: null !default;

// Content

// Footer
$app-footer-height: 70px !default;
$app-footer-height-mobile: 70px !default;
$app-footer-z-index: null !default;
$app-footer-z-index-mobile: null !default;
$app-footer-bg-color: null !default;
$app-footer-bg-color-mobile: null !default;
$app-footer-box-shadow: null !default;
$app-footer-box-shadow-mobile: null !default;
$app-footer-border-top: null !default;
$app-footer-border-top-mobile: null !default;

$app-footer-fixed-z-index: 100 !default;
$app-footer-fixed-z-index-mobile: 100 !default;
