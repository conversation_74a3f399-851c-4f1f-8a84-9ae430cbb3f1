//
// Aside
//

// General mode
.app-toolbar {
  @include app-layout-transition();
  display: flex;
  align-items: stretch;
}

// Desktop mode
@include media-breakpoint-up(lg) {
  // Base
  .app-toolbar {
    @include attr(z-index, $app-toolbar-base-z-index);
    @include attr(background-color, $app-toolbar-base-bg-color);
    @include attr(box-shadow, $app-toolbar-base-box-shadow);
    @include attr(border-top, $app-toolbar-base-border-top);
    @include attr(border-bottom, $app-toolbar-base-border-bottom);
  }

  // Vars
  body {
    --kt-app-toolbar-height: #{$app-toolbar-base-height};
    --kt-app-toolbar-height-actual: #{$app-toolbar-base-height};
  }

  [data-kt-app-toolbar-sticky="on"] {
    --kt-app-aside-height: #{$app-toolbar-sticky-height};
  }

  [data-kt-app-toolbar-minimize="on"] {
    --kt-app-aside-height: #{$app-toolbar-minimize-height};
  }

  // States
  .app-toolbar {
    height: var(--kt-app-toolbar-height);

    [data-kt-app-toolbar-fixed="true"] & {
      @include attr(z-index, $app-toolbar-fixed-z-index);
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
    }

    [data-kt-app-toolbar-sticky="on"] & {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      @include attr(z-index, $app-toolbar-sticky-z-index);
      @include attr(box-shadow, $app-toolbar-sticky-box-shadow);
      @include attr(background-color, $app-toolbar-sticky-bg-color);
      @include attr(border-top, $app-toolbar-sticky-border-top);
      @include attr(border-bottom, $app-toolbar-sticky-border-bottom);
    }

    [data-kt-app-toolbar-minimize="on"] & {
      @include app-layout-transition();
      @include attr(z-index, $app-toolbar-minimize-z-index);
      @include attr(box-shadow, $app-toolbar-minimize-box-shadow);
      @include attr(background-color, $app-toolbar-minimize-bg-color);
      @include attr(border-top, $app-toolbar-minimize-border-top);
      @include attr(border-bottom, $app-toolbar-minimize-border-bottom);
    }
  }

  // Integration
  .app-toolbar {
    // Header
    [data-kt-app-toolbar-fixed="true"][data-kt-app-header-fixed="true"] & {
      top: var(--kt-app-header-height);
    }

    // Aside
    [data-kt-app-toolbar-fixed="true"][data-kt-app-aside-fixed="true"][data-kt-app-aside-push-toolbar="true"]
      & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px)
      );
    }

    // Aside panel
    [data-kt-app-toolbar-fixed="true"][data-kt-app-aside-panel-fixed="true"][data-kt-app-aside-panel-push-toolbar="true"]
      & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px) + var(--kt-app-aside-panel-width) +
          var(--kt-app-aside-panel-gap-start, 0px) +
          var(--kt-app-aside-panel-gap-end, 0px)
      );
    }
  }
}

// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  // Base
  .app-toolbar {
    @include attr(z-index, $app-toolbar-base-z-index-mobile);
    @include attr(background-color, $app-toolbar-base-bg-color-mobile);
    @include attr(box-shadow, $app-toolbar-base-box-shadow-mobile);
    @include attr(border-top, $app-toolbar-base-border-top-mobile);
    @include attr(border-bottom, $app-toolbar-base-border-bottom-mobile);
  }

  // Vars
  body {
    --kt-app-toolbar-height: #{$app-toolbar-base-height-mobile};
  }

  [data-kt-app-toolbar-sticky-mobile="on"] {
    --kt-app-aside-height: #{$app-toolbar-sticky-height-mobile};
  }

  [data-kt-app-toolbar-minimize-mobile="on"] {
    --kt-app-aside-height: #{$app-toolbar-minimize-height-mobile};
  }

  // States
  .app-toolbar {
    height: var(--kt-app-toolbar-height);

    [data-kt-app-toolbar-fixed-mobile="true"] & {
      @include attr(z-index, $app-toolbar-fixed-z-index-mobile);
      position: fixed;
      top: calc(
        var(--kt-app-header-height, 0px) +
          var(--kt-app-header-mobile-height, 0px)
      );
      left: 0;
      right: 0;
    }

    [data-kt-app-toolbar-sticky-mobile="on"] & {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;

      @include attr(z-index, $app-toolbar-sticky-z-index-mobile);
      @include attr(background-color, $app-toolbar-sticky-bg-color-mobile);
      @include attr(box-shadow, $app-toolbar-sticky-box-shadow-mobile);
      @include attr(border-top, $app-toolbar-sticky-border-top-mobile);
      @include attr(border-bottom, $app-toolbar-sticky-border-bottom-mobile);
    }

    [data-kt-app-toolbar-minimize-mobile="on"] & {
      @include app-layout-transition();
      @include attr(z-index, $app-toolbar-minimize-z-index-mobile);
      @include attr(background-color, $app-toolbar-minimize-bg-color-mobile);
      @include attr(box-shadow, $app-toolbar-minimize-box-shadow-mobile);
      @include attr(border-top, $app-toolbar-minimize-border-top-mobile);
      @include attr(border-bottom, $app-toolbar-minimize-border-bottom-mobile);
    }
  }
}
