//
// Header primary
//

// General mode
.app-header-primary {
  @include app-layout-transition();
  display: flex;
  align-items: stretch;
}

// Desktop mode
@include media-breakpoint-up(lg) {
  // Base
  .app-header-primary {
    @include attr(z-index, $app-header-primary-base-z-index);
    @include attr(background-color, $app-header-primary-base-bg-color);
    @include attr(box-shadow, $app-header-primary-base-box-shadow);
    @include attr(border-bottom, $app-header-primary-base-border-bottom);
  }

  // Vars
  body {
    --kt-app-header-primary-height: #{$app-header-primary-base-height};
  }

  [data-kt-app-header-sticky="on"] {
    --kt-app-header-primary-height: #{$app-header-primary-sticky-height};
  }

  [data-kt-app-header-minimize="on"] {
    --kt-app-header-primary-height: #{$app-header-primary-minimize-height};
  }

  [data-kt-app-header-sticky="on"][data-kt-app-header-primary-sticky-hide="true"] {
    --kt-app-header-primary-height: 0;
  }

  // States
  .app-header-primary {
    height: var(--kt-app-header-primary-height);

    [data-kt-app-header-primary-fixed="true"] & {
      @include attr(z-index, $app-header-primary-fixed-z-index);
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
    }

    [data-kt-app-header-primary-static="true"] & {
      position: relative;
    }

    [data-kt-app-header-primary-sticky="on"] & {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      @include attr(height, $app-header-primary-sticky-height);
      @include attr(z-index, $app-header-primary-sticky-z-index);
      @include attr(background-color, $app-header-primary-sticky-bg-color);
      @include attr(box-shadow, $app-header-primary-sticky-box-shadow);
      @include attr(border-bottom, $app-header-primary-sticky-border-bottom);
    }

    [data-kt-app-header-primary-minimize="on"] & {
      @include app-layout-transition();
      @include attr(height, $app-header-primary-minimize-height);
      @include attr(z-index, $app-header-primary-minimize-z-index);
      @include attr(background-color, $app-header-primary-minimize-bg-color);
      @include attr(box-shadow, $app-header-primary-minimize-box-shadow);
      @include attr(border-bottom, $app-header-primary-minimize-border-bottom);
    }

    [data-kt-app-header-sticky="on"][data-kt-app-header-primary-sticky-hide="true"]
      & {
      display: none !important;
    }
  }

  // Integration
  .app-header-primary {
    // Aside
    [data-kt-app-aside-fixed="true"][data-kt-app-aside-push-header="true"] & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px)
      );
    }

    // Aside Panel
    [data-kt-app-aside-panel-fixed="true"][data-kt-app-aside-panel-push-header="true"]
      & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px) + var(--kt-app-aside-panel-width) +
          var(--kt-app-aside-panel-gap-start, 0px) +
          var(--kt-app-aside-panel-gap-end, 0px)
      );
    }
  }
}

// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  // Base
  .app-header-primary {
    flex-grow: 1;
    @include attr(z-index, $app-header-primary-base-z-index-mobile);
    @include attr(background-color, $app-header-primary-base-bg-color-mobile);
    @include attr(box-shadow, $app-header-primary-base-box-shadow-mobile);
    @include attr(border-bottom, $app-header-primary-base-border-bottom-mobile);
  }
}
