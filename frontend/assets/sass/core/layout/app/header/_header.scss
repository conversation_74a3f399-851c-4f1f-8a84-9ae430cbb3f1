//
// Aside
//

// General mode
.app-header {
  @include app-layout-transition();
  display: flex;
  align-items: stretch;
}

// Desktop mode
@include media-breakpoint-up(lg) {
  // Base
  .app-header {
    @include attr(z-index, $app-header-base-z-index);
    @include attr(background-color, $app-header-base-bg-color);
    @include attr(box-shadow, $app-header-base-box-shadow);
    @include attr(border-bottom, $app-header-base-border-bottom);
  }

  // Vars
  body {
    --kt-app-header-height: #{$app-header-base-height};
    --kt-app-header-height-actual: #{$app-header-base-height};
  }

  [data-kt-app-header-sticky="on"] {
    --kt-app-header-height: #{$app-header-sticky-height};
    --kt-app-header-height-actual: #{$app-header-base-height};
  }

  [data-kt-app-header-sticky="on"][data-kt-app-header-stacked="true"] {
    --kt-app-header-height: calc(
      var(--kt-app-header-primary-height, 0px) +
        var(--kt-app-header-secondary-height, 0px)
    );
    --kt-app-header-height-actual: calc(
      #{$app-header-primary-base-height} + #{$app-header-secondary-base-height}
    );
  }

  [data-kt-app-header-minimize="on"] {
    --kt-app-header-height: #{$app-header-minimize-height};
  }

  // States
  .app-header {
    height: var(--kt-app-header-height);

    [data-kt-app-header-fixed="true"] & {
      @include attr(z-index, $app-header-fixed-z-index);
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
    }

    [data-kt-app-header-static="true"] & {
      position: relative;
    }

    [data-kt-app-header-stacked="true"] & {
      flex-direction: column;
      height: calc(
        var(--kt-app-header-primary-height) +
          var(--kt-app-header-secondary-height, 0px)
      );
    }

    [data-kt-app-header-sticky="on"] & {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      @include attr(z-index, $app-header-sticky-z-index);
      @include attr(background-color, $app-header-sticky-bg-color);
      @include attr(box-shadow, $app-header-sticky-box-shadow);
      @include attr(border-bottom, $app-header-sticky-border-bottom);
    }

    [data-kt-app-header-minimize="on"] & {
      @include app-layout-transition();
      @include attr(z-index, $app-header-minimize-z-index);
      @include attr(background-color, $app-header-minimize-bg-color);
      @include attr(box-shadow, $app-header-minimize-box-shadow);
      @include attr(border-bottom, $app-header-minimize-border-bottom);
    }
  }

  // Integration
  .app-header {
    // Aside
    [data-kt-app-aside-fixed="true"][data-kt-app-aside-push-header="true"] & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px)
      );
    }

    // Aside Panel
    [data-kt-app-aside-panel-fixed="true"][data-kt-app-aside-panel-push-header="true"]
      & {
      left: calc(
        var(--kt-app-aside-width) + var(--kt-app-aside-gap-start, 0px) +
          var(--kt-app-aside-gap-end, 0px) + var(--kt-app-aside-panel-width) +
          var(--kt-app-aside-panel-gap-start, 0px) +
          var(--kt-app-aside-panel-gap-end, 0px)
      );
    }
  }
}

// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  // Base
  .app-header {
    @include attr(z-index, $app-header-base-z-index-mobile);
    @include attr(background-color, $app-header-base-bg-color-mobile);
    @include attr(box-shadow, $app-header-base-box-shadow-mobile);
    @include attr(border-bottom, $app-header-base-border-bottom-mobile);
  }

  // Vars
  body {
    --kt-app-header-height: #{$app-header-base-height-mobile};
  }

  [data-kt-app-header-sticky-mobile="on"] {
    --kt-app-header-height: #{$app-header-sticky-height-mobile};
    --kt-app-header-height-actual: #{$app-header-sticky-height-mobile};
  }

  [data-kt-app-header-minimize-mobile="on"] {
    --kt-app-header-height: #{$app-header-minimize-height-mobile};
    --kt-app-header-height-actual: #{$app-header-minimize-height-mobile};
  }

  // States
  .app-header {
    height: var(--kt-app-header-height);
    align-items: stretch;

    .app-header-mobile-drawer {
      display: none;
    }

    [data-kt-app-header-stacked="true"] & {
      flex-direction: column;
    }

    [data-kt-app-header-fixed-mobile="true"] & {
      @include attr(z-index, $app-header-fixed-z-index-mobile);
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      @include app-layout-transition();
    }

    [data-kt-app-header-sticky-mobile="on"] & {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      @include attr(z-index, $app-header-sticky-z-index-mobile);
      @include attr(background-color, $app-header-sticky-bg-color-mobile);
      @include attr(box-shadow, $app-header-sticky-box-shadow-mobile);
      @include attr(border-bottom, $app-header-sticky-border-bottom-mobile);
    }

    [data-kt-app-header-minimize-mobile="on"] & {
      @include app-layout-transition();
      @include attr(z-index, $app-header-minimize-z-index-mobile);
      @include attr(background-color, $app-header-minimize-bg-color-mobile);
      @include attr(box-shadow, $app-header-minimize-box-shadow-mobile);
      @include attr(border-bottom, $app-header-minimize-border-bottom-mobile);
    }
  }
}
