.swal2-container.swal2-center.unlock-candidate-confirm {
    .swal2-popup {
        width: 45em;
        display: grid;
        grid-template-columns: auto auto auto;
        padding: 0;
        .swal2-html-container {
            text-align: justify;
            line-height: inherit;
            padding: 10px;
            max-height: unset;
            .swal2-title {
                text-align: left;
                margin-bottom: 1em;
                padding-left: 0;
            }
        }
        .swal2-image {
            padding: 10px;
        }
        .swal2-actions {
            border-top: 1px solid #d9d9d9;
            grid-column-start: 1;
            grid-column-end: 3;
            justify-content: end;
            width: 100%;
            padding: 10px;
        }
    }
}

.note-item-content {
  display: none;
  :hover {
    display: inline-block;
    .note-item-actions {
      display: inline-block;
    }
  }
}
.modal .modal-footer::before {
  background-color: unset;
}
