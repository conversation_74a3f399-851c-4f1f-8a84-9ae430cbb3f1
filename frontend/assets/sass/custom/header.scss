.header {
  .header-brand {
    background-color: $white;
    justify-content: start;
    gap: 25px;
  }
}

.page-title {
  h1 {
    font-size: 25px;
    color: $topdev-color-3;
  }
}

// Desktop
@include media-breakpoint-up(lg) {
  .header {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    z-index: 102;
  }
}

.locale-switcher {
  display: inline-block;
  width: 30px;
  height: 19px;
}

.color-credits{
  color: #F68407 !important;
  svg,path{
    fill: #F68407;
  }
}

.topdev-background-2{
  color:$topdev-color-2;
}
.limit-max-height{
  max-height: 462px;
  overflow-y: auto;
  overflow-x: hidden;
}


.hover-history-credit:hover a{
  color: #F68407 !important;
}
