.multiselect {
  border-color: $gray-500;
  border-radius: $border-radius;
  .multiselect-tags {
    .multiselect-tag {
      background: #fbfbfb;
      border: 1px solid #cccccc;
      box-sizing: border-box;
      color: #5e6278;
      font-weight: 400;
      max-width: 190px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .multiselect-dropdown {
    z-index: 10000;
    max-height: 20rem;
  }

}
.multiselect.is-active {
  box-shadow: 0 0 $white;
}

.multiselect-option.is-selected {
  background: $topdev-color-4;
  color: $topdev-color-1;
}
.multiselect-option.is-selected.is-pointed {
  background: $topdev-color-4;
  color: $topdev-color-1;
}

.address-tag-content {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 200px;
}

.datepicker-remove {
  position: absolute;
  right: 1rem;
  color: #999;
  z-index: 10;
  cursor: pointer;
  top: 50%;
  transform: translateY(-50%);
}

.container-filter {
  .item-filter {
    .multiselect {
      &.job-id {
        .multiselect-dropdown {
          min-width: 456px;
        }
      }
    }
  }
}
