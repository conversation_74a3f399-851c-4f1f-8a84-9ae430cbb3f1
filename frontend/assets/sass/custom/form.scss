.form-control,
.form-select {
  border-radius: $border-radius;
  padding: 7px 16px;
  font-size: 16px;
  line-height: 23px;
  border-color: $topdev-color-2;
  font-weight: 400;

  &.form-control-solid {
    background-color: $white;
    border-color: $gray-500;
    color: $topdev-color-3;
    transition: $transition-input;

    .dropdown.show > &,
    &:active,
    &.active,
    &:focus,
    &.focus {
      background-color: $white;
      border-color: $primary;
      color: $input-solid-color;
      transition: $transition-input;
    }
    &::placeholder {
      color: #979797;
    }
  }
}

.form-group {
  margin-bottom: 15px;
}

.icon-form-icon {
  transform: translateY(-50%);
  position: absolute;
  margin: 0 10px;
  left: 0;
  top: 50%;
}
.invalid-icon-form-icon {
  top: 27%;
}

.form-select {
  border-radius: 0;
  padding: 0.6rem 3rem 0.6rem 1rem;
  border-color: $gray-500;
}

.form-label {
  font-size: 16px;
  font-weight: 500 !important;
  margin-bottom: 3px;
}

.heading {
  font-size: 13px;
  padding-top: 0px;
  padding-bottom: 0px;
}

.heading-1 {
  font-size: 16px;
}
.sub-title {
  font-size: 16px;
  padding-top: 4px;
}
.input-with-icon {
  position: relative;

  .icon {
    position: absolute;
    top: 18px;
    left: 16px;
    transform: translateY(-50%);
  }

  input {
    padding-left: 42px;
    &.pe-75 {
      padding-right: 75px;
    }
  }

  .right-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);

    .btn {
      font-weight: bold;
      font-size: 17px;

      &.btn-active {
        color: $topdev-color-link;
        .active-icon {
          svg {
            path {
              fill: $topdev-color-link;
            }
          }
        }
      }
    }
  }

  .invalid-icon {
    top: 27%;
  }
}

.was-validated .form-control:invalid,
.form-control.is-invalid,
.multiselect.is-invalid {
  border-color: #ff4d4f;
  &:focus {
    border-color: #ff4d4f;
  }
}

.error-message {
  font-size: 14.8px;
  color: $danger;
  margin-top: 8px;
  width: 100%;
}

.form-control-lg {
  padding: 1rem 1.25rem;
  height: 52px;
}

.form-icon {
  padding-left: 3.5rem;
}

.checkbox-custom {
  position: relative;
  width: 36px;
  height: 20px;
  display: inline-block;

  .checkbox-slider::before {
    position: absolute;
    content: "";
    display: flex;
    align-items: center;
    justify-content: center;
    top: 50%;
    left: 0.25rem;
    transform: translateY(-50%);
    height: 0.75rem;
    width: 0.75rem;
    background-color: $topdev-gray;
    border-radius: 50%;
    transition: all 0.3s linear;
  }

  .checkbox-input {
    width: 0;
    padding: 0;
    height: 0;
    &:checked + .checkbox-slider {
      background: $topdev-primary;
    }

    &:focus + .checkbox-slider {
      box-shadow: none;
      outline: none;
    }

    &:checked + .checkbox-slider:before {
      transform: translateX(1rem) translateY(-50%);
    }
  }

  .checkbox-slider {
    position: absolute;
    cursor: pointer;
    inset: 0;
    background: $topdev-color-2;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }
}
.search-keyword-container {
  display: flex;
  flex: 1;
  min-height: 52px;
  align-items: center;
  position: relative;
  background-color: white;
  border: solid 1px $topdev-gray-300;
  border-radius: 0.25rem;
  padding: 0.25rem 1.75rem;
  padding-left: 2.5rem;
  flex-wrap: wrap;
  min-width: 10rem;
  .search-keyword-icon {
    border-right: solid 2px $topdev-gray-300;
    padding-right: 0.25rem;
  }
  .search-keyword-list {
    display: inline-flex;
    gap: 0.25rem;
    align-items: center;
    flex-wrap: wrap;
  }
  .search-keyword-input {
    border: none;
    padding: 0.25rem 0.5rem;
    width: 100%;
    &:focus {
      outline: none;
    }
  }
  .keyword-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: $topdev-gray-200;
    position: relative;
    border-radius: 4px;
  }
}
