#company-erc {
  display: flex;
  height: 131px;
  padding: var(--spacing-md, 8px) var(--spacing-none, 0px);
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2xl, 24px);
  flex-shrink: 0;

  #company-erc__intro {
    display: flex;
    width: 444px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm, 8px);
    flex-shrink: 0;

    #company-erc__intro-title {
      align-self: stretch;
      margin-bottom: 0;

      color: var(--neutral-900, #3D3D3D);
      font-feature-settings: 'liga' off, 'clig' off;

      span {
        color: var(--Brand-500, #F05C43);
        font-feature-settings: 'liga' off, 'clig' off;
      }
    }

    #company-erc__intro-helper {
      align-self: stretch;
      margin-bottom: 0;

      color: var(--neutral-900, #3D3D3D);
      font-feature-settings: 'liga' off, 'clig' off;
    }
  }

  #company-erc__uploader {
    display: flex;
    height: 118px;
    padding: var(--spacing-xl, 16px) var(--spacing-2xl, 24px);
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    flex: 1 0 0;

    border-radius: var(--spacing-xs, 4px);
    border: 1px dashed var(--Brand-200, #FED2CA);
    background: var(--Brand-100, #FEE6E2);

    #company-erc__uploader-drag-drop-text {
      margin-bottom: 0;
      color: var(--neutral-500, #6D6D6D);
      text-align: center;

      span:first-child {
        color: var(--neutral-950, #292929);
        text-align: center;
        font-feature-settings: 'liga' off, 'clig' off;
      }

      #company-erc__uploader-button {
        color: var(--Brand-600, #DD3F24);
        font-feature-settings: 'liga' off, 'clig' off;
        border: none;
        background: none;
      }
    }
    #company-erc__uploader-helper-text {
      margin-bottom: 0;
      color: var(--neutral-500, #6D6D6D);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
    }
  }
}

#company-erc__uploaded {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--spacing-md, 8px);
  flex: 1 0 0;
}

#company-erc__file {
  display: flex;
  padding: 12px;
  align-items: center;
  gap: 8px;
  align-self: stretch;

  border-radius: 4px;
  background: var(--neutral-50, #F6F6F6);
}

#company-erc__filename {
  color: var(--neutral-900, #3D3D3D);
  font-feature-settings: 'liga' off, 'clig' off;
}

#company-erc__status {
  display: flex;
  padding: var(--spacing-none, 0px) 16px;
  justify-content: space-between;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  height: 31px;

  border-radius: var(--spacing-xs, 4px) var(--spacing-xs, 4px) var(--spacing-none, 4px) var(--spacing-none, 4px);
  background: var(--Brand-300, #FDB1A4);

  #company-erc__statusicon {
    width: 24px;
    height: 24px;
  }

  #company-erc__statustext {
    display: flex;
    padding-left: var(--spacing-md, 8px);
    flex-direction: column;
    justify-content: center;
    flex: 1 0 0;
    align-self: stretch;

    color: var(--neutral-950, #292929);
    font-feature-settings: 'liga' off, 'clig' off;
  }

  &.company-erc__status--active {
    border-radius: var(--spacing-xs, 4px) var(--spacing-xs, 4px) var(--spacing-none, 4px) var(--spacing-none, 4px);
    background: var(--Green-100, #DCFCE8);
  }
}

#company-erc__replacer {
  display: flex;
  height: 28px;
  padding: var(--spacing-none, 0px);
  justify-content: center;
  align-items: center;
  gap: 12px;
  align-self: stretch;

  border-radius: 4px;
  border: 1px dashed var(--Brand-200, #FED2CA);
  background: var(--neutral-White, #FFF);
}

#company-erc__replacer-icon {
  width: 21px;
  height: 21px;
}

#company-erc__replacer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

#company-erc__replacer-content__text {
  display: flex;
  align-items: center;
  gap: 8px;

  button {
    display: flex;
    height: 24px;
    padding: var(--spacing-none, 0px);
    justify-content: center;
    align-items: center;
    gap: var(--spacing-none, 0px);
    border: none;
    background: none;

    color: var(--Brand-600, #DD3F24);
    font-feature-settings: 'liga' off, 'clig' off;
  }
}

#company-erc__replacer-content__support {
  color: var(--neutral-500, #6D6D6D);
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
}

#company-erc__filecontainer {
  display: flex;
  height: 79px;
  padding: var(--spacing-none, 0px);
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}
