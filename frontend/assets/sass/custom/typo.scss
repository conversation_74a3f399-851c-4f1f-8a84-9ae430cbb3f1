h2 {
  font-size: 20px;
}

/* Small/SemiBold */
.text-small-semi-bold {
  font-family: <PERSON>o;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 142.857% */
  letter-spacing: 0.14px;
}

/* Medium/Regular* */
.text-medium-regular {
  font-family: <PERSON><PERSON>;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 137.5% */
  letter-spacing: 0.16px;
}

/* Extra Small/Reg */
.text-small-regular {
  font-family: <PERSON>o;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 150% */
  letter-spacing: 0.048px;
}

/* Extra Large/Bold* */
.text-extra-large-bold {
  font-family: Roboto;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px; /* 140% */
  letter-spacing: 0.03px;
}

/* Small/Reg* */
.text-small-regular {
  font-family: <PERSON>o;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  letter-spacing: 0.14px;
}

/* Medium/Bold* */
.text-medium-bold {
  font-family: Roboto;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 22px; /* 137.5% */
  letter-spacing: 0.16px;
}

/* Linked button/Large */
.linked-button-large {
  font-family: Roboto;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 137.5% */
  letter-spacing: 0.16px;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}
