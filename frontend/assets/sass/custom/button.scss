.btn-reset-custom {
  outline: none;
  background-color: transparent;
  font-size: 16px;
  &:focus {
    outline: none;
  }
}

.btn {
  border-radius: 4px;
}

.btn.btn-primary[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
}

.btn.btn-secondary {
  color: $topdev-color-2;
  font-weight: 500;
  background-color: transparent;
}

.btn.btn-link {
  padding: 0 !important;
  &:hover {
    color: $primary;
  }
}

.btn-topdev-1 {
  background-color: $topdev-color-1;
  color: $white;
  font-weight: bold;
  padding: 6px 16px !important;
  font-size: 17px;
  line-height: 23px;

  &:hover {
    color: $topdev-color-1;
    background-color: $topdev-color-5;
  }
}

.btn-topdev-2 {
  border: 1px solid $topdev-color-2;
  color: $topdev-color-2;
  background-color: #ffffff;
  font-size: 16px;
  font-weight: 400;
  padding: 6px 16px;
  border-radius: $border-radius;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &.btn-negotiable-active {
    color: #ffffff;
    font-weight: bold;
    background-color: #118de5;
    border: 1px solid #118de5;
  }

  &.btn-save-candidate-only-active {
    background-color: #feeeeb;
    color: $topdev-primary;
    border: 1px solid $topdev-primary;
  }

  &.btn-xs {
    font-size: 14px;
    padding: 4px 12px;
  }

  &.btn-outline-active {
    background-color: #ffffff;
    color: $topdev-primary;
    border: 1px solid $topdev-primary;
  }
}

.hover-svg-primary {
  &:hover {
    svg {
      path {
        fill: $topdev-color-link;
      }
    }
  }
}

//button action at list jobs
.btn-topdev-filter {
  background-color: $topdev-color-4;
  border-radius: 25px;
  font-size: 13px;
  padding: 7px 13px;
  font-weight: bolder;
  color: $topdev-color-3;
  border: 1px solid $topdev-color-2;
  white-space: nowrap;
  box-sizing: border-box;
  &.active {
    border: 1px solid $topdev-color-1;
    color: $topdev-color-1;
    background-color: $topdev-color-5;
  }
  &.review {
    border: 1px solid #f68407;
    background-color: #fde6cd;
    color: #f68407;
    padding: 5px 13px;
  }
  &.open {
    border: 1px solid #46bf44;
    color: #46bf44;
    background-color: #daf2da;
    padding: 5px 13px;
  }
  &.closed {
    border: none;
    color: $topdev-color-2;
    background-color: #efefef;
    padding: 5px 13px;
  }
}

.btn-list-action {
  font-size: 13px;
  color: $topdev-color-2;
  padding: 5px 12px;
  border: 1px solid $topdev-color-2;
  margin: 4px 0;
  border-radius: 4px;
  width: 85px;
  font-weight: bold;
  background-color: #ffffff;
  box-sizing: border-box;
  &:hover {
    background-color: $topdev-color-2;
    transition: 0.5s;
    color: #ffffff;
  }
}

.btn-list-action.primary {
  color: #ffffff;
  border: 1px solid $topdev-color-link;
  background-color: $topdev-color-link;
  box-sizing: border-box;
  &:disabled {
    border: 1px solid #efefef;
    background-color: #efefef;
    color: $topdev-color-2;
  }
  &:hover {
    opacity: 0.6;
  }
}

.btn-duplicate-job {
  background-color: #ffffff;
  padding: 8px 12px;
  color: #393e46;
  border-radius: 5px;
  border: 1px solid #ffffff;
  font-size: 13px !important;
  font-weight: 400;
  box-sizing: border-box;
  &:hover {
    background-color: rgba(211, 65, 39, 0.15);
    border: 1px solid #d34127;
    color: #d34127;
  }
}

//button job form
.btn-negotiable {
  padding: 0;
  border: none;
  background: url("/assets/icons/checkbox.svg");
  border-radius: 50%;
  box-sizing: content-box;
  height: 20px;
  width: 20px;
  position: relative;
  &.active {
    background: url("/assets/icons/checkbox-selected.svg");
  }

  &.btn-negotiable-estimate {
    top: 4.5px;
  }
}

.box-salary {
  width: calc(100% - 32px);
}

.btn-currency {
  padding: 8px 24px !important;
  background-color: #cccccc;
  color: #ffffff;
  font-weight: bold;
  border-radius: 4px;
  font-size: 16px;
  border: none;
  &:hover {
    color: #ffffff;
  }
}
.btn-currency.primary {
  background-color: #118de5;
}
.application-button {
  padding: 6px 16px;
  text-transform: uppercase;
  font-size: 17px;
  text-align: center;
  border-radius: 5px;
  font-weight: bold;
  &.ready {
    background-color: #daf2da;
    color: #46bf44;
  }
  &.not-match {
    background-color: #fde6cd;
    color: #f68407;
  }
  &.recalled {
    background-color: #feeeec;
    color: #ea5e2b;
  }
}

.btn-choose-type-of-cv {
  font-size: 14px;
  color: $topdev-gray-500;
  background-color: $topdev-gray-200;
  width: 125px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  &:hover:not(.active) {
    background-color: $topdev-gray-300;
  }
  &.active {
    background-color: white;
    font-weight: 600;
  }
}

.field-svg-btn {
  height: fit-content;
  margin-top: 8px;
}

.mt-2px {
  margin-top: -2px;
}

.btn-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-md {
  height: 3.25rem;
  font-weight: 600;
  line-height: initial;
  padding: 1rem 1.5rem;
}

.btn-outline {
  border: solid 1px $topdev-primary;
  color: $topdev-primary;
  .svg-icon {
    color: $topdev-primary;
  }
}

.btn.btn-danger {
  background-color: $topdev-primary;
}

.btn-action-candidate {
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
  &:hover {
    color: $topdev-primary;
  }
}

.btn-unlocked {
  background-color: $topdev-green-light;
  color: $topdev-green-dark;
  display: inline-block;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  font-size: 1rem;
  border-radius: 0.25rem;
}

.btn-redirect {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  background-color: $topdev-gray-200;
  border-radius: 4px;
  border: 2px solid $topdev-gray-300;
  transition: all 0.3s linear;
  color: $topdev-gray-600;
  &:hover {
    background-color: $topdev-gray-300;
  }
}

.btn-unlock-info {
  font-size: 14px;
  font-weight: 600;
  min-width: 10rem;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  color: $topdev-color-3;
  border-color: $topdev-gray-200;
  border: solid $topdev-primary 1px;
  background-color: $topdev-primary-100;
  color: $topdev-primary;
  border-radius: 2px;
  padding: 6px 1rem;
  outline: none;
  cursor: pointer;
  &:focus {
    outline: none;
  }
  &:disabled {
    background-color: $topdev-gray-300;
    border-color: $topdev-gray-300;
    color: white;
  }
  &.btn-sm {
    height: 2.25rem;
  }
  &.btn-md {
    height: 3.25rem;
  }
}

.btn-unlocked-info {
  @extend .btn-unlock-info;
  background-color: $topdev-green-light;
  color: $topdev-green-dark;
  cursor: auto;
  border: solid $topdev-green-light 1px;
}

.btn-reset-filter {
  &:hover {
    color: $topdev-primary;
  }
}

.btn-send-information {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}

.btn-block {
  width: 100%;
  display: block;
  @extend .line-clamp-1;
}
