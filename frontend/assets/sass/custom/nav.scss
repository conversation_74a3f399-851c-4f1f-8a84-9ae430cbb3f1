.nav-line-tabs .nav-item .nav-link.active,
.nav-line-tabs .nav-item.show .nav-link,
.nav-line-tabs .nav-item .nav-link:hover:not(.disabled) {
  border: none;
}

.nav-line-tabs {
  border: none;
  .nav-item {
    border: none;
    margin: 0;
    .nav-link {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border: none;
      &.active {
        &::after {
          display: block;
          background-color: $topdev-primary;
        }
      }
      &:hover {
        &::after {
          display: block;
          background-color: $topdev-primary;
        }
      }

      &::after {
        content: "";
        transform: translateY(50%);
        position: absolute;
        bottom: 0;
        display: none;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: transparent;
        z-index: 1;
      }
    }
  }
}

.nav-line-tabs .nav-item .nav-link {
  color: $topdev-color-3;
}

// Active & Hover States
.nav-link.active {
  font-weight: 600;
}

// .nav-line-tabs .nav-item .nav-link.active,
// .nav-line-tabs .nav-item.show .nav-link,
// .nav-line-tabs .nav-item .nav-link:hover:not(.disabled) {
//     background-color: transparent;
//     border: 0;
//     border-bottom: 1px solid $topdev-color-1;
//     transition: $transition-link;
//     color: $topdev-color-1;
// }

.nav-tabs {
  list-style: none;
  padding: 0;
  margin: 0;
  .btn-tab {
    position: relative;
    height: 38px;
    display: inline-flex;
    max-width: 12rem;
    width: 100%;
    padding: 0 0.5rem;
    align-items: center;
    justify-content: center;
    outline: none;
    border: none;
    background-color: transparent;
    &:focus {
      outline: none;
    }
    &:hover {
      color: $topdev-primary;
      &::after {
        background-color: $topdev-primary;
      }
    }
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-color: $topdev-gray-600;
      transform: translateY(50%);
      z-index: 1;
    }
    &.active {
      font-weight: 700;
      color: $topdev-primary;
      &::after {
        background-color: $topdev-primary;
        height: 4px;
      }
    }
  }
}
