body {
  background-color: $topdev-gray;
}

// Bg
.bg-topdev-1 {
  background-color: $topdev-color-1;
}
.bg-topdev-4 {
  background-color: $topdev-color-4;
}

// Flex, gap custom
.gap-25px {
  column-gap: 25px;
}

// Form
.benefit-select-icon {
  width: 236px;
}

// Alert
.topdev-new-alert {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 99999999;
  border-radius: 0;
  text-align: center;
  font-size: 13px;
  height: 40px;
  padding: 0;
  line-height: 40px;

  .alert-title{
    a{
      text-decoration: underline;
      color: #664300;
    }
  }

  .btn-close {
    position: absolute;
    right: 10px;
    top: 12px;
    font-size: 15px;
    height: 8px;
    width: 8px;
  }
}

.topdev-alert-show {
  .aside-menu {
    margin-top: 40px;
  }

  #kt_content {
    margin-top: 40px;
  }

  &#kt_header {
    top: 40px;
  }
}

.layout-content {
  min-height: calc(100dvh - 74px - 52px);
  @include media-breakpoint-down(lg){
    min-height: calc(100dvh - 60px - 52px);
  }
}
