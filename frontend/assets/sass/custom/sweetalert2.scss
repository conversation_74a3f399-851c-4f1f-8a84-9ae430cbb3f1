.topdev-confirm .swal2-popup {
  padding-bottom: 10px;
}

.topdev-confirm .swal2-styled.swal2-confirm {
  background-color: $topdev-color-link;
  border-radius: $border-radius;
  height: 35px;
  padding: 6px 24px;
  font-weight: bold;
  font-size: 17px;
  line-height: 23px;
}
.topdev-confirm .swal2-styled.swal2-cancel {
  background-color: unset;
  border-radius: $border-radius;
  height: 35px;
  padding: 6px 24px;
  font-weight: bold;
  font-size: 17px;
  line-height: 23px;
  color: $topdev-color-2;
}
.topdev-confirm .swal2-popup .swal2-title {
  text-align: left;
  font-size: 20px;
  font-weight: bold;
  color: $topdev-color-3;
  padding-left: 25px;
  padding-top: 25px;
}
.topdev-confirm.swal2-container.swal2-center>.swal2-popup {
  border-radius: $border-radius;
  width: 728px;
}
.topdev-confirm.swal2-container .swal2-html-container {
  font-size: 16px;
  text-align: left;

  &:after {
    display: block;
    width: 100%;
    content: "";
    margin-top: 40px;
  }
  a{
    color: $topdev-color-link;
    text-decoration: none;
  }
}
.topdev-confirm .swal2-popup .swal2-actions {
  margin: 0 0 0 auto;
  padding: 5px 20px 0 0;
  border-top: 1px solid $topdev-color-2;
  width: 100%;
  justify-content: flex-end;
}

// Unlock candidate confirm
.unlock-candidate-confirm{
  .swal2-styled.swal2-confirm{
      background-color: $topdev-color-link;
      color: white;
    }
  .swal2-styled.swal2-cancel{
    color: $topdev-color-2;
    background-color: white;
    border: none;
    font-size: 17px;
  }
  .swal2-actions{
    flex-direction: row-reverse;
    font-size: 17px;
  }
  .swal2-popup .swal2-title{
    font-size: 20px;
  }
  .swal2-html-container{
    font-size: 16px;
    p {
      margin-bottom: 8px;
    }
  }
}

// Topdev confirm waiting approved

.topdev-confirm.topdev-confirm-waiting-approved .swal2-styled.swal2-confirm {
  background-color: $topdev-color-1;
  padding: 6px 3rem;
}
.topdev-confirm.topdev-confirm-waiting-approved .swal2-popup .swal2-actions {
  border-top: none;
  justify-content: center;
}
