.usage-history {
    .page-content {
        gap: var(--spacing-lg, 16px);

        .tab {
            gap: var(--spacing-xl, 24px);
        }

        .tab-content {
            .credit-package-header {
                .heading {
                    gap: var(--spacing-md, 8px);
                }
            }
        }
    }
    .heading {
        color: var(--neutral-900, #3D3D3D);
    }
    .description {
        color: var(--neutral-500, #6D6D6D);
    }
    .nav-line-tabs {
        .nav-item {
            .nav-link {
                &.active {
                    color: var(--brand-300-main, #DD3F24);
                }
            }
        }
    }
}