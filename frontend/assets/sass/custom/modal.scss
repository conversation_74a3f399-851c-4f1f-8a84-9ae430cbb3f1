.modal {
  .content-side-banner-modal {
    width: calc(100% - 265px);
    .desc-side-banner-modal {
      max-height: 258px;
      overflow-y: auto;
      padding-right: 10px;
      text-align: justify;
    }
  }

  .image-side-banner-modal {
    img {
      border-radius: 5px;
      width: 100%;
    }
  }

  .modal-dialog {
    max-width: 728px;
  }

  .modal-content {
    border-radius: $border-radius;
  }

  .modal-header {
    padding-top: 24px;
    padding-bottom: 6px;
    border-bottom: none;
  }

  .modal-sub-title {
    font-size: 13px;
  }

  .modal-body {
    padding-top: 0;
  }

  .modal-footer {
    padding-top: 10px;
    padding-bottom: 24px;
    gap: 16px;
    position: relative;
    display: flex;
    justify-content: space-between;
    &::before {
      content: "";
      position: absolute;
      height: 40%;
      width: 1px;
      background-color: #c9c9c9;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%) rotate(15deg);
    }

    & > * {
      margin: 0;
    }
    .contact-now {
      gap: 0.75rem;
      .contact-img {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 9999px;
      }
      .contact-title {
        font-size: 1rem;
        text-transform: uppercase;
        color: #313131;
        font-weight: 700;
        line-height: 1.3;
        letter-spacing: 0.5px;
        margin: 0;
      }
      .contact-number {
        line-height: 1.3;
        font-size: 1.25rem;
        font-weight: 700;
        letter-spacing: 1px;
        color: #313131;
        margin: 0;
      }
    }

    .btn-consultation {
      text-transform: uppercase;
      background-color: #f8e3df;
      color: #d34127;
      border-radius: 100px;
      font-weight: 700;
      font-size: 1rem;
      padding: 0.75rem 1.5rem;
      transition: all 0.2s ease-out;
      box-shadow: 0px 5px 4px 2px rgba(0, 0, 0, 0.15);
      &:hover {
        background-color: #d34127;
        color: white;
      }
    }
  }

  .title-side-banner-modal {
    .btn-close {
      position: absolute;
      right: 4px;
      top: -10px;
      font-size: 0.75rem;
    }
  }
}

.modal {
  .model-footer-filter {
    display: flex;
    justify-content: end;
    gap: 1rem;
    align-items: center;
    padding: 1rem 1.75rem;
    background-color: $topdev-gray;
  }
  .modal-header-filter {
    padding: 0.75rem 1.75rem;
    background-color: $topdev-gray;
    border-bottom: 1px solid $topdev-gray;
  }
}

#modal-select-job-package {
  .modal-content {
    background: none;
    box-shadow: none;
  }

  .modal-dialog {
    max-width: 1280px;
  }

  .price-column__benefits-list {
    padding-left: 0;
  }

  .price-column__benefits-list li {
    background-image: url(https://c.topdevvn.com/uploads/2025/01/15/tick.svg);
    background-repeat: no-repeat;
    padding-left: 20px;
    background-position: 0 3px;
    margin-bottom: 1rem;
  }

  .price-column__benefits-list.crown li {
    background-image: url(https://c.topdevvn.com/uploads/2025/01/15/crown.svg);
  }

}

