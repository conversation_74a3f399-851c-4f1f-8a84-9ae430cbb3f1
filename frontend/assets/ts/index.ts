export * as KTUtil from './_utils/index'
export * as components from './components/index'
// declare global {
//   interface Window {
//     keenthemes: any;
//   }
// }

// window.keenthemes = {
//   components: {
//     ScrollTop: components.ScrollTopComponent,
//     Coockie: components.CookieComponent,
//     Drawer: components.DrawerComponent,
//     Feedback: components.FeedbackComponent,
//     ImageInput: components.ImageInputComponent,
//     Scroll: components.ScrollComponent,
//     Stepper: components.StepperComponent,
//     Sticky: components.StickyComponent,
//     Toggle: components.ToggleComponent,
//     Menu: components.MenuComponent,
//     Search: components.SearchComponent,
//     Dialder: components.DialerComponent,
//     PasswordMeter: components.PasswordMeterComponent,
//     Place: components.PlaceComponent,
//     defaultDialerOptions: components.defaultDialerOptions,
//     defaultDialderQueries: components.defaultDialerQueires,
//     defaultPasswordMeterOptions: components.defaultPasswordMeterOptions,
//     defaultPasswordMeterQueries: components.defaultPasswordMeterQueires,
//     defaultPlaceOptions: components.defaultPlaceOptions,
//     defaultPlaceQueries: components.defaultPlaceQueires,
//     defaultDrawerOptions: components.defaultDrawerOptions,
//     defaultFeedbackOptions: components.defaultFeedbackOptions,
//     defaultImageInputOptions: components.defaultImageInputOptions,
//     defaultScrollOptions: components.defaultScrollOptions,
//     defaultScrollTopOptions: components.defaultScrollTopOptions,
//     defaultStepperOptions: components.defaultStepperOptions,
//     defaultStickyOptions: components.defaultStickyOptions,
//     defaultToggleOptions: components.defaultToggleOptions,
//     defaultMenuOptions: components.defaultMenuOptions,
//     defaultSearchOptions: components.defaultSearchOptions,
//   },
//   utils: KTUtil,
// };
