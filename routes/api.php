<?php

use App\Http\Controllers\Api\Banner\CreateBannerAction;
use App\Http\Controllers\Api\Candidate\ExportCandidateAction;
use App\Http\Controllers\Api\Candidate\GetCandidateAction;
use App\Http\Controllers\Api\Candidate\GetCandidateCVAction;
use App\Http\Controllers\Api\Candidate\GetCandidateCVPreviewAction;
use App\Http\Controllers\Api\Candidate\ShowCandidateAction;
use App\Http\Controllers\Api\Candidate\UpdateCandidateAction;
use App\Http\Controllers\Api\Company\ActiveUnlockPackageAction;
use App\Http\Controllers\Api\Company\GetAvailableCreditAction;
use App\Http\Controllers\Api\Company\GetCompanyInformationAction;
use App\Http\Controllers\Api\Company\GetContactInformationAction;
use App\Http\Controllers\Api\Company\GetCreditHistoryAction;
use App\Http\Controllers\Api\Company\GetEmployerAction;
use App\Http\Controllers\Api\Company\UpdateCompanyInformationAction;
use App\Http\Controllers\Api\Company\UpdateContactInformationAction;
use App\Http\Controllers\Api\Company\UpdateEmployerAction;
use App\Http\Controllers\Api\Dashboard\GetApplicationByStatusAction;
use App\Http\Controllers\Api\Dashboard\GetApplicationScoreCardsAction;
use App\Http\Controllers\Api\Dashboard\GetApplicationTimeSeriesAction;
use App\Http\Controllers\Api\Dashboard\GetPackageBalanceAction;
use App\Http\Controllers\Api\Dashboard\GetPremiumJobByStatusAction;
use App\Http\Controllers\Api\FormValue\GetAllEmployerNameAction;
use App\Http\Controllers\Api\FormValue\GetAllJobTitleAction;
use App\Http\Controllers\Api\FormValue\GetCandidateProvinceAction;
use App\Http\Controllers\Api\FormValue\GetCompanyProvinceAction;
use App\Http\Controllers\Api\Job\CheckRequestingPackageTicketStatusAction;
use App\Http\Controllers\Api\Job\ExportJobAction;
use App\Http\Controllers\Api\Job\GetJobAction;
use App\Http\Controllers\Api\Job\JobGetCategoryAction;
use App\Http\Controllers\Api\Job\JobStoreAction;
use App\Http\Controllers\Api\Job\RequestDesignAction;
use App\Http\Controllers\Api\Job\ShowJobAction;
use App\Http\Controllers\Api\Job\UpdateJobAction;
use App\Http\Controllers\Api\Media\UploadERCAction;
use App\Http\Controllers\Api\Media\UploadMediaAction;
use App\Http\Controllers\Api\MyProduct\CheckFreeJobPostingAvailableAction;
use App\Http\Controllers\Api\MyProduct\CheckJobPostingAvailableAction;
use App\Http\Controllers\Api\MyProduct\GetJobPostingAvailableAction;
use App\Http\Controllers\Api\MyProduct\GetJobPostingHistoryAction;
use App\Http\Controllers\Api\MyProduct\GetFreeJobQuotaAction;
use App\Http\Controllers\Api\MyProduct\RequestFreeJobQuotaAction;
use App\Http\Controllers\Api\MyProduct\RequestUpgradeToPremiumAction;
use App\Http\Controllers\Api\SearchCandidate\CheckCreditBeforeUnlockCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\CheckMaxAllowBeforeUnlockCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\CheckMaxAllowClickCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\ExportSearchCandidatesAction;
use App\Http\Controllers\Api\SearchCandidate\GetSavedSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\GetSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\GetSearchCandidateDownloadCVAction;
use App\Http\Controllers\Api\SearchCandidate\GetSearchCandidateNoteAction;
use App\Http\Controllers\Api\SearchCandidate\GetUnlockedSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\Note\DeleteSearchCandidateNoteAction;
use App\Http\Controllers\Api\SearchCandidate\Note\StoreSearchCandidateNoteAction;
use App\Http\Controllers\Api\SearchCandidate\Note\UpdateSearchCandidateNoteAction;
use App\Http\Controllers\Api\SearchCandidate\Notification\RequestRefundSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\Notification\SendNotificationToGoogleChatAction;
use App\Http\Controllers\Api\SearchCandidate\RandCodeSearchCandidateCVAction;
use App\Http\Controllers\Api\SearchCandidate\RemoveCompanySaveSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\ShowSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\StoreCompanySaveSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\UnlockSearchCandidateAction;
use App\Http\Controllers\Api\SearchCandidate\UnlockSearchCandidateDraftAction;
use App\Http\Controllers\Api\User\ChangeUserPasswordAction;
use App\Http\Controllers\Api\User\GetUserProfileAction;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum', 'role.employer'])->group(function (Router $api) {
    /* Company profile */
    $api->get('/company/profile', GetCompanyInformationAction::class);
    $api->put('/company/profile', UpdateCompanyInformationAction::class);

    /* Contact information */
    $api->get('/company/contact', GetContactInformationAction::class);
    $api->put('/company/contact', UpdateContactInformationAction::class);
    $api->get('/company/credit', GetAvailableCreditAction::class);
    $api->get('/company/credit-history', GetCreditHistoryAction::class);
    $api->post('/company/active-unlock-package', ActiveUnlockPackageAction::class);

    /* Employer information */
    $api->get('/company/employers', GetEmployerAction::class);
    $api->put('/company/employers/{id}', UpdateEmployerAction::class);

    /* Jobs */
    $api->get('/jobs', GetJobAction::class);
    $api->post('/jobs', JobStoreAction::class);
    $api->get('/jobs/categories', JobGetCategoryAction::class);
    $api->get('/jobs/{id}', ShowJobAction::class);
    $api->put('/jobs/{id}', UpdateJobAction::class);
    $api->post('/jobs/export', ExportJobAction::class);
    $api->post('/jobs/request-design', RequestDesignAction::class);
    $api->post('/jobs/preview-url', \App\Http\Controllers\Api\Job\JobGetPreviewUrlAction::class);

    $api->get('/form-values/get-all-job-title', GetAllJobTitleAction::class)->name('api.form-values.job-title');
    $api->get('/form-values/get-all-employer-name', GetAllEmployerNameAction::class)->name('api.form-values.employer-name');
    $api->get('/form-values/get-all-company-province', GetCompanyProvinceAction::class)->name('api.form-values.company-province');
    $api->get('/form-values/get-all-candidates-province', GetCandidateProvinceAction::class)->name('api.form-values.candidates-province');

    /* Media upload */
    $api->post('/media/upload', UploadMediaAction::class);
    $api->post('/erc/upload', UploadERCAction::class);

    /* User */
    $api->get('/user', GetUserProfileAction::class);
    $api->put('/user/password', ChangeUserPasswordAction::class);

    /* Banner */
    $api->post('/banner/add-action', CreateBannerAction::class);

    /* ------------ CANDIDATE ------------ */
    /* Candidate */
    $api->get('/candidates', GetCandidateAction::class);
    $api->get('/candidates/{id}', ShowCandidateAction::class);
    $api->put('/candidates/{id}', UpdateCandidateAction::class);
    $api->post('/candidates/export', ExportCandidateAction::class);

    /* Generate download cv key */
    $api->post('/candidates/{id}/preview-cv', GetCandidateCVPreviewAction::class);
    $api->post('/candidates/{id}/cv', GetCandidateCVAction::class);

    /* ------------ SEARCH CANDIDATE ------------ */
    /* Search candidates */
    $api->get('/search-candidates', GetSearchCandidateAction::class);
    $api->get('/search-candidates/unlocked', GetUnlockedSearchCandidateAction::class);
    $api->get('/search-candidates/saved', GetSavedSearchCandidateAction::class);
    $api->post('/search-candidates/export', ExportSearchCandidatesAction::class);

    /* Search candidate detail */
    $api->get('/search-candidates/max-allow-unlock', CheckMaxAllowBeforeUnlockCandidateAction::class);
    $api->post('/search-candidates/max-click-candidate', CheckMaxAllowClickCandidateAction::class);
    $api->get('/search-candidates/{searchCandidateId}', ShowSearchCandidateAction::class);
    $api->get('/search-candidates/{searchCandidateId}/check-credit', CheckCreditBeforeUnlockCandidateAction::class);
    $api->post('/search-candidates/{searchCandidateId}/unlock', UnlockSearchCandidateAction::class);
    $api->post('/search-candidates/{searchCandidateId}/unlock-draft', UnlockSearchCandidateDraftAction::class);

    $api->post('/search-candidates/{searchCandidateId}/preview-cv', RandCodeSearchCandidateCVAction::class);

    /* Generate download cv key */
    $api->post('/search-candidates/{searchCandidateId}/cv', GetSearchCandidateDownloadCVAction::class);

    /* Note of search candidate */
    $api->get('/search-candidates/{searchCandidateId}/notes', GetSearchCandidateNoteAction::class);
    $api->post('/search-candidates/{searchCandidateId}/notes', StoreSearchCandidateNoteAction::class);
    $api->put('/search-candidates/{searchCandidateId}/notes/{noteId}', UpdateSearchCandidateNoteAction::class);
    $api->delete('/search-candidates/{searchCandidateId}/notes/{nodeId}', DeleteSearchCandidateNoteAction::class);

    /*
     * Company save search candidate
     */
    $api->post('/search-candidates/{searchCandidateId}/save', StoreCompanySaveSearchCandidateAction::class);
    $api->delete('/search-candidates/{searchCandidateId}/remove', RemoveCompanySaveSearchCandidateAction::class);

    /*
     * Send Notification To Google Chat Space
     */
    $api->get('/search-candidates/{searchCandidateId}/send-me-notification', SendNotificationToGoogleChatAction::class);

    /*
     * Request refund & Send Notification To Google Chat Space
     */
    $api->post('/search-candidates/{searchCandidateId}/request-refund', RequestRefundSearchCandidateAction::class);

    /* My Products */
    /* Job Posting */
    $api->get('/company/job-postings/histories', GetJobPostingHistoryAction::class);
    $api->get('/company/job-postings/check-free-job-limit', \App\Http\Controllers\Api\MyProduct\CheckFreeJobLimitAction::class);
    $api->get('/company/job-postings/check-it-quota', \App\Http\Controllers\Api\MyProduct\CheckItJobPostingQuotaAction::class);
    $api->post('/company/job-postings/request-free-quota', RequestFreeJobQuotaAction::class);
    $api->post('/company/job-postings/send-buy-package-notification', RequestUpgradeToPremiumAction::class);
    $api->get('/company/job-postings/check-available-package', CheckJobPostingAvailableAction::class);
    $api->get('/company/job-postings/get-available-packages', GetJobPostingAvailableAction::class);
    $api->get('/company/job-postings/check-is-requesting-package', CheckRequestingPackageTicketStatusAction::class);
    $api->get('/company/job-postings/check-has-free-job-opened', CheckFreeJobPostingAvailableAction::class);
    $api->get('/company/job-postings/free-job-quota', GetFreeJobQuotaAction::class);

    /** Dashboard */
    $api->get('/dashboard/get-application-time-series', GetApplicationTimeSeriesAction::class);
    $api->get('/dashboard/get-application-by-status', GetApplicationByStatusAction::class);
    $api->get('/dashboard/get-application-score-cards', GetApplicationScoreCardsAction::class);
    $api->get('/dashboard/get-premium-job-by-status', GetPremiumJobByStatusAction::class);
    $api->get('/dashboard/get-package-balance', GetPackageBalanceAction::class);
});
