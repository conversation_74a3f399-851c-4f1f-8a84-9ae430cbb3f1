<?php

use App\Http\Controllers\Employer\DashController;
use App\Http\Controllers\Frontend\Candidate\CandidateDownloadCVAction;
use App\Http\Controllers\Frontend\Candidate\CandidateDownloadListAction;
use App\Http\Controllers\Frontend\Candidate\CandidatePreviewCVAction;
use App\Http\Controllers\Frontend\Job\DownloadListJob;
use App\Http\Controllers\Frontend\SearchCandidate\SearchCandidateDownloadCVAction;
use App\Http\Controllers\Frontend\SearchCandidate\DownloadListSearchCandidate;
use App\Http\Controllers\Api\SearchCandidate\GetSearchCandidatePreviewCVAction;
use App\Http\Controllers\LoginController;
use App\Http\Middleware\TrustIp;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/auth/redirect', [LoginController::class, 'redirectToProvider']);
Route::get('/auth/callback', [LoginController::class, 'handleProviderCallback']);
Route::get('/auth/logout', [LoginController::class, 'logout'])->name('auth.logout');
Route::get('/auth/get-token', [LoginController::class, 'getToken'])->name('auth.get-token');

/*
 * Candidate download cv
 */
Route::get('/candidates/{id}/download-cv', CandidateDownloadCVAction::class)
    ->name('candidates.download-cv');
Route::get('/candidates/{id}/preview-cv', CandidatePreviewCVAction::class)
    ->name('candidates.preview-cv');

/* Search candidate download cv */
Route::get('/search-candidates/{id}/download-cv', SearchCandidateDownloadCVAction::class)
    ->name('search-candidates.download-cv');
Route::get('/search-candidates/{searchCandidateId}/preview-cv', GetSearchCandidatePreviewCVAction::class)
    ->name('search-candidates.preview-cv');

/*
 * Export list job
 */
Route::get('/jobs/download', DownloadListJob::class)
    ->name('jobs.download');

/*
 * Export list candidates
 */
Route::get('/candidates/download', CandidateDownloadListAction::class)
    ->name('candidates.download');

/*
 * Export list search candidates
 */
Route::get('/search-candidates/download', DownloadListSearchCandidate::class)
    ->name('search-candidates.download');

/*
 * Private location
 */
Route::get('logs', [Rap2hpoutre\LaravelLogViewer\LogViewerController::class, 'index'])
    ->middleware([TrustIp::class]);

/* health check */
Route::group([
    'prefix' => 'health',
    'middleware' => [],
], function () {
    Route::get('liveness', [\App\Http\Controllers\HealthController::class,  'liveness']);
    Route::get('readiness', [\App\Http\Controllers\HealthController::class,  'readiness']);
    Route::get('phpinfo', [\App\Http\Controllers\HealthController::class,  'phpinfo'])
        ->middleware([TrustIp::class])->name('phpinfo');
});

/*
 * Main application
 */
Route::get('/{any}', [DashController::class, 'index'])
    ->middleware(['auth', 'role.employer'])
    ->where('any', '.*');
