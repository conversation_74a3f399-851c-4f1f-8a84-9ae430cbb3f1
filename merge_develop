#!/bin/bash

ACCEPT_INSTALL=y

# Set colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print error messages
error_exit() {
    echo -e "${RED}Error: $1${NC}" >&2
    exit 1
}

# Function to print success messages
success_msg() {
    echo -e "${GREEN}$1${NC}"
}

# Function to print warning messages
warning_msg() {
    echo -e "${YELLOW}Warning: $1${NC}"
}

# Check if we're in a git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    error_exit "Not a git repository. Please run this script from a git repository."
fi

# Check for uncommitted changes
if ! git diff-index --quiet HEAD --; then
    warning_msg "You have uncommitted changes. Please commit or stash them before merging."
    read -p "Would you like to stash your changes? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git stash save "Auto-stash before merging to develop"
        STASH_SAVED=1
    else
        error_exit "Please commit or stash your changes before merging."
    fi
fi

# Get current branch name
current_branch=$(git branch --show-current)
if [ -z "$current_branch" ]; then
    error_exit "Not currently on any branch (detached HEAD state). Please checkout a branch first."
fi

# Don't allow merging from develop to itself
if [ "$current_branch" = "develop" ]; then
    error_exit "Already on 'develop' branch. Please checkout the feature branch you want to merge."
fi

echo "Current branch: $current_branch"

# Fetch all remote changes
echo "Fetching latest changes from remote..."
git fetch --all --prune || error_exit "Failed to fetch from remote."

# Store the current commit hash for later
original_commit=$(git rev-parse HEAD)

# Checkout develop
echo "Checking out 'develop' branch..."
git checkout develop || error_exit "Failed to checkout 'develop' branch."

# Pull latest develop with rebase
echo "Pulling latest changes for 'develop'..."
git pull --rebase origin develop || {
    warning_msg "Rebase failed. Attempting regular pull..."
    git merge --abort 2>/dev/null
    git reset --hard HEAD
    git pull origin develop || error_exit "Failed to pull latest 'develop'."
}

# Merge the feature branch
echo "Merging '$current_branch' into 'develop'..."
if ! git merge --no-ff -m "Merge branch '$current_branch' into develop" "$current_branch"; then
    warning_msg "Merge conflict detected. Please resolve conflicts and complete the merge manually."
    echo -e "${YELLOW}After resolving conflicts, run:${NC}"
    echo "1. git add ."
    echo "2. git commit -m 'Resolve merge conflicts from $current_branch to develop'"
    echo "3. git push origin develop"
    echo -e "4. git checkout $current_branch"

    # Try to return to original branch
    git merge --abort 2>/dev/null
    git checkout "$current_branch" 2>/dev/null

    # Restore stashed changes if any
    if [ "$STASH_SAVED" = 1 ]; then
        git stash pop
    fi

    exit 1
fi

# Push changes to remote develop
if [[ ! $ACCEPT_INSTALL =~ ^[Yy]$ ]]; then
    read -p "Would you like to push the merge to remote 'develop'? (y/n) " -n 1 -r
    echo
fi
if [[ $ACCEPT_INSTALL =~ ^[Yy]$ || $REPLY =~ ^[Yy]$ ]]; then
    echo "Pushing changes to remote 'develop'..."
    git push origin develop || {
        warning_msg "Push failed. You may need to push manually with 'git push origin develop'."
    }
    success_msg "Successfully merged '$current_branch' into 'develop' and pushed to remote."
else
    warning_msg "Merge completed locally but not pushed to remote. You can push later with 'git push origin develop'."
fi

# Return to original branch
echo "Returning to '$current_branch'..."
git checkout "$current_branch" || error_exit "Failed to return to '$current_branch'."

# Restore stashed changes if any
if [ "$STASH_SAVED" = 1 ]; then
    echo "Restoring stashed changes..."
    git stash pop || warning_msg "Failed to restore stashed changes. Use 'git stash list' and 'git stash apply' to restore them manually."
fi

success_msg "Merge process completed successfully. You are now back on '$current_branch'."
