# Employer Dash v2  Documentation

## Introduction
Employer dash v2 is dashboard for Employer manages their information, manages jobs, create/edit jobs and manages their candidates 

## Getting Started

### Framework and packages
Package we are using:
- `laravel/framework ^8.75`
- `vue ^3.2.37`
- `metronic 8`

### Software requirement 
Make sure you have read & install minimum requirement software
- https://github.com/laravel/docs/blob/8.x/deployment.md#server-requirements
- https://vuejs.org/guide/typescript/overview.html#using-vue-with-typescript

### Installation
This is basic installation steps for you to begin

_Step 1_: Clone repository

```shell
<NAME_EMAIL>:topdev/employer-dash-v2.git
```

_Step 2_: Install composer packages & node packages

```shell
cd employer-dash-v2 
composer install
yarn
```

_Step 3_: Configuration

```shell
cp .env.example .env
php artisan key:generate
```
**Note**: please change `.env` file to your local development environment

### Up & Running
_Step 1_: Compiling assets
```shell
yarn dev
```

_Step 2_: Run integrate server
```shell
php artisan serve
```

Terminal will be
```shell
Starting Laravel development server: http://127.0.0.1:8000
[Wed Jul 13 16:39:15 2022] PHP 7.4.29 Development Server (http://127.0.0.1:8000) started
```

Then you can open the browser `http://127.0.0.1:8000`

**Note**: using different url need to generate new SSO client and config in `.env` file.

### Analyse
