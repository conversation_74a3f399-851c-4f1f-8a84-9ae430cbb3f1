<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'uuid' => $this->faker->uuid(),
            'email' => $this->faker->unique()->safeEmail,
            'username' => $this->faker->userName(),
            'password' => bcrypt('password'),
            'position' => $this->faker->jobTitle,
            'display_name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'full_name' => $this->faker->name,
            'type' => 'employer',
            'approved_at' => now(),
        ];
    }
}
