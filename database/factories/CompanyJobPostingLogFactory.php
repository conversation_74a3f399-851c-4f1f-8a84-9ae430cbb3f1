<?php

namespace Database\Factories;

use App\Models\CompanyJobPostingLog;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyJobPostingLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CompanyJobPostingLog::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'company_id' => $this->faker->randomNumber(9),
            'job_id' => $this->faker->randomNumber(9),
            'job_title' => $this->faker->jobTitle,
            'package_name' => $this->faker->randomElement(['Top Job', 'Distinction Job', 'Basic Plus Job', 'Basic Job'])
        ];
    }
}
