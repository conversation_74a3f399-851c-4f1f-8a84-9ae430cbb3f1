<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBannerActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::connection('mysql_emp')->hasTable('banner_actions')) {
            return;
        }

        Schema::connection('mysql_emp')->create('banner_actions', function (Blueprint $table) {
            $table->id();
            $table->string('banner_id');
            $table->string('action');
            $table->string('username')->nullable();
            $table->timestamp('action_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('mysql_emp')->dropIfExists('banner_actions');
    }
}
