<!DOCTYPE html>
<html>
<head>
  <title>@yield('title', 'Employer Dashboard')</title>
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <link rel="stylesheet" href="{{ mix('emp-v2/style.css') }}">
  @stack('scripts')

  @if (config('ga.enable'))
  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ config('ga.id') }}');</script>
  <!-- End Google Tag Manager -->
  @endif
</head>
<body @if(request()->is('search-candidates*')) data-kt-aside-minimize="on" @endif>
  @if (config('ga.enable'))
  <!-- Google Tag Manager (noscript) -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id={{ config('ga.id') }}"
            height="0" width="0" style="display:none;visibility:hidden"></iframe>
  </noscript>
  <!-- End Google Tag Manager (noscript) -->
  @endif

  @yield('content')

  <script src="{{ mix('emp-v2/manifest.js') }}"></script>
  <script src="{{ mix('emp-v2/vendor.js') }}"></script>
  <script src="{{ mix('emp-v2/app.js') }}"></script>

  @if (app()->environment('production'))
  <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
      var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
      s1.async=true;
      s1.src='https://embed.tawk.to/57b28bf4c621180ddf180a9c/default';
      s1.charset='UTF-8';
      s1.setAttribute('crossorigin','*');
      s0.parentNode.insertBefore(s1,s0);
    })();
  </script>
  @endif
</body>
</html>
