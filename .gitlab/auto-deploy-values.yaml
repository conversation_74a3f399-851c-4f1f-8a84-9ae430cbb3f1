hpa:
  enabled: false
  #  minReplicas: 1
  #  maxReplicas: 3
  # Only one of targetCPUUtilizationPercentage and metrics can be chosen.
  # This is because targetCPUUtilizationPercentage is from autoscaling/v1,
  # whereas metrics is from autoscaling/v2. If both are provided, only
  # metrics will be used.
  # See https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale-walkthrough/
  # for examples of each.
#  targetCPUUtilizationPercentage: 800
#  metrics:
#  - type: Resource
#    resource:
#      name: cpu
#      target:
#        type: Utilization
#        averageUtilization: 800

ingress:
#  annotations:
#    # https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/annotations/
#    nginx.ingress.kubernetes.io/client-body-buffer-size: "10m"
#    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
  path: "/"
  tls:
    enabled: false
  className: nginx
#  canary:
#    weight: 0

livenessProbe:
  enabled: true
  path: "/health/liveness"
readinessProbe:
  enabled: false
  path: "/health/readiness"

#hostAliases:
#- ip: X.X.X.X
#  hostnames:
#  - dns1.DOMAIN1
#  - dns2.DOMAIN2

#resources:
#  requests:
#   cpu: 100m
#   memory: 128Mi

## Configure extra Volumes
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumes: &extraVolumes
  - name: dotenv-secret-volume
    secret:
      secretName: env.secret
  - name: storage
    nfs:
      server: ************
      path: /srv/data/resources
      readOnly: false
# - name: config-volume
#   configMap:
#     name: test-config

## Configure extra Volumes mounts
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
#
extraVolumeMounts: &extraVolumeMounts
  - name: dotenv-secret-volume
    mountPath: /workspace/.env
    subPath: DOTENV_FILE
  - name: storage
    # FILE_RESOURCE_PATH=/srv/assets
    mountPath: /srv/assets
# - name: config-volume
#   mountPath: /app/config.yaml
#   subPath: config.yaml

.worker: &worker
  replicaCount: 1
  command: [ "/cnb/process/schedule" ]
  livenessProbe: &workerLivenessProbe
    command: [ "/bin/sh", "-c", "ps aux | grep -v grep | grep -q '<defunct>' && exit 1 || true" ]
    initialDelaySeconds: 15
    periodSeconds: 60
    timeoutSeconds: 15
    probeType: "exec"
  readinessProbe: *workerLivenessProbe
  extraVolumes: *extraVolumes
  extraVolumeMounts: *extraVolumeMounts

#workers:
#  schedule:
#    <<: *worker
#    command: [ "/cnb/process/schedule" ]
#    livenessProbe: &scheduleLivenessProbe
#      <<: *workerLivenessProbe
#      command: [ "/bin/sh", "-c", "ps -efww | grep -v grep | grep 'schedule:work'" ]
#    readinessProbe: *scheduleLivenessProbe

#  horizon:
#    <<: *worker
#    command: [ "/cnb/process/horizon" ]
#    livenessProbe: &horizonLivenessProbe
#      <<: *workerLivenessProbe
#      command: [ "/cnb/process/horizon-status" ]
#    readinessProbe: *horizonLivenessProbe
#    lifecycle:
#      preStop:
#        exec:
#          command: [ "/cnb/process/horizon-terminate" ]

