## Configure extra Volumes
## ref: https://kubernetes.io/docs/concepts/storage/volumes/
# sed "s/\${CI_ENVIRONMENT_SLUG}/${CI_ENVIRONMENT_SLUG}/g" .gitlab/dotenv-secret-volume.yaml > .gitlab/$CI_ENVIRONMENT_SLUG.env-secret-vol.yaml
extraVolumes: &extraVolumes
  - name: dotenv-secret-volume
    secret:
      secretName: env.secret
  - name: storage
    nfs:
      server: 192.168.1.62
      path: /srv/data/resources
      readOnly: false

.worker: &worker
  extraVolumes: *extraVolumes

#workers:
#  schedule: *worker
#  #  queue: *worker
#  horizon: *worker
